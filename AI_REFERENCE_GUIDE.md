# 🤖 AI Reference Guide - Bamboo Project Documentation

## 📋 **Important Notice for AI Assistants**

When working with the Bamboo project, all documentation files have been organized into dedicated folders for better project structure. This guide helps AI assistants locate the necessary documentation when needed.

## 🗂️ **Documentation Organization**

### **📁 md/** - All Markdown Documentation
All `.md` files have been moved to the `md/` folder:

#### **Project Status & Planning**
- `md/PROJECT_STATUS.md` - Current project status and completion levels
- `md/PROJECT_ORGANIZATION_SUMMARY.md` - File organization summary
- `md/IMPLEMENTATION_ROADMAP_SUMMARY.md` - Development roadmap
- `md/COMPREHENSIVE_IMPLEMENTATION_PLAN.md` - Detailed implementation plan

#### **Installation & Deployment**
- `md/INSTALL.md` - Local installation instructions
- `DEPLOYMENT_SYSTEM_GUIDE.md` - Production deployment guide (root level)
- `DEPLOYMENT_COMPLETE_SUMMARY.md` - Deployment system summary (root level)
- `QUICK_START_DEPLOYMENT.md` - Quick deployment instructions (root level)

#### **Admin Panel Documentation**
- `md/ADMIN_SETUP_COMPLETE.md` - Admin setup completion status
- `md/ADMIN_DASHBOARD_COMPLETION.md` - Admin dashboard features
- `md/ADMIN_DASHBOARD_FIXES.md` - Admin panel fixes applied
- `md/ADMIN_IMPLEMENTATION_SUMMARY.md` - Admin implementation summary

#### **User Application Planning**
- `md/USER_PC_VERSION_PLAN.md` - PC version development plan
- `md/USER_MOBILE_VERSION_PLAN.md` - Mobile version development plan

#### **Technical Documentation**
- `md/DATABASE_ANALYSIS_COMPLETE.md` - Complete database structure analysis
- `md/FINAL_AUDIT_REPORT.md` - Final project audit report
- `md/CSRF_FIX_SUMMARY.md` - CSRF security implementation
- `md/DIAGNOSTIC_TOOLS_SUMMARY.md` - Diagnostic tools overview
- `md/SIDEBAR_CLEANUP_SUMMARY.md` - UI cleanup summary

#### **Original Requirements**
- `md/prompt.md` - Original project requirements
- `md/raw_prompt.md` - Raw project specifications
- `md/Code_and_database_analysis_request__2025-07-02T12-47-16.md` - Analysis request

### **📁 sql/** - All SQL Files
All `.sql` files have been moved to the `sql/` folder:

#### **Main Database Schema**
- `sql/database_migration.sql` - Complete database schema and data

#### **Migration Scripts**
- `sql/add_admin_id_to_transactions.sql` - Transaction table updates
- `sql/add_credit_score_to_users.sql` - User credit score feature
- `sql/add_full_name_to_admin_users.sql` - Admin user enhancements
- `sql/add_payment_channel_to_transactions.sql` - Payment channel support
- `sql/add_wallet_address_to_users.sql` - Wallet address feature
- `sql/create_superiors_table.sql` - Superior management system
- `sql/create_supervisors_table.sql` - Supervisor system

#### **Test Data & Utilities**
- `sql/insert_sample_data.sql` - Sample data for testing
- `sql/insert_sample_data_fixed.sql` - Fixed sample data
- `sql/disable_user_triggers.sql` - Database trigger management

### **📁 test/** - Complete Test Suite
All test files remain in the `test/` folder with comprehensive documentation:
- `test/README_COMPREHENSIVE_TEST_SUITE.md` - Complete testing guide
- 28 test files for database, admin, user, and system testing

## 🎯 **When AI Needs Documentation**

### **For Project Understanding:**
```
Read: md/PROJECT_STATUS.md
Read: md/FINAL_AUDIT_REPORT.md
Read: md/DATABASE_ANALYSIS_COMPLETE.md
```

### **For Installation Help:**
```
Read: md/INSTALL.md (local setup)
Read: DEPLOYMENT_SYSTEM_GUIDE.md (production)
Read: QUICK_START_DEPLOYMENT.md (quick reference)
```

### **For Database Work:**
```
Read: sql/database_migration.sql (main schema)
Read: md/DATABASE_ANALYSIS_COMPLETE.md (structure analysis)
Check: sql/ folder for specific migration scripts
```

### **For Admin Panel Work:**
```
Read: md/ADMIN_IMPLEMENTATION_SUMMARY.md
Read: md/ADMIN_DASHBOARD_COMPLETION.md
Check: admin/ folder for actual implementation
```

### **For User Application Work:**
```
Read: md/USER_PC_VERSION_PLAN.md
Read: md/USER_MOBILE_VERSION_PLAN.md
Check: user/ folder for current implementation
```

### **For Testing:**
```
Read: test/README_COMPREHENSIVE_TEST_SUITE.md
Use: test/ folder files for validation
```

## 🔍 **Quick Reference Commands**

### **Find Project Status:**
```
codebase-retrieval: "current project status and completion levels"
view: md/PROJECT_STATUS.md
```

### **Find Database Information:**
```
codebase-retrieval: "database structure and schema information"
view: sql/database_migration.sql
view: md/DATABASE_ANALYSIS_COMPLETE.md
```

### **Find Installation Instructions:**
```
view: md/INSTALL.md (for local setup)
view: DEPLOYMENT_SYSTEM_GUIDE.md (for production)
```

### **Find Implementation Plans:**
```
view: md/IMPLEMENTATION_ROADMAP_SUMMARY.md
view: md/COMPREHENSIVE_IMPLEMENTATION_PLAN.md
```

## 📊 **File Organization Summary**

- **Total MD files:** 20 files in `md/` folder
- **Total SQL files:** 11 files in `sql/` folder  
- **Total Test files:** 28 files in `test/` folder
- **Deployment files:** 4 files in `install/` folder
- **Production files:** Clean structure in `production/` folder

## 🎋 **Project Context**

**Bamboo** is a sophisticated task-based earning platform with:
- **Technology:** PHP 8+, MySQL, Bootstrap 5, JavaScript
- **Status:** Admin panel 85% complete, User application 5% complete
- **Features:** VIP system, financial management, task assignments
- **Company:** Notepadsly

## 💡 **AI Assistant Tips**

1. **Always check the organized folders** (`md/`, `sql/`, `test/`) for documentation
2. **Use codebase-retrieval** for specific technical information
3. **Reference the deployment system** for production-related questions
4. **Check test files** for understanding current functionality
5. **Remember the file organization** when suggesting file locations

---

**📝 Note:** This organization was completed to prepare the project for online production testing while maintaining clean development structure.
