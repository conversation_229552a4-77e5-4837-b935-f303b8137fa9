# Bamboo Database Structure - Complete Analysis

## Database Overview

**Database Name**: `matchmaking`  
**Character Set**: `utf8mb4_unicode_ci`  
**Engine**: InnoDB (for all tables)  
**Total Tables**: 15 core tables + 2 views + stored procedures + triggers

## Core Tables Structure

### 1. User Management Tables

#### `users` - Main user accounts
```sql
Primary Fields:
- id (PK, AUTO_INCREMENT)
- username (UNIQUE, VARCHAR(50))
- phone (UNIQUE, VARCHAR(20))
- email (UNIQUE, VARCHAR(100))
- password_hash (VARCHAR(255))
- withdrawal_pin_hash (VARCHAR(255))
- gender (ENUM: 'male', 'female')
- invitation_code (UNIQUE, VARCHAR(20))
- invited_by (FK to users.id)

Financial Fields:
- balance (DECIMAL(10,2)) - Current available balance
- commission_balance (DECIMAL(10,2)) - Referral earnings
- frozen_balance (DECIMAL(10,2)) - Temporarily frozen funds
- total_deposited (DECIMAL(10,2)) - Lifetime deposits
- total_withdrawn (DECIMAL(10,2)) - Lifetime withdrawals
- total_commission_earned (DECIMAL(10,2)) - Total referral earnings

VIP & Task Fields:
- vip_level (FK to vip_levels.level)
- tasks_completed_today (INT)
- last_task_date (DATE)

Status & Verification:
- status (ENUM: 'pending', 'active', 'suspended', 'banned')
- email_verified (BOOLEAN)
- phone_verified (BOOLEAN)
- avatar_url (VARCHAR(500))

Activity Tracking:
- referral_count (INT)
- last_login (TIMESTAMP)
- login_count (INT)
- created_at, updated_at (TIMESTAMP)
```

#### `admin_users` - Admin accounts
```sql
- id (PK)
- username (UNIQUE)
- email (UNIQUE)
- password_hash
- role (ENUM: 'super_admin', 'admin', 'moderator')
- permissions (JSON)
- last_login, status, created_at, updated_at
```

#### `user_sessions` - Session management
```sql
- id (VARCHAR(128), PK) - Session ID
- user_id (FK to users.id)
- ip_address (VARCHAR(45))
- user_agent (TEXT)
- payload (LONGTEXT)
- last_activity (INT)
```

### 2. VIP System Tables

#### `vip_levels` - VIP level definitions
```sql
- id (PK)
- level (UNIQUE INT) - VIP level number (1, 2, 3, etc.)
- name (VARCHAR(50)) - Display name (VIP 1, VIP 2, etc.)
- min_balance (DECIMAL(10,2)) - Minimum balance required
- max_daily_tasks (INT) - Daily task limit
- commission_multiplier (DECIMAL(3,2)) - Profit multiplier
- withdrawal_limit_daily (DECIMAL(10,2)) - Daily withdrawal limit
- withdrawal_fee_percentage (DECIMAL(5,2)) - Withdrawal fee
- benefits (TEXT) - VIP level benefits description

Default VIP Levels:
1. VIP 1: $0 min, 5 tasks, 1.00x multiplier
2. VIP 2: $100 min, 10 tasks, 1.20x multiplier
3. VIP 3: $500 min, 15 tasks, 1.50x multiplier
4. VIP 4: $1000 min, 20 tasks, 1.80x multiplier
5. VIP 5: $2500 min, 30 tasks, 2.00x multiplier
```

### 3. Product & Task System Tables

#### `product_categories` - Product categories
```sql
- id (PK)
- name (VARCHAR(100))
- description (TEXT)
- status (ENUM: 'active', 'inactive')
- sort_order (INT)
```

#### `products` - Products for tasks
```sql
- id (PK)
- name (VARCHAR(255))
- description (TEXT)
- image_url (VARCHAR(500))
- price (DECIMAL(10,2)) - Product price
- commission_rate (DECIMAL(5,2)) - Base commission percentage
- category_id (FK to product_categories.id)
- min_vip_level (FK to vip_levels.level) - Minimum VIP required
- max_daily_assignments (INT) - Daily assignment limit
- weight (INT) - Selection probability weight
- stock (INT) - Available stock
- status (ENUM: 'active', 'inactive', 'out_of_stock')
- total_assignments, total_completions (INT)
```

#### `tasks` - User task assignments
```sql
- id (PK)
- user_id (FK to users.id)
- product_id (FK to products.id)
- amount (DECIMAL(10,2)) - Task amount
- commission_earned (DECIMAL(10,2)) - Total commission
- base_commission (DECIMAL(10,2)) - Base commission
- vip_bonus (DECIMAL(10,2)) - VIP bonus amount
- status (ENUM: 'assigned', 'in_progress', 'completed', 'failed', 'expired')
- assigned_at, started_at, completed_at, expires_at (TIMESTAMP)
- submission_data (JSON) - Task submission details
- admin_notes (TEXT)
```

### 4. Financial System Tables

#### `transactions` - All financial transactions
```sql
- id (PK)
- user_id (FK to users.id)
- type (ENUM: 'deposit', 'withdrawal', 'commission', 'bonus', 'referral_bonus', 'penalty', 'adjustment')
- amount (DECIMAL(10,2))
- balance_before, balance_after (DECIMAL(10,2))
- status (ENUM: 'pending', 'processing', 'completed', 'failed', 'cancelled')
- payment_method (VARCHAR(50))
- transaction_id (UNIQUE, VARCHAR(100)) - Internal transaction ID
- external_transaction_id (VARCHAR(255)) - External payment system ID
- fee_amount (DECIMAL(10,2))
- description, admin_notes (TEXT)
- processed_by (FK to admin_users.id)
- processed_at, created_at, updated_at (TIMESTAMP)
```

### 5. Business Logic Tables (Critical)

#### `negative_settings` - Force deposit system
```sql
- id (PK)
- user_id (FK to users.id)
- trigger_task_number (INT) - Which task number triggers (e.g., 2 for 2/45)
- product_id_override (FK to products.id) - Expensive product to show
- override_amount (DECIMAL(15,2)) - Amount that causes negative balance
- is_active (BOOLEAN) - Setting is active
- is_triggered (BOOLEAN) - Has been triggered
- admin_id_created (FK to admin_users.id)
- created_at, updated_at (TIMESTAMP)

Purpose: Forces users into negative balance requiring deposits
```

#### `withdrawal_quotes` - Admin messages to users
```sql
- id (PK)
- user_id (FK to users.id)
- message (TEXT) - Admin message to user
- status (ENUM: 'active', 'resolved')
- admin_id_created (FK to admin_users.id)
- created_at, updated_at (TIMESTAMP)

Purpose: Admin can send messages to users about withdrawals
```

#### `user_salaries` - Salary payments
```sql
- id (PK)
- user_id (FK to users.id)
- amount (DECIMAL(15,2))
- status (ENUM: 'paid', 'pending_approval')
- admin_id_processed (FK to admin_users.id)
- paid_at (TIMESTAMP)
- notes (TEXT)
- created_at, updated_at (TIMESTAMP)
```

### 6. System Configuration Tables

#### `settings` - System settings (legacy)
```sql
- id (PK)
- key (UNIQUE, VARCHAR(100))
- value (TEXT)
- type (ENUM: 'string', 'integer', 'float', 'boolean', 'json')
- description (TEXT)
- category (VARCHAR(50))
- is_public (BOOLEAN)
- updated_by (FK to admin_users.id)
```

#### `app_settings` - Enhanced settings system
```sql
- id (PK)
- setting_key (UNIQUE, VARCHAR(100))
- setting_value (TEXT)
- setting_type (ENUM: 'string', 'integer', 'float', 'boolean', 'json', 'text')
- category (VARCHAR(50))
- description (TEXT)
- is_public (BOOLEAN)
- admin_id_updated (FK to admin_users.id)

Key Settings:
- app_name, app_logo, app_certificate
- company_name, opening_hours, closing_hours
- signup_bonus, min_wallet_balance
- min_withdrawal_amount, max_withdrawal_daily
- usdt_multiplier (for welcome popup)
- contract_terms, about_us, faq_content
- latest_events, user_registration_agreement
- referral commission rates (level1, level2, level3)
- primary_color, secondary_color (appearance)
- maintenance_mode, registration_enabled
```

#### `customer_service_contacts` - Support contacts
```sql
- id (PK)
- name (VARCHAR(255))
- link (VARCHAR(500))
- type (ENUM: 'telegram', 'whatsapp', 'email', 'phone', 'other')
- is_active (BOOLEAN)
- sort_order (INT)
- admin_id_created (FK to admin_users.id)
```

#### `notifications` - System notifications
```sql
- id (PK)
- type (ENUM: 'system', 'user', 'admin', 'banner')
- title (VARCHAR(255))
- message (TEXT)
- target_user_id (FK to users.id) - Specific user or NULL for global
- target_vip_level (INT) - Target VIP level or NULL for all
- is_global, is_popup, is_banner (BOOLEAN)
- banner_color (VARCHAR(7))
- status (ENUM: 'active', 'inactive', 'scheduled')
- start_date, end_date (TIMESTAMP)
- read_count (INT)
- created_by (FK to admin_users.id)
```

## Database Views

### `user_dashboard_view` - User dashboard data
Combines user info with VIP level details and task counts.

### `admin_user_stats` - Admin statistics
Provides aggregated user statistics for admin dashboard.

## Stored Procedures

### `AssignRandomTask(user_id)` - Task assignment logic
1. Validates user can receive tasks (daily limit check)
2. Selects random product based on VIP level and weight
3. Calculates commission based on VIP multiplier
4. Creates task record
5. Updates user task count

### `ProcessTransaction(user_id, type, amount, description)` - Financial transactions
1. Gets current balance
2. Calculates new balance based on transaction type
3. Generates unique transaction ID
4. Creates transaction record
5. Updates user balance

## Database Triggers

### `reset_daily_tasks` - Daily task reset
Automatically resets `tasks_completed_today` when date changes.

### `update_referral_count` - Referral tracking
Updates referrer's count when new user registers.

## Critical Business Flow

### Task Submission Flow
1. User clicks "Start Matching"
2. System checks for negative settings trigger
3. If negative setting exists: shows expensive product, deducts large amount
4. If normal: randomly selects product based on VIP level
5. Deducts product amount from balance
6. User submits task
7. System calculates profit (amount × VIP multiplier)
8. Refunds original amount + profit to user
9. Updates task as completed

### Negative Settings Flow (Revenue Generation)
1. Admin sets negative setting for user at specific task number
2. When user reaches that task, system shows expensive product
3. Product cost exceeds user balance, creating negative balance
4. User must deposit to continue
5. After deposit, user can submit task and get refund + profit
6. This forces deposits and generates revenue

## Database Relationships

**Key Foreign Keys:**
- users.vip_level → vip_levels.level
- users.invited_by → users.id (self-referencing)
- tasks.user_id → users.id
- tasks.product_id → products.id
- transactions.user_id → users.id
- negative_settings.user_id → users.id
- All admin-created records → admin_users.id

## Performance Optimizations

**Indexes Created:**
- All primary and foreign keys
- Composite indexes for common queries
- Status and date-based indexes
- User activity indexes

**Query Optimization:**
- Views for complex dashboard queries
- Stored procedures for business logic
- Proper indexing strategy
- Efficient pagination support

This database structure supports the complete Bamboo business model with sophisticated user management, VIP progression, task assignment, financial tracking, and the critical negative settings system for revenue generation.
