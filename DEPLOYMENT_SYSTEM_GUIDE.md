# 🚀 Bamboo Deployment System - Complete Guide

## 📋 Overview

The Bamboo deployment system provides a comprehensive solution for deploying the Bamboo task management platform to production servers with automated installation, database setup, and configuration management.

## 🗂️ Deployment Structure

```
Bamboo/
├── install/                    # Installation system
│   ├── install.php            # Main installation script
│   ├── database_migration.sql # Database schema
│   ├── production.zip         # Production files (create manually)
│   └── README_PRODUCTION_ZIP.md
├── production/                # Clean production files
│   ├── admin/                 # Admin panel
│   ├── user/                  # User application
│   ├── includes/              # Core PHP files
│   ├── assets/                # CSS, JS, images
│   ├── api/                   # API endpoints
│   ├── uploads/               # File uploads
│   ├── logs/                  # Error logs
│   ├── offline.html           # PWA offline page
│   └── sw.js                  # Service worker
├── test/                      # Testing suite (excluded from production)
├── md/                        # Documentation (excluded from production)
└── sql/                       # SQL files (excluded from production)
```

## 🎯 Installation Features

### 🔧 **Installation Script (install.php)**
- **Step-by-step wizard** with progress tracking
- **System requirements check** (PHP, MySQL, extensions)
- **Database configuration** with connection testing
- **Two installation methods:** Automatic and Manual
- **File verification** and integrity checks
- **Production-ready configuration** setup
- **Security measures** and post-installation cleanup

### 📊 **Installation Steps**
1. **Welcome & Requirements Check** - Verify server compatibility
2. **Database Configuration** - Enter and test database credentials
3. **Installation Method Selection** - Choose automatic or manual
4. **File Extraction & Database Import** - Deploy application files
5. **Verification & Completion** - Confirm successful installation

## 🚀 Quick Deployment Guide

### Step 1: Prepare for Deployment
1. **Create production.zip:**
   ```powershell
   # Windows PowerShell
   cd C:\MAMP\htdocs\Bamboo
   Compress-Archive -Path "production\*" -DestinationPath "install\production.zip"
   ```

2. **Verify files in install/ folder:**
   - `install.php` ✅
   - `database_migration.sql` ✅
   - `production.zip` ✅ (create manually)

### Step 2: Upload to Server
1. Upload the entire `install/` folder to your web server
2. Ensure proper file permissions (755 for directories, 644 for files)
3. Verify your hosting meets requirements:
   - PHP 8.0+
   - MySQL 5.7+
   - PDO MySQL extension
   - File upload capabilities

### Step 3: Run Installation
1. Navigate to: `https://yourdomain.com/install/install.php`
2. Follow the installation wizard:
   - Check system requirements
   - Enter database credentials
   - Choose installation method
   - Complete installation
3. Access admin panel: `https://yourdomain.com/admin/login/`

### Step 4: Post-Installation Security
1. **Delete install folder:** `rm -rf install/`
2. **Change default admin password**
3. **Review security settings**
4. **Configure SSL/HTTPS**

## 🔧 Installation Methods

### 🚀 **Automatic Installation**
- **Best for:** Most users and standard hosting
- **Process:**
  - Automatically extracts production.zip
  - Imports database schema
  - Configures application settings
  - Ready to use immediately
- **Requirements:** ZIP extension enabled

### 🔧 **Manual Installation**
- **Best for:** Advanced users or restricted hosting
- **Process:**
  - Manual extraction of production.zip
  - Guided database import
  - Step-by-step verification
  - More control over the process
- **Requirements:** FTP/File manager access

## 🗄️ Database Configuration

### **Supported Databases:**
- MySQL 5.7+
- MariaDB 10.3+

### **Database Setup:**
1. Create empty database through hosting control panel
2. Note database credentials:
   - Host (usually 'localhost')
   - Database name
   - Username
   - Password
   - Port (usually 3306)

### **Automatic Schema Import:**
The installer automatically imports:
- 15+ core tables (users, admin_users, products, etc.)
- Default data and settings
- Stored procedures and triggers
- Indexes and relationships

## 🔒 Security Features

### **Installation Security:**
- Requirements verification before proceeding
- Database connection validation
- File integrity checks
- Automatic cleanup recommendations

### **Production Security:**
- Debug mode disabled
- Secure configuration defaults
- CSRF protection enabled
- Input validation and sanitization
- Session security measures

### **Post-Installation Security:**
- Default password change prompts
- Install folder deletion warnings
- Security configuration guidance
- SSL/HTTPS recommendations

## 📊 System Requirements

### **Server Requirements:**
- **PHP:** 8.0 or higher
- **Database:** MySQL 5.7+ or MariaDB 10.3+
- **Web Server:** Apache with mod_rewrite
- **Memory:** 128MB minimum
- **Storage:** 100MB minimum

### **PHP Extensions:**
- PDO MySQL
- JSON
- GD (for image processing)
- ZIP (for automatic installation)
- cURL (for external API calls)

### **File Permissions:**
- **Directories:** 755
- **Files:** 644
- **Writable directories:** uploads/, logs/

## 🛠️ Troubleshooting

### **Common Issues:**

#### Installation Fails
- **Check:** System requirements
- **Verify:** Database credentials
- **Ensure:** Proper file permissions
- **Try:** Manual installation method

#### Database Connection Errors
- **Verify:** Database exists and is accessible
- **Check:** Username/password combination
- **Confirm:** Host and port settings
- **Test:** Connection from hosting control panel

#### File Permission Errors
- **Set:** Correct directory permissions (755)
- **Ensure:** Web server can write to uploads/ and logs/
- **Check:** SELinux or similar security policies

#### Missing Files After Installation
- **Verify:** production.zip contains all required files
- **Check:** ZIP extraction completed successfully
- **Ensure:** No file size limits exceeded during upload

## 📞 Support Information

### **Default Admin Credentials:**
- **Username:** admin
- **Password:** admin123
- **⚠️ Change immediately after installation**

### **Important Directories:**
- **Admin Panel:** `/admin/`
- **User Application:** `/user/`
- **API Endpoints:** `/api/`
- **File Uploads:** `/uploads/`
- **Error Logs:** `/logs/`

### **Configuration Files:**
- **Main Config:** `includes/config.php`
- **Database:** `includes/database.php`
- **Functions:** `includes/functions.php`

## 🎉 Post-Installation Checklist

- [ ] Installation completed successfully
- [ ] Admin panel accessible
- [ ] Default admin password changed
- [ ] Install folder deleted
- [ ] SSL/HTTPS configured
- [ ] Error logging enabled
- [ ] File permissions verified
- [ ] Backup system configured
- [ ] Security settings reviewed
- [ ] Application settings configured

## 📈 Next Steps

After successful deployment:
1. **Configure Application Settings** in admin panel
2. **Set up VIP levels** and membership tiers
3. **Add products** for task assignments
4. **Configure payment methods** and financial settings
5. **Test user registration** and task system
6. **Set up monitoring** and maintenance procedures

---

**🎋 Bamboo Deployment System** - Production-ready deployment made simple!
