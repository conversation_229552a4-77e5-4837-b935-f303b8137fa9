# 🚀 Bamboo Quick Start Deployment Guide

## 📋 **Ready to Deploy? Follow These Steps!**

### **Step 1: Create production.zip** ⚡
```powershell
# Windows PowerShell (Run from Bamboo directory)
Compress-Archive -Path "production\*" -DestinationPath "install\production.zip"
```

### **Step 2: Validate Deployment** 🔍
Open in browser:
```
http://localhost/Bamboo/install/validate_deployment.php?validate=deployment
```

### **Step 3: Upload to Server** 📤
Upload the entire `install/` folder to your web server root.

### **Step 4: Run Installation** 🎯
Navigate to:
```
https://yourdomain.com/install/install.php
```

### **Step 5: Complete Setup** ✅
1. Follow the 5-step installation wizard
2. Enter your database credentials
3. Choose automatic installation
4. Access admin panel with default credentials:
   - Username: `admin`
   - Password: `admin123`
5. **IMPORTANT:** Change the default password immediately!

### **Step 6: Security** 🔒
1. Delete the `install/` folder
2. Configure SSL/HTTPS
3. Review security settings

---

## 🎉 **That's It! Your Bamboo Application is Live!**

**Admin Panel:** `https://yourdomain.com/admin/login/`  
**User Application:** `https://yourdomain.com/user/login/`

For detailed instructions, see `DEPLOYMENT_SYSTEM_GUIDE.md`
