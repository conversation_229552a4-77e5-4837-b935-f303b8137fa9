<?php
/**
 * Bamboo Web Application - Admin Header
 * Company: Notepadsly
 * Version: 1.0
 */

// Prevent direct access
if (!defined('BAMBOO_APP')) {
    die('Direct access not permitted');
}

// Get app settings
$app_name = getAppSetting('app_name', APP_NAME);
$app_logo = getAppSetting('app_logo', '');

// Get all appearance settings
$appearance_settings = [
    'primary_color' => getAppSetting('appearance_primary_color', '#ff6900'),
    'secondary_color' => getAppSetting('appearance_secondary_color', '#ffffff'),
    'accent_color' => getAppSetting('appearance_accent_color', '#007bff'),
    'gradient_start' => getAppSetting('appearance_gradient_start', '#ff6900'),
    'gradient_end' => getAppSetting('appearance_gradient_end', '#ff8533'),
    'card_background' => getAppSetting('appearance_card_background', '#ffffff'),
    'sidebar_style' => getAppSetting('appearance_sidebar_style', 'gradient'),
    'sidebar_text_color' => getAppSetting('appearance_sidebar_text_color', '#ffffff'),
    'card_shadow' => getAppSetting('appearance_card_shadow', 'subtle'),
    'border_radius' => getAppSetting('appearance_border_radius', '0.5rem'),
    'button_style' => getAppSetting('appearance_button_style', 'gradient'),
    'theme_mode' => getAppSetting('appearance_theme_mode', 'light'),
    'table_header_color' => getAppSetting('appearance_table_header_color', 'primary'),
    'table_header_text_color' => getAppSetting('appearance_table_header_text_color', '#ffffff'),
    'logo_size' => getAppSetting('appearance_logo_size', 'medium')
];

// Helper function to convert hex to RGB
function hexToRgb($hex) {
    $hex = ltrim($hex, '#');
    return sscanf($hex, "%02x%02x%02x");
}

// Helper function to get shadow value
function getShadowValue($shadowType) {
    switch ($shadowType) {
        case 'none': return 'none';
        case 'subtle': return '0 0.125rem 0.25rem rgba(0, 0, 0, 0.075)';
        case 'medium': return '0 0.5rem 1rem rgba(0, 0, 0, 0.15)';
        case 'strong': return '0 1rem 3rem rgba(0, 0, 0, 0.175)';
        default: return '0 0.125rem 0.25rem rgba(0, 0, 0, 0.075)';
    }
}

// Helper function to get button style
function getButtonStyle($settings) {
    switch ($settings['button_style']) {
        case 'primary':
            return "background: {$settings['primary_color']}";
        case 'accent':
            return "background: {$settings['accent_color']}";
        case 'gradient':
        default:
            return "background: linear-gradient(135deg, {$settings['primary_color']} 0%, {$settings['accent_color']} 100%)";
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <title><?php echo htmlspecialchars($page_title ?? $app_name . ' - Admin'); ?></title>
    
    <!-- Favicon -->
    <?php
    $favicon_path = ASSETS_URL . 'images/favicon.ico';
    $favicon_exists = file_exists(__DIR__ . '/../../assets/images/favicon.ico') && filesize(__DIR__ . '/../../assets/images/favicon.ico') > 100; // Check if it's a real file, not placeholder
    ?>
    <?php if ($favicon_exists): ?>
        <link rel="icon" type="image/x-icon" href="<?php echo $favicon_path; ?>?v=<?php echo filemtime(__DIR__ . '/../../assets/images/favicon.ico'); ?>">
        <link rel="shortcut icon" type="image/x-icon" href="<?php echo $favicon_path; ?>?v=<?php echo filemtime(__DIR__ . '/../../assets/images/favicon.ico'); ?>">
    <?php else: ?>
        <!-- Fallback: Use a data URI for a simple favicon -->
        <link rel="icon" type="image/svg+xml" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32'><rect width='32' height='32' fill='%23ff6900'/><text x='16' y='20' font-family='Arial' font-size='18' fill='white' text-anchor='middle'>B</text></svg>">
    <?php endif; ?>
    
    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    
    <!-- Admin CSS -->
    <link href="<?php echo BASE_URL; ?>admin/assets/css/admin.css" rel="stylesheet">

    <!-- Dynamic Theme System -->
    <link href="<?php echo BASE_URL; ?>admin/assets/css/dynamic-theme.css" rel="stylesheet">

    <!-- Enhanced Tables Styling -->
    <link href="<?php echo BASE_URL; ?>admin/assets/css/enhanced-tables.css" rel="stylesheet">

    <!-- White Theme Override - Ensures all admin pages have white background -->
    <link href="<?php echo BASE_URL; ?>admin/assets/css/admin-white-theme.css" rel="stylesheet">
    
    <!-- Additional CSS files -->
    <?php if (isset($additional_css) && is_array($additional_css)): ?>
        <?php foreach ($additional_css as $css_file): ?>
            <link href="<?php echo $css_file; ?>" rel="stylesheet">
        <?php endforeach; ?>
    <?php endif; ?>
    
    <!-- Dynamic Theme Colors -->
    <style>
        :root {
            /* Primary appearance settings */
            --admin-primary: <?php echo $appearance_settings['primary_color']; ?>;
            --admin-secondary: <?php echo $appearance_settings['secondary_color']; ?>;
            --admin-accent: <?php echo $appearance_settings['accent_color']; ?>;
            --admin-gradient-end: <?php echo $appearance_settings['gradient_end']; ?>;
            --admin-border-radius: <?php echo $appearance_settings['border_radius']; ?>;

            /* RGB values for alpha usage */
            --admin-primary-rgb: <?php echo implode(',', hexToRgb($appearance_settings['primary_color'])); ?>;
            --admin-accent-rgb: <?php echo implode(',', hexToRgb($appearance_settings['accent_color'])); ?>;

            /* Dynamic variables */
            --dynamic-primary: <?php echo $appearance_settings['primary_color']; ?>;
            --dynamic-secondary: <?php echo $appearance_settings['secondary_color']; ?>;
            --dynamic-accent: <?php echo $appearance_settings['accent_color']; ?>;
            --dynamic-gradient-start: <?php echo $appearance_settings['gradient_start']; ?>;
            --dynamic-gradient-end: <?php echo $appearance_settings['gradient_end']; ?>;
            --dynamic-card-bg: <?php echo $appearance_settings['card_background']; ?>;
            --dynamic-border-radius: <?php echo $appearance_settings['border_radius']; ?>;
            --dynamic-primary-rgb: <?php echo implode(',', hexToRgb($appearance_settings['primary_color'])); ?>;
            --dynamic-accent-rgb: <?php echo implode(',', hexToRgb($appearance_settings['accent_color'])); ?>;
            --dynamic-card-shadow: <?php echo getShadowValue($appearance_settings['card_shadow']); ?>;

            /* Table header specific variables */
            <?php
            // Determine table header background color based on setting
            $table_header_bg = '';
            switch ($appearance_settings['table_header_color']) {
                case 'primary':
                    $table_header_bg = $appearance_settings['primary_color'];
                    break;
                case 'accent':
                    $table_header_bg = $appearance_settings['accent_color'];
                    break;
                case 'secondary':
                    $table_header_bg = $appearance_settings['secondary_color'];
                    break;
                case 'gradient':
                    $table_header_bg = 'linear-gradient(135deg, ' . $appearance_settings['gradient_start'] . ', ' . $appearance_settings['gradient_end'] . ')';
                    break;
                default:
                    $table_header_bg = $appearance_settings['primary_color'];
            }
            ?>
            --admin-table-header-bg: <?php echo $table_header_bg; ?>;
            --admin-table-header-text: <?php echo $appearance_settings['table_header_text_color']; ?>;

            /* Logo size variable */
            <?php
            // Determine logo size based on setting
            $logo_size = '';
            switch ($appearance_settings['logo_size']) {
                case 'small':
                    $logo_size = '120px';
                    break;
                case 'medium':
                    $logo_size = '150px';
                    break;
                case 'large':
                    $logo_size = '180px';
                    break;
                default:
                    $logo_size = '150px';
            }
            ?>
            --admin-logo-size: <?php echo $logo_size; ?>;
        }

        /* Apply sidebar style */
        .admin-sidebar {
            <?php if ($appearance_settings['sidebar_style'] === 'gradient'): ?>
                background: linear-gradient(180deg, <?php echo $appearance_settings['gradient_start']; ?> 0%, <?php echo $appearance_settings['gradient_end']; ?> 100%) !important;
                color: <?php echo $appearance_settings['sidebar_text_color']; ?> !important;
            <?php elseif ($appearance_settings['sidebar_style'] === 'solid'): ?>
                background: <?php echo $appearance_settings['primary_color']; ?> !important;
                color: <?php echo $appearance_settings['sidebar_text_color']; ?> !important;
            <?php else: ?>
                background: #f8f9fa !important;
                color: #495057 !important;
            <?php endif; ?>
        }

        /* Apply sidebar text color to all text elements */
        <?php if ($appearance_settings['sidebar_style'] !== 'light'): ?>
        .admin-sidebar .nav-link,
        .admin-sidebar .nav-link i,
        .admin-sidebar .brand-text,
        .admin-sidebar .brand-subtitle,
        .admin-sidebar .sidebar-heading,
        .admin-sidebar .nav-item .nav-link {
            color: <?php echo $appearance_settings['sidebar_text_color']; ?> !important;
        }

        .admin-sidebar .nav-link:hover,
        .admin-sidebar .nav-link.active {
            color: rgba(<?php echo implode(',', hexToRgb($appearance_settings['sidebar_text_color'])); ?>, 0.8) !important;
            background: rgba(<?php echo implode(',', hexToRgb($appearance_settings['sidebar_text_color'])); ?>, 0.1) !important;
        }
        <?php endif; ?>

        /* Apply logo size */
        .brand-logo,
        .brand-logo-svg,
        .brand-logo-svg object,
        .brand-logo-svg img {
            width: var(--admin-logo-size) !important;
            height: var(--admin-logo-size) !important;
            max-width: var(--admin-logo-size) !important;
            max-height: var(--admin-logo-size) !important;
        }

        /* Apply button style */
        .btn-primary {
            <?php echo getButtonStyle($appearance_settings); ?> !important;
            border: none !important;
            color: white !important;
        }

        /* Apply card styling */
        .card {
            background: <?php echo $appearance_settings['card_background']; ?> !important;
            border-radius: <?php echo $appearance_settings['border_radius']; ?> !important;
            box-shadow: <?php echo getShadowValue($appearance_settings['card_shadow']); ?> !important;
        }
    </style>
    
    <!-- Meta tags -->
    <meta name="robots" content="noindex, nofollow">
    <meta name="theme-color" content="<?php echo $appearance_settings['primary_color']; ?>">
    
    <!-- CSRF Token -->
    <meta name="csrf-token" content="<?php echo generateCSRFToken(); ?>">
</head>
<body class="<?php echo $body_class ?? 'admin-page'; ?>">
    
    <!-- Loading Spinner -->
    <div id="admin-loading" class="admin-loading">
        <div class="spinner-border text-primary" role="status">
            <span class="visually-hidden">Loading...</span>
        </div>
    </div>
    
    <!-- Flash Messages -->
    <?php $flash_messages = getFlashMessages(); ?>
    <?php if (!empty($flash_messages)): ?>
        <div class="admin-flash-messages">
            <?php if (isset($flash_messages['success'])): ?>
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <i class="bi bi-check-circle-fill me-2"></i>
                    <?php echo htmlspecialchars($flash_messages['success']); ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>
            
            <?php if (isset($flash_messages['error'])): ?>
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <i class="bi bi-exclamation-triangle-fill me-2"></i>
                    <?php echo htmlspecialchars($flash_messages['error']); ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>
        </div>
    <?php endif; ?>