<?php
/**
 * Bamboo Web Application - View Member
 * Company: Notepadsly
 * Version: 1.1
 */

define('BAMBOO_APP', true);
require_once '../../includes/config.php';
require_once '../../includes/functions.php';

if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

if (!isAdminLoggedIn()) {
    redirect('admin/login/');
}

$user_id = (int)($_GET['id'] ?? 0);
if ($user_id === 0) {
    redirect('admin/member_management/');
}

// Fetch user info
$query = "SELECT u.*, v.name as vip_name FROM users u LEFT JOIN vip_levels v ON u.vip_level = v.level WHERE u.id = ?";
$user = fetchRow($query, [$user_id]);

if (!$user) {
    redirect('admin/member_management/');
}

// Fetch inviter (agent/superior) info from superiors table using invited_by (invitation code)
$inviter = null;
if (!empty($user['invited_by'])) {
    $inviter = fetchRow("SELECT * FROM superiors WHERE invitation_code = ?", [$user['invited_by']]);
}

// Referral count: number of users whose invited_by matches this user's invitation_code
$referral_count = fetchRow("SELECT COUNT(*) as cnt FROM users WHERE invited_by = ?", [$user['invitation_code']]);
$referral_count = $referral_count ? (int)$referral_count['cnt'] : 0;

// Handle POST requests for user management actions
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['csrf_token'])) {
    if (!verifyCSRFToken($_POST['csrf_token'])) {
        showError('Invalid security token. Please try again.');
    } else {
        $action = $_POST['action'] ?? '';

        switch ($action) {
            case 'update_credit_score':
                $score = (int)($_POST['score'] ?? 0);
                if ($score >= 0 && $score <= 100) {
                    if (updateRecord('users', ['credit_score' => $score], 'id = ?', [$user_id])) {
                        showSuccess('Credit score updated successfully!');
                        // Refresh user data
                        $user = fetchRow($query, [$user_id]);
                    } else {
                        showError('Failed to update credit score.');
                    }
                } else {
                    showError('Credit score must be between 0 and 100.');
                }
                break;

            case 'change_login_password':
                $new_password = $_POST['new_password'] ?? '';
                if (!empty($new_password) && strlen($new_password) >= 6) {
                    $hashed_password = password_hash($new_password, PASSWORD_DEFAULT);
                    if (updateRecord('users', ['password' => $hashed_password], 'id = ?', [$user_id])) {
                        showSuccess('Login password updated successfully!');
                    } else {
                        showError('Failed to update login password.');
                    }
                } else {
                    showError('Password must be at least 6 characters long.');
                }
                break;

            case 'change_payment_password':
                $new_payment_password = $_POST['new_payment_password'] ?? '';
                if (!empty($new_payment_password) && strlen($new_payment_password) >= 4) {
                    $hashed_payment_password = password_hash($new_payment_password, PASSWORD_DEFAULT);
                    if (updateRecord('users', ['withdrawal_pin_hash' => $hashed_payment_password], 'id = ?', [$user_id])) {
                        showSuccess('Payment password (withdrawal PIN) updated successfully!');
                    } else {
                        showError('Failed to update payment password.');
                    }
                } else {
                    showError('Payment password must be at least 4 characters long.');
                }
                break;

            case 'adjust_balance':
                $amount = (float)($_POST['amount'] ?? 0);
                $type = $_POST['type'] ?? '';
                if ($amount > 0 && in_array($type, ['addition', 'deduction'])) {
                    $admin_id = $_SESSION['admin_id'] ?? null;
                    $admin_username = $_SESSION['admin_username'] ?? 'Unknown';
                    $order_no = generateOrderNo();
                    $payment_channel = 'Admin Panel';
                    $state = 'completed';

                    $success = adjustUserBalance($user_id, $amount, $type, $admin_id, $order_no, $payment_channel, $admin_username, $state);

                    if ($success) {
                        showSuccess('Balance adjusted successfully!');
                        // Refresh user data
                        $user = fetchRow($query, [$user_id]);
                    } else {
                        showError('Failed to adjust balance.');
                    }
                } else {
                    showError('Invalid amount or type.');
                }
                break;
        }
    }
}

// Get appearance settings for gradient colors
$gradient_start = getAppSetting('appearance_gradient_start', '#ff6900');
$gradient_end = getAppSetting('appearance_gradient_end', '#ff8533');

$page_title = 'View Member';
$additional_css = [BASE_URL . 'admin/assets/css/member-details.css'];
include '../includes/admin_header.php';
?>

<style>
/* Enhanced Wallet Address Copy Button Styling */
.wallet-address-container {
    margin-top: 0.5rem;
}

.wallet-address {
    font-family: 'Courier New', monospace;
    background: rgba(var(--admin-primary-rgb, 255, 105, 0), 0.05);
    padding: 0.5rem 0.75rem;
    border-radius: 0.375rem;
    border: 1px solid rgba(var(--admin-primary-rgb, 255, 105, 0), 0.2);
    font-size: 0.875rem;
    word-break: break-all;
    flex: 1;
}

.copy-btn {
    background: var(--admin-primary, #ff6900) !important;
    border-color: var(--admin-primary, #ff6900) !important;
    color: white !important;
    transition: all 0.3s ease;
    white-space: nowrap;
    min-width: 80px;
}

.copy-btn:hover {
    background: rgba(var(--admin-primary-rgb, 255, 105, 0), 0.8) !important;
    border-color: rgba(var(--admin-primary-rgb, 255, 105, 0), 0.8) !important;
    transform: translateY(-1px);
    box-shadow: 0 0.25rem 0.75rem rgba(var(--admin-primary-rgb, 255, 105, 0), 0.3);
}

.copy-btn.copied {
    background: #28a745 !important;
    border-color: #28a745 !important;
}

.copy-btn i {
    margin-right: 0.25rem;
}
</style>

<div class="admin-wrapper">
    <?php include '../includes/admin_sidebar.php'; ?>
    <div class="admin-main">
        <?php include '../includes/admin_topbar.php'; ?>
        <div class="admin-content">
            <div class="container-fluid">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <div class="d-flex align-items-center">
                        <div class="user-avatar-header me-3">
                            <?php if (!empty($user['avatar'])): ?>
                                <img src="<?php echo BASE_URL . 'uploads/avatars/' . $user['avatar']; ?>" alt="Avatar" class="header-avatar">
                            <?php else: ?>
                                <div class="header-avatar-initials">
                                    <?php echo strtoupper(substr($user['username'], 0, 2)); ?>
                                </div>
                            <?php endif; ?>
                        </div>
                        <div>
                            <h1 class="h3 mb-0">User Details: <?php echo htmlspecialchars($user['username']); ?></h1>
                            <small class="text-muted">Member ID: <?php echo $user['id']; ?> | Status:
                                <span class="badge bg-<?php echo getStatusBadgeClass($user['status']); ?>"><?php echo ucfirst($user['status']); ?></span>
                            </small>
                        </div>
                    </div>
                    <div>
                        <a href="edit.php?id=<?php echo $user_id; ?>" class="btn btn-primary"><i class="bi bi-pencil me-2"></i>Edit User</a>
                        <a href="index.php" class="btn btn-secondary"><i class="bi bi-arrow-left me-2"></i>Back to List</a>
                    </div>
                </div>

                <!-- Quick Actions Section - Prominently Displayed -->
                <div class="row mb-4">
                    <div class="col-12">
                        <div class="card quick-actions-card">
                            <div class="card-header text-white" style="background: linear-gradient(135deg, <?php echo $gradient_start; ?>, <?php echo $gradient_end; ?>);">
                                <h5 class="card-title mb-0">
                                    <i class="bi bi-lightning-charge me-2"></i>Quick Actions
                                </h5>
                                <small class="opacity-75">Manage user account and settings</small>
                            </div>
                            <div class="card-body p-0">
                                <div class="row g-0">
                                    <div class="col-md-3">
                                        <a href="#" class="quick-action-item" data-bs-toggle="modal" data-bs-target="#adjustBalanceModal">
                                            <div class="quick-action-icon bg-success">
                                                <i class="bi bi-plus-circle"></i>
                                            </div>
                                            <div class="quick-action-content">
                                                <div class="quick-action-title">Plus Deduction</div>
                                                <div class="quick-action-desc">Adjust balance</div>
                                            </div>
                                        </a>
                                    </div>
                                    <div class="col-md-3">
                                        <a href="negative_settings.php?id=<?php echo $user_id; ?>" class="quick-action-item">
                                            <div class="quick-action-icon bg-warning">
                                                <i class="bi bi-exclamation-triangle"></i>
                                            </div>
                                            <div class="quick-action-content">
                                                <div class="quick-action-title">Negative Settings</div>
                                                <div class="quick-action-desc">Configure limits</div>
                                            </div>
                                        </a>
                                    </div>
                                    <div class="col-md-3">
                                        <a href="withdraw_quotes.php?id=<?php echo $user_id; ?>" class="quick-action-item">
                                            <div class="quick-action-icon bg-info">
                                                <i class="bi bi-cash-coin"></i>
                                            </div>
                                            <div class="quick-action-content">
                                                <div class="quick-action-title">Withdraw Quote</div>
                                                <div class="quick-action-desc">Manage withdrawals</div>
                                            </div>
                                        </a>
                                    </div>
                                    <div class="col-md-3">
                                        <a href="payment_card.php?id=<?php echo $user_id; ?>" class="quick-action-item">
                                            <div class="quick-action-icon bg-primary">
                                                <i class="bi bi-credit-card"></i>
                                            </div>
                                            <div class="quick-action-content">
                                                <div class="quick-action-title">Payment Card</div>
                                                <div class="quick-action-desc">Card management</div>
                                            </div>
                                        </a>
                                    </div>
                                </div>
                                <div class="row g-0">
                                    <div class="col-md-3">
                                        <a href="edit.php?id=<?php echo $user_id; ?>" class="quick-action-item">
                                            <div class="quick-action-icon bg-secondary">
                                                <i class="bi bi-person-gear"></i>
                                            </div>
                                            <div class="quick-action-content">
                                                <div class="quick-action-title">Basic Information</div>
                                                <div class="quick-action-desc">Edit profile</div>
                                            </div>
                                        </a>
                                    </div>
                                    <div class="col-md-3">
                                        <a href="#" class="quick-action-item" data-bs-toggle="modal" data-bs-target="#creditScoreModal">
                                            <div class="quick-action-icon bg-dark">
                                                <i class="bi bi-graph-up"></i>
                                            </div>
                                            <div class="quick-action-content">
                                                <div class="quick-action-title">Credit Score</div>
                                                <div class="quick-action-desc">Manage rating</div>
                                            </div>
                                        </a>
                                    </div>
                                    <div class="col-md-3">
                                        <a href="salary.php?id=<?php echo $user_id; ?>" class="quick-action-item">
                                            <div class="quick-action-icon bg-success">
                                                <i class="bi bi-currency-dollar"></i>
                                            </div>
                                            <div class="quick-action-content">
                                                <div class="quick-action-title">Salary</div>
                                                <div class="quick-action-desc">Salary management</div>
                                            </div>
                                        </a>
                                    </div>
                                    <div class="col-md-3">
                                        <a href="reset_task.php?id=<?php echo $user_id; ?>" class="quick-action-item">
                                            <div class="quick-action-icon bg-warning">
                                                <i class="bi bi-arrow-clockwise"></i>
                                            </div>
                                            <div class="quick-action-content">
                                                <div class="quick-action-title">Reset Task</div>
                                                <div class="quick-action-desc">Task management</div>
                                            </div>
                                        </a>
                                    </div>
                                </div>
                                <div class="row g-0">
                                    <div class="col-md-3">
                                        <a href="#" class="quick-action-item" data-bs-toggle="modal" data-bs-target="#changePasswordModal">
                                            <div class="quick-action-icon bg-info">
                                                <i class="bi bi-key"></i>
                                            </div>
                                            <div class="quick-action-content">
                                                <div class="quick-action-title">Login Password</div>
                                                <div class="quick-action-desc">Change password</div>
                                            </div>
                                        </a>
                                    </div>
                                    <div class="col-md-3">
                                        <a href="#" class="quick-action-item" data-bs-toggle="modal" data-bs-target="#changePaymentPasswordModal">
                                            <div class="quick-action-icon bg-primary">
                                                <i class="bi bi-shield-lock"></i>
                                            </div>
                                            <div class="quick-action-content">
                                                <div class="quick-action-title">Payment Password</div>
                                                <div class="quick-action-desc">Security settings</div>
                                            </div>
                                        </a>
                                    </div>
                                    <div class="col-md-3">
                                        <a href="my_team.php?id=<?php echo $user_id; ?>" class="quick-action-item">
                                            <div class="quick-action-icon bg-secondary">
                                                <i class="bi bi-people"></i>
                                            </div>
                                            <div class="quick-action-content">
                                                <div class="quick-action-title">My Team</div>
                                                <div class="quick-action-desc">Team overview</div>
                                            </div>
                                        </a>
                                    </div>
                                    <div class="col-md-3">
                                        <a href="delete.php?id=<?php echo $user_id; ?>" class="quick-action-item danger">
                                            <div class="quick-action-icon bg-danger">
                                                <i class="bi bi-trash"></i>
                                            </div>
                                            <div class="quick-action-content">
                                                <div class="quick-action-title">Delete Account</div>
                                                <div class="quick-action-desc">Permanent removal</div>
                                            </div>
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-lg-8">
                        <div class="card mb-4">
                            <div class="card-header">
                                <h5 class="card-title mb-0">
                                    <i class="bi bi-person-circle me-2"></i>Account Information
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="info-row">
                                    <span class="info-label">Username:</span>
                                    <span class="info-value"><?php echo htmlspecialchars($user['username']); ?></span>
                                </div>
                                <div class="info-row">
                                    <span class="info-label">Email:</span>
                                    <span class="info-value"><?php echo htmlspecialchars($user['email']); ?></span>
                                </div>
                                <div class="info-row">
                                    <span class="info-label">Phone:</span>
                                    <span class="info-value"><?php echo htmlspecialchars($user['phone']); ?></span>
                                </div>
                                <div class="info-row">
                                    <span class="info-label">Gender:</span>
                                    <span class="info-value"><?php echo ucfirst($user['gender']); ?></span>
                                </div>
                                <div class="info-row">
                                    <span class="info-label">Status:</span>
                                    <span class="info-value"><span class="badge bg-<?php echo getStatusBadgeClass($user['status']); ?>"><?php echo ucfirst($user['status']); ?></span></span>
                                </div>
                            </div>
                        </div>

                        <div class="card mb-4">
                            <div class="card-header">
                                <h5 class="card-title mb-0">
                                    <i class="bi bi-wallet2 me-2"></i>Financial Overview
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="financial-item">
                                            <div class="financial-label">Main Balance</div>
                                            <div class="financial-value text-success fw-bold"><?php echo formatCurrency($user['balance']); ?></div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="financial-item">
                                            <div class="financial-label">Commission Balance</div>
                                            <div class="financial-value text-success fw-bold"><?php echo formatCurrency($user['commission_balance']); ?></div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="financial-item">
                                            <div class="financial-label">Frozen Balance</div>
                                            <div class="financial-value text-warning fw-bold"><?php echo formatCurrency($user['frozen_balance']); ?></div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="financial-item">
                                            <div class="financial-label">Total Profit</div>
                                            <div class="financial-value text-success fw-bold"><?php echo formatCurrency($user['total_profit'] ?? 0); ?></div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="financial-item">
                                            <div class="financial-label">Total Deposited</div>
                                            <div class="financial-value text-success fw-bold"><?php echo formatCurrency($user['total_deposited']); ?></div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="financial-item">
                                            <div class="financial-label">Total Withdrawn</div>
                                            <div class="financial-value text-success fw-bold"><?php echo formatCurrency($user['total_withdrawn']); ?></div>
                                        </div>
                                    </div>
                                </div>

                                <hr class="my-4">

                                <div class="info-row">
                                    <span class="info-label">USDT Wallet Address:</span>
                                    <div class="wallet-address-container">
                                        <?php if (!empty($user['usdt_wallet_address'])): ?>
                                            <div class="d-flex align-items-center gap-2">
                                                <span class="wallet-address" id="walletAddress"><?php echo htmlspecialchars($user['usdt_wallet_address']); ?></span>
                                                <button type="button" class="btn btn-sm btn-primary copy-btn" onclick="copyWalletAddress()" title="Copy to clipboard">
                                                    <i class="bi bi-copy"></i> Copy
                                                </button>
                                            </div>
                                        <?php else: ?>
                                            <span class="text-muted">N/A</span>
                                        <?php endif; ?>
                                    </div>
                                </div>
                                <div class="info-row">
                                    <span class="info-label">Exchange Name:</span>
                                    <span class="info-value"><?php echo htmlspecialchars($user['exchange_name'] ?? 'N/A'); ?></span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-4">
                        <div class="card mb-4">
                            <div class="card-header">
                                <h5 class="card-title mb-0">VIP & Referral</h5>
                            </div>
                            <div class="card-body">
                                <div class="info-group">
                                    <span class="info-label">VIP Level:</span>
                                    <span class="info-value badge bg-info fs-6"><?php echo htmlspecialchars($user['vip_name'] ?? 'N/A'); ?></span>
                                </div>
                                <div class="info-group">
                                    <span class="info-label">Invitation Code:</span>
                                    <span class="info-value"><?php echo htmlspecialchars($user['invitation_code']); ?></span>
                                </div>
                                <div class="info-group">
                                    <span class="info-label">Invited By (Agent Name):</span>
                                    <span class="info-value"><?php echo $inviter ? htmlspecialchars($inviter['name']) : 'N/A'; ?></span>
                                </div>
                                <div class="info-group">
                                    <span class="info-label">Referral Count:</span>
                                    <span class="info-value"><?php echo $referral_count; ?></span>
                                </div>
                            </div>
                        </div>
                        <div class="card">
                            <div class="card-header">
                                <h5 class="card-title mb-0">Activity</h5>
                            </div>
                            <div class="card-body">
                                <div class="info-group">
                                    <span class="info-label">Registration Date:</span>
                                    <small class="info-value"><?php echo formatDate($user['created_at']); ?></small>
                                </div>
                                <div class="info-group">
                                    <span class="info-label">Last Login:</span>
                                    <small class="info-value"><?php echo $user['last_login'] ? formatDate($user['last_login']) : 'Never'; ?></small>
                                </div>
                                <div class="info-group">
                                    <span class="info-label">Tasks Completed Today:</span>
                                    <small class="info-value"><?php echo $user['tasks_completed_today']; ?></small>
                                </div>
                            </div>
                        </div>

                    </div>
                </div>
            </div>
        </div>
        <?php include '../includes/admin_footer.php'; ?>
    </div>
</div>

<script>
function copyWalletAddress() {
    const walletAddress = document.getElementById('walletAddress').textContent;
    const copyBtn = document.querySelector('.copy-btn');

    navigator.clipboard.writeText(walletAddress).then(function() {
        // Success feedback
        const originalIcon = copyBtn.innerHTML;
        copyBtn.innerHTML = '<i class="bi bi-check"></i>';
        copyBtn.classList.add('copied');
        copyBtn.title = 'Copied!';

        setTimeout(function() {
            copyBtn.innerHTML = originalIcon;
            copyBtn.classList.remove('copied');
            copyBtn.title = 'Copy to clipboard';
        }, 2000);
    }).catch(function(err) {
        // Fallback for older browsers
        const textArea = document.createElement('textarea');
        textArea.value = walletAddress;
        document.body.appendChild(textArea);
        textArea.select();
        document.execCommand('copy');
        document.body.removeChild(textArea);

        // Success feedback
        const originalIcon = copyBtn.innerHTML;
        copyBtn.innerHTML = '<i class="bi bi-check"></i>';
        copyBtn.classList.add('copied');
        copyBtn.title = 'Copied!';

        setTimeout(function() {
            copyBtn.innerHTML = originalIcon;
            copyBtn.classList.remove('copied');
            copyBtn.title = 'Copy to clipboard';
        }, 2000);
    });
}
</script>

<!-- Credit Score Modal -->
<div class="modal fade" id="creditScoreModal" tabindex="-1" aria-labelledby="creditScoreModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="creditScoreModalLabel">Update Credit Score</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form action="view.php?id=<?php echo $user_id; ?>" method="POST">
                <div class="modal-body">
                    <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                    <input type="hidden" name="action" value="update_credit_score">
                    <div class="mb-3">
                        <label for="score" class="form-label">Credit Score Percentage</label>
                        <input type="number" class="form-control" id="score" name="score"
                               value="<?php echo htmlspecialchars($user['credit_score'] ?? ''); ?>"
                               min="0" max="100" placeholder="Enter credit score (0-100)" required>
                        <div class="form-text">Enter a percentage value between 0 and 100</div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Update Credit Score</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Change Password Modal -->
<div class="modal fade" id="changePasswordModal" tabindex="-1" aria-labelledby="changePasswordModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="changePasswordModalLabel">Change Login Password</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form action="view.php?id=<?php echo $user_id; ?>" method="POST">
                <div class="modal-body">
                    <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                    <input type="hidden" name="action" value="change_login_password">
                    <div class="mb-3">
                        <label for="new_password" class="form-label">New Login Password</label>
                        <input type="password" class="form-control" id="new_password" name="new_password"
                               placeholder="Enter new login password" required minlength="6">
                        <div class="form-text">Password must be at least 6 characters long</div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-info">Update Password</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Change Payment Password Modal -->
<div class="modal fade" id="changePaymentPasswordModal" tabindex="-1" aria-labelledby="changePaymentPasswordModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="changePaymentPasswordModalLabel">Change Payment Password</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form action="view.php?id=<?php echo $user_id; ?>" method="POST">
                <div class="modal-body">
                    <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                    <input type="hidden" name="action" value="change_payment_password">
                    <div class="mb-3">
                        <label for="new_payment_password" class="form-label">New Payment Password (Withdrawal PIN)</label>
                        <input type="password" class="form-control" id="new_payment_password" name="new_payment_password"
                               placeholder="Enter new payment password" required minlength="4">
                        <div class="form-text">This PIN is required for withdrawal requests. Must be at least 4 characters.</div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Update Payment Password</button>
                </div>
            </form>
        </div>
    </div>
</div>



<?php include '../includes/admin_footer_scripts.php'; ?>

<!-- Adjust Balance Modal -->
<div class="modal fade" id="adjustBalanceModal" tabindex="-1" aria-labelledby="adjustBalanceModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="adjustBalanceModalLabel">Adjust User Balance</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form action="view.php?id=<?php echo $user_id; ?>" method="POST">
                <div class="modal-body">
                    <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                    <input type="hidden" name="action" value="adjust_balance">
                    <p><strong>Username:</strong> <?php echo htmlspecialchars($user['username']); ?></p>
                    <p><strong>Current Balance:</strong> <?php echo formatCurrency($user['balance']); ?></p>
                    <div class="mb-3">
                        <label for="amount" class="form-label">Amount (USDT)</label>
                        <input type="number" class="form-control" id="amount" name="amount" step="0.01" min="0.01" required>
                    </div>
                    <div class="mb-3">
                        <label for="type" class="form-label">Operation Type</label>
                        <select class="form-select" id="type" name="type" required>
                            <option value="">Select operation</option>
                            <option value="addition">Add to Balance</option>
                            <option value="deduction">Deduct from Balance</option>
                        </select>
                    </div>
                    <div class="alert alert-warning">
                        <i class="bi bi-exclamation-triangle me-2"></i>
                        <strong>Warning:</strong> This will directly modify the user's balance and create a transaction record.
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                    <button type="submit" class="btn btn-primary">Apply Adjustment</button>
                </div>
            </form>
        </div>
    </div>
</div>
