<?php
/**
 * Bamboo Web Application - Admin Profile Management
 * Company: Notepadsly
 * Version: 1.0
 */

define('BAMBOO_APP', true);
require_once '../../includes/config.php';
require_once '../../includes/functions.php';

if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Check if admin is logged in
if (!isAdminLoggedIn()) {
    redirect('admin/login/');
}

$current_admin_id = getCurrentAdminId();
$admin_user = fetchRow("SELECT * FROM admin_users WHERE id = ?", [$current_admin_id]);

if (!$admin_user) {
    showError('Admin user not found.');
    redirect('admin/dashboard/');
}

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['csrf_token'])) {
    if (!verifyCSRFToken($_POST['csrf_token'])) {
        showError('Invalid security token. Please try again.');
    } else {
        $action = $_POST['action'] ?? '';
        
        switch ($action) {
            case 'update_profile':
                $username = sanitizeInput($_POST['username'] ?? '');
                $email = sanitizeInput($_POST['email'] ?? '');
                $full_name = sanitizeInput($_POST['full_name'] ?? '');
                
                // Validate inputs
                if (empty($username) || empty($email)) {
                    showError('Username and email are required.');
                    break;
                }
                
                if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
                    showError('Please enter a valid email address.');
                    break;
                }
                
                // Check for duplicates (excluding current user)
                $existing_admin = fetchRow("SELECT id FROM admin_users WHERE (username = ? OR email = ?) AND id != ?", [$username, $email, $current_admin_id]);
                if ($existing_admin) {
                    showError('Username or email already exists for another admin.');
                    break;
                }
                
                // Update profile
                $update_data = [
                    'username' => $username,
                    'email' => $email,
                    'full_name' => $full_name,
                    'updated_at' => date('Y-m-d H:i:s')
                ];
                
                if (updateRecord('admin_users', $update_data, 'id = ?', [$current_admin_id])) {
                    // Update session variables
                    $_SESSION['admin_username'] = $username;
                    $_SESSION['admin_email'] = $email;
                    $_SESSION['admin_full_name'] = $full_name;
                    
                    showSuccess('Profile updated successfully!');
                    // Refresh admin data
                    $admin_user = fetchRow("SELECT * FROM admin_users WHERE id = ?", [$current_admin_id]);
                } else {
                    showError('Failed to update profile. Please try again.');
                }
                break;
                
            case 'change_password':
                $current_password = $_POST['current_password'] ?? '';
                $new_password = $_POST['new_password'] ?? '';
                $confirm_password = $_POST['confirm_password'] ?? '';
                
                // Validate inputs
                if (empty($current_password) || empty($new_password) || empty($confirm_password)) {
                    showError('All password fields are required.');
                    break;
                }
                
                if (strlen($new_password) < 8) {
                    showError('New password must be at least 8 characters long.');
                    break;
                }
                
                if ($new_password !== $confirm_password) {
                    showError('New password and confirmation do not match.');
                    break;
                }
                
                // Verify current password
                if (!verifyPassword($current_password, $admin_user['password_hash'])) {
                    showError('Current password is incorrect.');
                    break;
                }
                
                // Update password
                $new_password_hash = password_hash($new_password, PASSWORD_DEFAULT);
                if (updateRecord('admin_users', ['password_hash' => $new_password_hash], 'id = ?', [$current_admin_id])) {
                    showSuccess('Password changed successfully!');
                } else {
                    showError('Failed to change password. Please try again.');
                }
                break;
        }
    }
}

// Page configuration
$page_title = 'Admin Profile';
$body_class = 'admin-page';
$additional_css = [
    BASE_URL . 'admin/assets/css/admin.css'
];

include '../includes/admin_header.php';
?>

<style>
.profile-card {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 1rem;
    padding: 2rem;
    margin-bottom: 2rem;
    border: none;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.profile-avatar {
    width: 120px;
    height: 120px;
    border-radius: 50%;
    background: linear-gradient(135deg, var(--bs-primary) 0%, var(--bs-secondary) 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 3rem;
    color: white;
    margin: 0 auto 1rem;
}

.info-card {
    background: white;
    border-radius: 0.75rem;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    border: 1px solid #e9ecef;
    transition: all 0.3s ease;
}

.info-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.form-floating {
    margin-bottom: 1rem;
}

.role-badge {
    font-size: 0.875rem;
    padding: 0.5rem 1rem;
    border-radius: 2rem;
}

.stats-item {
    text-align: center;
    padding: 1rem;
    border-radius: 0.5rem;
    background: rgba(255,255,255,0.7);
    margin-bottom: 1rem;
}

.stats-number {
    font-size: 1.5rem;
    font-weight: bold;
    color: var(--bs-primary);
}

.btn-section {
    border-top: 1px solid #e9ecef;
    padding-top: 1rem;
    margin-top: 1rem;
}
</style>

<div class="admin-wrapper">
    <?php include '../includes/admin_sidebar.php'; ?>
    <div class="admin-main">
        <?php include '../includes/admin_topbar.php'; ?>
        <div class="admin-content">
            <div class="container-fluid">
                
                <!-- Page Header -->
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <div>
                        <h1 class="h3 mb-0">
                            <i class="bi bi-person-circle text-primary me-2"></i>
                            My Profile
                        </h1>
                        <small class="text-muted">Manage your administrator account settings</small>
                    </div>
                    <div>
                        <a href="<?php echo BASE_URL; ?>admin/dashboard/" class="btn btn-secondary">
                            <i class="bi bi-arrow-left me-2"></i>Back to Dashboard
                        </a>
                    </div>
                </div>

                <div class="row">
                    <!-- Profile Overview -->
                    <div class="col-md-4">
                        <div class="profile-card text-center">
                            <div class="profile-avatar">
                                <?php echo strtoupper(substr($admin_user['username'], 0, 2)); ?>
                            </div>
                            <h4 class="mb-1"><?php echo htmlspecialchars($admin_user['full_name'] ?: $admin_user['username']); ?></h4>
                            <p class="text-muted mb-3"><?php echo htmlspecialchars($admin_user['email']); ?></p>
                            <span class="badge role-badge bg-<?php echo $admin_user['role'] === 'super_admin' ? 'danger' : ($admin_user['role'] === 'admin' ? 'primary' : 'secondary'); ?>">
                                <?php echo ucfirst(str_replace('_', ' ', $admin_user['role'])); ?>
                            </span>
                            
                            <div class="row mt-4">
                                <div class="col-6">
                                    <div class="stats-item">
                                        <div class="stats-number"><?php echo $admin_user['status'] === 'active' ? 'Active' : 'Inactive'; ?></div>
                                        <small class="text-muted">Status</small>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <div class="stats-item">
                                        <div class="stats-number"><?php echo date('M j', strtotime($admin_user['created_at'])); ?></div>
                                        <small class="text-muted">Joined</small>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="mt-3">
                                <small class="text-muted">
                                    <i class="bi bi-clock me-1"></i>
                                    Last login: <?php echo $admin_user['last_login'] ? date('M j, Y g:i A', strtotime($admin_user['last_login'])) : 'Never'; ?>
                                </small>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Profile Forms -->
                    <div class="col-md-8">
                        <!-- Update Profile Information -->
                        <div class="info-card">
                            <h5 class="mb-3">
                                <i class="bi bi-person-gear me-2"></i>Profile Information
                            </h5>
                            <form method="POST">
                                <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                                <input type="hidden" name="action" value="update_profile">
                                
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-floating">
                                            <input type="text" class="form-control" id="username" name="username" 
                                                   value="<?php echo htmlspecialchars($admin_user['username']); ?>" required>
                                            <label for="username">Username</label>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-floating">
                                            <input type="email" class="form-control" id="email" name="email" 
                                                   value="<?php echo htmlspecialchars($admin_user['email']); ?>" required>
                                            <label for="email">Email Address</label>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="form-floating">
                                    <input type="text" class="form-control" id="full_name" name="full_name" 
                                           value="<?php echo htmlspecialchars($admin_user['full_name'] ?? ''); ?>">
                                    <label for="full_name">Full Name (Optional)</label>
                                </div>
                                
                                <div class="btn-section">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="bi bi-check-circle me-2"></i>Update Profile
                                    </button>
                                </div>
                            </form>
                        </div>
                        
                        <!-- Change Password -->
                        <div class="info-card">
                            <h5 class="mb-3">
                                <i class="bi bi-shield-lock me-2"></i>Change Password
                            </h5>
                            <form method="POST" id="passwordForm">
                                <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                                <input type="hidden" name="action" value="change_password">
                                
                                <div class="form-floating">
                                    <input type="password" class="form-control" id="current_password" name="current_password" required>
                                    <label for="current_password">Current Password</label>
                                </div>
                                
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-floating">
                                            <input type="password" class="form-control" id="new_password" name="new_password" 
                                                   required minlength="8">
                                            <label for="new_password">New Password</label>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-floating">
                                            <input type="password" class="form-control" id="confirm_password" name="confirm_password" 
                                                   required minlength="8">
                                            <label for="confirm_password">Confirm New Password</label>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="btn-section">
                                    <button type="submit" class="btn btn-warning">
                                        <i class="bi bi-key me-2"></i>Change Password
                                    </button>
                                </div>
                            </form>
                        </div>
                        
                        <!-- Account Information -->
                        <div class="info-card">
                            <h5 class="mb-3">
                                <i class="bi bi-info-circle me-2"></i>Account Information
                            </h5>
                            <div class="row">
                                <div class="col-md-6">
                                    <p><strong>Account ID:</strong> #<?php echo $admin_user['id']; ?></p>
                                    <p><strong>Role:</strong> <?php echo ucfirst(str_replace('_', ' ', $admin_user['role'])); ?></p>
                                </div>
                                <div class="col-md-6">
                                    <p><strong>Created:</strong> <?php echo date('F j, Y g:i A', strtotime($admin_user['created_at'])); ?></p>
                                    <p><strong>Last Updated:</strong> <?php echo date('F j, Y g:i A', strtotime($admin_user['updated_at'])); ?></p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Password form validation
    const passwordForm = document.getElementById('passwordForm');
    if (passwordForm) {
        passwordForm.addEventListener('submit', function(e) {
            const newPassword = document.getElementById('new_password').value;
            const confirmPassword = document.getElementById('confirm_password').value;
            
            if (newPassword !== confirmPassword) {
                e.preventDefault();
                alert('New password and confirmation do not match.');
                return false;
            }
            
            if (newPassword.length < 8) {
                e.preventDefault();
                alert('Password must be at least 8 characters long.');
                return false;
            }
        });
    }
    
    // Auto-hide alerts after 5 seconds
    setTimeout(function() {
        const alerts = document.querySelectorAll('.alert');
        alerts.forEach(alert => {
            alert.style.transition = 'opacity 0.5s';
            alert.style.opacity = '0';
            setTimeout(() => alert.remove(), 500);
        });
    }, 5000);
});
</script>

<?php include '../includes/admin_footer.php'; ?>
