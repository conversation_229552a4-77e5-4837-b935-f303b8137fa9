<?php
/**
 * Bamboo Web Application - Admin Superior Management Page
 * Company: Notepadsly
 * Version: 1.0
 */

define('BAMBOO_APP', true);
require_once '../../includes/config.php';
require_once '../../includes/functions.php';
require_once '../../includes/database.php';

if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

if (!isAdminLoggedIn()) {
    redirect('admin/login/');
}

$page_title = 'Superior Management';
$page_section = 'superior_management';

// Handle form submission for adding a new agent/superior
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['add_superior'])) {
    $name = sanitizeInput($_POST['name'] ?? '');
    $phone = sanitizeInput($_POST['phone'] ?? '');
    $email = sanitizeInput($_POST['email'] ?? '');
    $invitation_code = generateRandomString(8);
    $now = date('Y-m-d H:i:s');
    // Validate inputs
    if (empty($name) || empty($phone) || empty($email)) {
        showError('Name, phone, and email are required.');
    } else {
        // Check if invitation code already exists (should be unique)
        if (recordExists('superiors', 'invitation_code = ?', [$invitation_code])) {
            showError('Generated invitation code already exists. Please try again.');
        } else {
            // Insert new agent/superior into the superiors table
            $new_superior_data = [
                'name' => $name,
                'phone' => $phone,
                'email' => $email,
                'invitation_code' => $invitation_code,
                'created_at' => $now
            ];
            if (insertRecord('superiors', $new_superior_data)) {
                showSuccess("Agent '{$name}' added successfully with Invitation Code: {$invitation_code}");
            } else {
                showError('Failed to add agent. Database error.');
            }
        }
    }
}

// Fetch all superiors (agents)
$superiors = fetchAll("SELECT * FROM superiors ORDER BY created_at DESC");

include '../includes/admin_header.php';
?>

<style>
/* Enhanced Table Styling with Primary Color Integration */
.table {
    border-collapse: separate;
    border-spacing: 0;
    border-radius: 0.75rem;
    overflow: hidden;
    box-shadow: 0 0.125rem 0.5rem rgba(0, 0, 0, 0.08);
}

.table thead th {
    background: var(--admin-table-header-bg, var(--admin-primary, #ff6900)) !important;
    border-bottom: 3px solid rgba(var(--dynamic-primary-rgb, 255, 105, 0), 0.3) !important;
    border-top: none !important;
    font-weight: 600;
    color: var(--admin-table-header-text, #ffffff) !important;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    padding: 1.25rem 0.75rem;
}

.table tbody tr {
    border-bottom: 2px solid #e9ecef !important;
    transition: all 0.3s ease;
    position: relative;
}

.table tbody tr:nth-child(even) {
    background-color: rgba(248, 249, 250, 0.5);
}

.table tbody tr:hover {
    background: linear-gradient(135deg, rgba(var(--admin-primary-rgb, 255, 105, 0), 0.05) 0%, rgba(var(--admin-primary-rgb, 255, 105, 0), 0.02) 100%);
    transform: scale(1.002);
    box-shadow: 0 0.25rem 0.75rem rgba(0, 0, 0, 0.12);
}

.table td {
    vertical-align: middle;
    padding: 1.25rem 0.75rem;
    border-left: none;
    border-right: none;
    position: relative;
}

.table th {
    border-left: none;
    border-right: none;
}
</style>

<div class="admin-wrapper">
    <?php include '../includes/admin_sidebar.php'; ?>
    <div class="admin-main">
        <?php include '../includes/admin_topbar.php'; ?>
        <div class="admin-content">
            <div class="container-fluid">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h1 class="h3 mb-0"><?php echo $page_title; ?></h1>
                </div>

                <?php $messages = getFlashMessages(); ?>
                <?php if (!empty($messages)): ?>
                    <div class="admin-flash-messages">
                        <?php if (isset($messages['success'])): ?>
                            <div class="alert alert-success alert-dismissible fade show" role="alert">
                                <?php echo htmlspecialchars($messages['success']); ?>
                                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                            </div>
                        <?php endif; ?>
                        <?php if (isset($messages['error'])): ?>
                            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                                <?php echo htmlspecialchars($messages['error']); ?>
                                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                            </div>
                        <?php endif; ?>
                    </div>
                <?php endif; ?>

                <div class="card mb-4">
                    <div class="card-header">
                        Add New Agent (Superior)
                    </div>
                    <div class="card-body">
                        <form method="POST">
                            <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                            <div class="row">
                                <div class="col-md-3 mb-3">
                                    <label for="name" class="form-label">Agent Name</label>
                                    <input type="text" class="form-control" id="name" name="name" required>
                                </div>
                                <div class="col-md-3 mb-3">
                                    <label for="phone" class="form-label">Phone</label>
                                    <input type="text" class="form-control" id="phone" name="phone" required>
                                </div>
                                <div class="col-md-3 mb-3">
                                    <label for="email" class="form-label">Email</label>
                                    <input type="email" class="form-control" id="email" name="email" required>
                                </div>
                                <div class="col-md-3 mb-3 d-flex align-items-end">
                                    <button type="submit" name="add_superior" class="btn btn-primary w-100">Add Agent</button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>

                <div class="card">
                    <div class="card-header">
                        All Agents (Superiors)
                    </div>
                    <div class="card-body">
                        <?php if (empty($superiors)): ?>
                            <p>No agents found.</p>
                        <?php else: ?>
                            <div class="table-responsive">
                                <table class="table table-striped table-hover">
                                    <thead>
                                        <tr>
                                            <th>ID</th>
                                            <th>Name</th>
                                            <th>Phone</th>
                                            <th>Email</th>
                                            <th>Invitation Code</th>
                                            <th>Invited Users</th>
                                            <th>Creation Time</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($superiors as $superior): ?>
                                            <tr>
                                                <td><?php echo htmlspecialchars($superior['id']); ?></td>
                                                <td><?php echo htmlspecialchars($superior['name']); ?></td>
                                                <td><?php echo htmlspecialchars($superior['phone']); ?></td>
                                                <td><?php echo htmlspecialchars($superior['email']); ?></td>
                                                <td><?php echo htmlspecialchars($superior['invitation_code']); ?></td>
                                                <td><?php echo (int)$superior['invited_count']; ?></td>
                                                <td><?php echo formatDate($superior['created_at']); ?></td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>

            </div>
        </div>
        <?php include '../includes/admin_footer.php'; ?>
    </div>
</div>

<?php include '../includes/admin_footer_scripts.php'; ?>
