/**
 * Bamboo Mobile Optimizations
 * Enhanced mobile experience with app-like interactions
 */

/* ===== MOBILE-FIRST OPTIMIZATIONS ===== */

/* Viewport Height Fix */
:root {
    --vh: 1vh;
}

.full-height {
    height: calc(var(--vh, 1vh) * 100);
}

/* ===== ENHANCED TOUCH INTERACTIONS ===== */

/* Better Touch Targets */
.touch-target {
    min-height: 44px;
    min-width: 44px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
}

/* Touch Feedback States */
.touching {
    opacity: 0.7;
    transform: scale(0.98);
}

.btn-pressed {
    transform: scale(0.95);
    opacity: 0.8;
}

/* ===== APP-LIKE ANIMATIONS ===== */

/* Fade In Animation */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Slide In From Bottom */
@keyframes slideInFromBottom {
    from {
        opacity: 0;
        transform: translateY(100%);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Bounce Animation */
@keyframes bounce {
    0%, 20%, 53%, 80%, 100% {
        transform: translate3d(0, 0, 0);
    }
    40%, 43% {
        transform: translate3d(0, -8px, 0);
    }
    70% {
        transform: translate3d(0, -4px, 0);
    }
    90% {
        transform: translate3d(0, -2px, 0);
    }
}

/* Pulse Animation */
@keyframes pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
    100% {
        transform: scale(1);
    }
}

/* ===== MOBILE NAVIGATION ENHANCEMENTS ===== */

/* Fixed Bottom Navigation with Blur Effect */
@media (max-width: 768px) {
    .user-footer {
        position: fixed !important;
        bottom: 0;
        left: 0;
        right: 0;
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(20px);
        -webkit-backdrop-filter: blur(20px);
        border-top: 1px solid rgba(0, 0, 0, 0.1);
        z-index: 1000;
        padding: 0.5rem 0 env(safe-area-inset-bottom);
    }
    
    .footer-nav-item {
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        border-radius: 0.75rem;
        margin: 0 0.25rem;
        position: relative;
        overflow: hidden;
    }
    
    .footer-nav-item::after {
        content: '';
        position: absolute;
        top: 50%;
        left: 50%;
        width: 0;
        height: 0;
        background: rgba(var(--primary-rgb), 0.2);
        border-radius: 50%;
        transform: translate(-50%, -50%);
        transition: all 0.3s ease;
    }
    
    .footer-nav-item:active::after {
        width: 100px;
        height: 100px;
    }
    
    .footer-nav-item.active {
        background: rgba(var(--primary-rgb), 0.1);
        color: var(--primary-color);
    }
    
    .footer-nav-item.active .footer-nav-icon {
        animation: bounce 0.6s ease;
    }
    
    /* Adjust main content for fixed footer */
    .main-content {
        padding-bottom: 100px !important;
    }
}

/* ===== ENHANCED FORM CONTROLS ===== */

@media (max-width: 768px) {
    /* Floating Labels */
    .form-floating {
        position: relative;
    }
    
    .form-floating > .form-control:focus ~ label,
    .form-floating > .form-control:not(:placeholder-shown) ~ label {
        opacity: 0.65;
        transform: scale(0.85) translateY(-0.5rem) translateX(0.15rem);
    }
    
    /* Enhanced Input States */
    .form-control:focus {
        border-color: var(--primary-color);
        box-shadow: 0 0 0 0.25rem rgba(var(--primary-rgb), 0.25);
        transform: translateY(-1px);
    }
    
    .form-control:invalid {
        border-color: #dc3545;
        animation: shake 0.5s ease-in-out;
    }
    
    @keyframes shake {
        0%, 100% { transform: translateX(0); }
        10%, 30%, 50%, 70%, 90% { transform: translateX(-2px); }
        20%, 40%, 60%, 80% { transform: translateX(2px); }
    }
}

/* ===== LOADING STATES ===== */

.loading-skeleton {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
}

@keyframes loading {
    0% {
        background-position: 200% 0;
    }
    100% {
        background-position: -200% 0;
    }
}

/* ===== MOBILE MODAL ENHANCEMENTS ===== */

@media (max-width: 768px) {
    .modal-dialog {
        margin: 0;
        height: 100vh;
        max-width: 100%;
    }
    
    .modal-content {
        height: 100vh;
        border-radius: 0;
        border: none;
    }
    
    .modal-fullscreen-sm-down {
        animation: slideInFromBottom 0.3s ease-out;
    }
    
    .modal.fade .modal-dialog {
        transform: translateY(100%);
        transition: transform 0.3s ease-out;
    }
    
    .modal.show .modal-dialog {
        transform: translateY(0);
    }
}

/* ===== PERFORMANCE OPTIMIZATIONS ===== */

/* Hardware Acceleration */
.accelerated {
    transform: translateZ(0);
    -webkit-transform: translateZ(0);
    will-change: transform;
}

/* Reduce Motion for Accessibility */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* ===== DARK MODE SUPPORT ===== */

@media (prefers-color-scheme: dark) {
    .auto-dark {
        background-color: #1a1a1a;
        color: #ffffff;
    }
    
    .auto-dark .card {
        background-color: #2d2d2d;
        border-color: #404040;
    }
    
    .auto-dark .user-footer {
        background: rgba(26, 26, 26, 0.95);
        border-top-color: #404040;
    }
}

/* ===== UTILITY CLASSES ===== */

.fade-in {
    animation: fadeIn 0.5s ease-out;
}

.slide-in-bottom {
    animation: slideInFromBottom 0.4s ease-out;
}

.bounce-in {
    animation: bounce 0.6s ease;
}

.pulse {
    animation: pulse 2s infinite;
}

.no-scroll {
    overflow: hidden;
    height: 100vh;
}

.smooth-transition {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.glass-effect {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}
