/**
 * Bamboo User Task Submission CSS
 * Core task system styling
 */

/* CSS Variables */
:root {
    --primary-color: #007bff;
    --secondary-color: #6c757d;
    --success-color: #28a745;
    --warning-color: #ffc107;
    --danger-color: #dc3545;
    --info-color: #17a2b8;
    --light-color: #f8f9fa;
    --dark-color: #343a40;
    --border-radius: 12px;
    --box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    --transition: all 0.3s ease;
}

/* Global Styles */
body {
    background: linear-gradient(135deg, #f5f6fa 0%, #e9ecef 100%);
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    min-height: 100vh;
}

/* Header Styles */
.task-header {
    background: linear-gradient(135deg, var(--primary-color), rgba(var(--primary-rgb), 0.8));
    color: white;
    padding: 1.5rem 0;
    box-shadow: var(--box-shadow);
}

.user-stats {
    text-align: right;
}

.stat-item {
    margin-bottom: 0.5rem;
}

.stat-label {
    font-size: 0.9rem;
    color: rgba(255, 255, 255, 0.8);
    margin-bottom: 0.25rem;
}

.stat-value {
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: 0.25rem;
}

/* Balance Section */
.balance-section {
    background: white;
    padding: 1.5rem 0;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.balance-card, .progress-card {
    text-align: center;
    padding: 1rem;
}

.balance-label, .progress-label {
    font-size: 0.9rem;
    color: var(--secondary-color);
    margin-bottom: 0.5rem;
    font-weight: 500;
}

.balance-value, .progress-value {
    font-size: 2rem;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 0.25rem;
}

.balance-note {
    color: var(--secondary-color);
    font-size: 0.8rem;
}

.progress-bar-container {
    margin-top: 0.5rem;
}

.progress {
    height: 8px;
    border-radius: 4px;
    background-color: rgba(0, 0, 0, 0.1);
}

/* Main Task Area */
.task-main {
    padding: 2rem 0;
    min-height: calc(100vh - 200px);
}

/* Product Grid */
.product-grid-container {
    max-width: 600px;
    margin: 0 auto;
    text-align: center;
}

.product-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 1rem;
    margin-bottom: 2rem;
    padding: 2rem;
    background: white;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
}

.product-placeholder {
    aspect-ratio: 1;
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    border: 2px dashed #dee2e6;
    border-radius: var(--border-radius);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    transition: var(--transition);
    cursor: pointer;
}

.product-placeholder:hover {
    border-color: var(--primary-color);
    background: linear-gradient(135deg, rgba(var(--primary-rgb), 0.1), rgba(var(--primary-rgb), 0.05));
}

.placeholder-icon {
    font-size: 2rem;
    color: var(--secondary-color);
    margin-bottom: 0.5rem;
}

.placeholder-text {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--secondary-color);
}

/* Start Button */
.start-button-container {
    text-align: center;
}

.start-matching-btn {
    font-size: 1.2rem;
    font-weight: 700;
    padding: 1rem 3rem;
    border-radius: 50px;
    background: linear-gradient(135deg, var(--primary-color), rgba(var(--primary-rgb), 0.8));
    border: none;
    box-shadow: 0 8px 20px rgba(var(--primary-rgb), 0.3);
    transition: var(--transition);
    text-transform: uppercase;
    letter-spacing: 1px;
}

.start-matching-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 12px 25px rgba(var(--primary-rgb), 0.4);
}

.start-matching-btn:active {
    transform: translateY(0);
}

/* Active Task Container */
.active-task-container {
    max-width: 800px;
    margin: 0 auto;
}

.task-card {
    background: white;
    border-radius: var(--border-radius);
    padding: 2rem;
    box-shadow: var(--box-shadow);
    border: 1px solid rgba(0, 0, 0, 0.05);
}

.task-title {
    color: var(--dark-color);
    font-weight: 700;
    margin-bottom: 1.5rem;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

/* Product Display */
.product-display {
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    border-radius: var(--border-radius);
    padding: 2rem;
    text-align: center;
    margin-bottom: 1.5rem;
}

.product-image {
    max-width: 200px;
    max-height: 200px;
    border-radius: var(--border-radius);
    margin-bottom: 1rem;
    box-shadow: var(--box-shadow);
}

.product-name {
    font-size: 1.3rem;
    font-weight: 700;
    color: var(--dark-color);
    margin-bottom: 0.5rem;
}

.product-price {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 0.5rem;
}

.product-profit {
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--success-color);
    margin-bottom: 1rem;
}

.product-details {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-top: 1rem;
}

.detail-item {
    text-align: left;
}

.detail-label {
    font-size: 0.9rem;
    color: var(--secondary-color);
    margin-bottom: 0.25rem;
}

.detail-value {
    font-weight: 600;
    color: var(--dark-color);
}

/* Task Actions */
.task-actions .btn {
    font-size: 1.1rem;
    font-weight: 700;
    padding: 1rem;
    border-radius: var(--border-radius);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.task-actions .btn-success {
    background: linear-gradient(135deg, var(--success-color), #20c997);
    border: none;
    box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
}

.task-actions .btn-success:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(40, 167, 69, 0.4);
}

/* Deposit Required Styles */
.deposit-required {
    background: linear-gradient(135deg, #fff3cd, #ffeaa7);
    border: 2px solid var(--warning-color);
}

.deposit-message {
    background: var(--warning-color);
    color: #856404;
    padding: 1rem;
    border-radius: var(--border-radius);
    margin-bottom: 1rem;
    font-weight: 600;
}

.balance-needed {
    font-size: 1.2rem;
    font-weight: 700;
    color: var(--danger-color);
    margin-bottom: 1rem;
}

/* Success/Error Messages */
.task-success {
    background: linear-gradient(135deg, #d4edda, #c3e6cb);
    border: 2px solid var(--success-color);
    color: #155724;
    padding: 1.5rem;
    border-radius: var(--border-radius);
    text-align: center;
    margin-bottom: 1rem;
}

.task-error {
    background: linear-gradient(135deg, #f8d7da, #f5c6cb);
    border: 2px solid var(--danger-color);
    color: #721c24;
    padding: 1.5rem;
    border-radius: var(--border-radius);
    text-align: center;
    margin-bottom: 1rem;
}

/* Loading States */
.loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.9);
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: var(--border-radius);
    z-index: 10;
}

/* Animations */
@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

.pulse {
    animation: pulse 2s infinite;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.fade-in {
    animation: fadeIn 0.5s ease-out;
}

/* Responsive Design */
@media (max-width: 768px) {
    .task-header {
        padding: 1rem 0;
    }
    
    .balance-section {
        padding: 1rem 0;
    }
    
    .task-main {
        padding: 1rem 0;
    }
    
    .product-grid {
        gap: 0.5rem;
        padding: 1rem;
    }
    
    .task-card {
        padding: 1.5rem;
    }
    
    .start-matching-btn {
        font-size: 1rem;
        padding: 0.75rem 2rem;
    }
    
    .product-details {
        grid-template-columns: 1fr;
    }
    
    .task-actions .btn {
        font-size: 1rem;
        padding: 0.75rem;
    }
}

@media (max-width: 576px) {
    .product-grid {
        grid-template-columns: repeat(3, 1fr);
        gap: 0.25rem;
        padding: 0.5rem;
    }
    
    .placeholder-icon {
        font-size: 1.5rem;
    }
    
    .placeholder-text {
        font-size: 1.2rem;
    }
    
    .balance-value, .progress-value {
        font-size: 1.5rem;
    }
    
    .stat-value {
        font-size: 1.2rem;
    }
}
