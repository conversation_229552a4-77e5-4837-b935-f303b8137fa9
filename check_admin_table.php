<?php
define('BAMBOO_APP', true);
require_once 'includes/config.php';

echo "=== ADMIN USERS TABLE STRUCTURE ===\n";

try {
    $pdo = new PDO('mysql:host=localhost;port=3306;dbname=matchmaking;charset=utf8mb4', 'root', 'root');
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // Show table structure
    $stmt = $pdo->query('DESCRIBE admin_users');
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "Table columns:\n";
    foreach($columns as $col) {
        echo "- {$col['Field']}: {$col['Type']} (Null: {$col['Null']}, Key: {$col['Key']}, Default: {$col['Default']})\n";
    }
    
    echo "\n=== ADMIN USER DATA ===\n";
    $stmt = $pdo->query('SELECT * FROM admin_users');
    $admin = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($admin) {
        foreach($admin as $key => $value) {
            if ($key === 'password_hash' && $value) {
                echo "$key: " . substr($value, 0, 20) . "...\n";
            } else {
                echo "$key: $value\n";
            }
        }
    }
    
} catch(Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}
?>
