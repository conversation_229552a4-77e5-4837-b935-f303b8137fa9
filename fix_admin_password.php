<?php
define('BAMBOO_APP', true);
require_once 'includes/config.php';

echo "=== FIXING ADMIN PASSWORD ===\n";

try {
    $pdo = new PDO('mysql:host=localhost;port=3306;dbname=matchmaking;charset=utf8mb4', 'root', 'root');
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // Get current admin
    $stmt = $pdo->query("SELECT * FROM admin_users WHERE username = 'admin'");
    $admin = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($admin) {
        echo "Current admin password hash: " . $admin['password_hash'] . "\n";
        
        // Create new password hash for 'admin123'
        $new_password = 'admin123';
        $new_hash = password_hash($new_password, PASSWORD_DEFAULT);
        
        echo "New password hash: $new_hash\n";
        
        // Update the admin password
        $stmt = $pdo->prepare("UPDATE admin_users SET password_hash = ? WHERE username = 'admin'");
        $result = $stmt->execute([$new_hash]);
        
        if ($result) {
            echo "✅ Admin password updated successfully!\n";
            
            // Test the new password
            $stmt = $pdo->query("SELECT password_hash FROM admin_users WHERE username = 'admin'");
            $updated_admin = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if (password_verify($new_password, $updated_admin['password_hash'])) {
                echo "✅ Password verification test passed!\n";
                echo "Admin login credentials: admin / admin123\n";
            } else {
                echo "❌ Password verification test failed!\n";
            }
        } else {
            echo "❌ Failed to update admin password\n";
        }
    } else {
        echo "❌ Admin user not found\n";
    }
    
} catch(Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}
?>
