# 📦 Bamboo Manual Installation Guide

## 🎯 Simple Manual Installation Process

The Bamboo installation has been simplified to a **manual process** that's more reliable and easier to control:

1. **You extract files manually** (production.zip)
2. **You import database manually** (database_migration.sql)
3. **install.php just updates config** with your database credentials
4. **Done!** - Simple and reliable

## 🗂️ What's in the install/ folder

## 📁 Files Included in Production.zip

The production folder already contains the clean, production-ready files. You need to create a ZIP archive of the entire `production/` folder contents.

### ✅ **Included Directories:**
- `admin/` - Complete admin panel system
- `user/` - User application files
- `includes/` - Core PHP includes and configuration
- `assets/` - CSS, JavaScript, and images
- `api/` - API endpoints
- `uploads/` - File upload directories with sample content
- `logs/` - Empty logs directory for error logging

### ✅ **Included Files:**
- `offline.html` - PWA offline page
- `sw.js` - Service worker for PWA functionality

### ❌ **Excluded (Not in Production):**
- `test/` - All test files and diagnostics
- `md/` - Documentation files
- `sql/` - SQL files (handled separately)
- Development configuration files
- Local development tools

## 🚀 Quick Installation Steps

### **Step 1: Create production.zip** (One-time)
```powershell
# Windows PowerShell - Run from Bamboo directory
Compress-Archive -Path "production\*" -DestinationPath "install\production.zip"
```

### **Step 2: Upload install/ folder to your server**
Upload the entire `install/` folder to your web server.

### **Step 3: Extract files manually**
1. Extract `production.zip` in your web root (where you want the application)
2. You should see folders: `admin/`, `user/`, `includes/`, `assets/`, etc.

### **Step 4: Import database manually**
1. Create a new database in your hosting control panel
2. Import `database_migration.sql` using phpMyAdmin or your hosting database tool

### **Step 5: Run install.php**
1. Navigate to: `yourdomain.com/install/install.php`
2. Enter your database credentials
3. The script will update your config.php file
4. Access your application!

## 🎯 Why Manual Installation?

- **More Reliable:** No complex automation that can fail
- **Better Control:** You see exactly what's happening
- **Hosting Friendly:** Works on any hosting provider
- **Simpler:** Just extract, import, configure - done!
- **No Permissions Issues:** You handle file extraction yourself

## 📋 Verification Checklist

After creating `production.zip`, verify it contains:

### ✅ **Required Directories:**
- [ ] `admin/` (with all subdirectories)
- [ ] `user/` (with login, logout, register, dashboard)
- [ ] `includes/` (config.php, database.php, functions.php, etc.)
- [ ] `assets/` (css, js, images folders)
- [ ] `api/` (csrf-token.php and other API files)
- [ ] `uploads/` (with subdirectories: avatars, backups, certificates, etc.)
- [ ] `logs/` (empty directory)

### ✅ **Required Root Files:**
- [ ] `offline.html`
- [ ] `sw.js`

### ✅ **Configuration Check:**
- [ ] `includes/config.php` has production-ready settings
- [ ] `DEBUG_MODE` is set to `false`
- [ ] Database credentials are placeholder values
- [ ] No test files included

## 🚀 Installation Process

Once `production.zip` is created and placed in the `install/` folder:

1. **Upload to Server:**
   - Upload the entire `install/` folder to your web server
   - Ensure `install/production.zip` and `install/database_migration.sql` are present

2. **Run Installer:**
   - Navigate to `yourdomain.com/install/install.php`
   - Follow the installation wizard
   - Choose "Automatic Installation" to use production.zip

3. **Post-Installation:**
   - Delete the `install/` folder for security
   - Change default admin password
   - Configure application settings

## 🔒 Security Notes

- **Never include** test files or development tools in production.zip
- **Always verify** that sensitive configuration is using placeholder values
- **Remove install folder** after successful installation
- **Change default passwords** immediately after installation

## 📊 Expected File Structure in production.zip

```
production.zip
├── admin/
│   ├── index.php
│   ├── login/
│   ├── dashboard/
│   ├── member_management/
│   └── [other admin modules]
├── user/
│   ├── login/
│   ├── register/
│   └── dashboard/
├── includes/
│   ├── config.php
│   ├── database.php
│   └── functions.php
├── assets/
│   ├── css/
│   ├── js/
│   └── images/
├── api/
├── uploads/
├── logs/
├── offline.html
└── sw.js
```

## 🆘 Troubleshooting

### Issue: ZIP file too large
- **Solution:** Remove unnecessary files from uploads/ folder
- **Keep:** Directory structure but remove large sample files

### Issue: Missing directories in ZIP
- **Solution:** Ensure you're selecting ALL contents of production/ folder
- **Check:** Use "Show hidden files" if some directories appear missing

### Issue: Installation fails
- **Check:** Verify production.zip contains all required files
- **Verify:** File permissions are correct after extraction
- **Test:** Try manual extraction to verify ZIP integrity

## 📞 Support

If you encounter issues creating production.zip:
1. Verify the production/ folder contains all necessary files
2. Check file permissions and access rights
3. Try different ZIP creation methods
4. Ensure sufficient disk space for ZIP creation

---

**Next Step:** After creating production.zip, run the installation by accessing `install/install.php` in your web browser.
