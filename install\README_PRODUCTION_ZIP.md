# 📦 Production.zip Creation Guide

This guide explains how to create the `production.zip` file required for the Bamboo installation system.

## 🎯 Overview

The `production.zip` file contains all the essential application files needed for deployment, excluding development files, tests, and documentation.

## 📁 Files Included in Production.zip

The production folder already contains the clean, production-ready files. You need to create a ZIP archive of the entire `production/` folder contents.

### ✅ **Included Directories:**
- `admin/` - Complete admin panel system
- `user/` - User application files
- `includes/` - Core PHP includes and configuration
- `assets/` - CSS, JavaScript, and images
- `api/` - API endpoints
- `uploads/` - File upload directories with sample content
- `logs/` - Empty logs directory for error logging

### ✅ **Included Files:**
- `offline.html` - PWA offline page
- `sw.js` - Service worker for PWA functionality

### ❌ **Excluded (Not in Production):**
- `test/` - All test files and diagnostics
- `md/` - Documentation files
- `sql/` - SQL files (handled separately)
- Development configuration files
- Local development tools

## 🔧 How to Create production.zip

### Method 1: Using File Explorer (Windows)
1. Navigate to the `Bamboo/production/` folder
2. Select ALL contents inside the production folder (Ctrl+A)
3. Right-click and select "Send to" → "Compressed (zipped) folder"
4. Rename the zip file to `production.zip`
5. Move `production.zip` to the `install/` folder

### Method 2: Using Command Line (Windows)
```powershell
# Navigate to the Bamboo directory
cd C:\MAMP\htdocs\Bamboo

# Create the zip file
powershell Compress-Archive -Path "production\*" -DestinationPath "install\production.zip"
```

### Method 3: Using Command Line (Mac/Linux)
```bash
# Navigate to the Bamboo directory
cd /path/to/Bamboo

# Create the zip file
cd production
zip -r ../install/production.zip .
```

### Method 4: Using 7-Zip (Recommended)
1. Install 7-Zip if not already installed
2. Navigate to `Bamboo/production/` folder
3. Select all contents (Ctrl+A)
4. Right-click → 7-Zip → "Add to archive..."
5. Set archive name to `production.zip`
6. Set archive format to ZIP
7. Move to `install/` folder

## 📋 Verification Checklist

After creating `production.zip`, verify it contains:

### ✅ **Required Directories:**
- [ ] `admin/` (with all subdirectories)
- [ ] `user/` (with login, logout, register, dashboard)
- [ ] `includes/` (config.php, database.php, functions.php, etc.)
- [ ] `assets/` (css, js, images folders)
- [ ] `api/` (csrf-token.php and other API files)
- [ ] `uploads/` (with subdirectories: avatars, backups, certificates, etc.)
- [ ] `logs/` (empty directory)

### ✅ **Required Root Files:**
- [ ] `offline.html`
- [ ] `sw.js`

### ✅ **Configuration Check:**
- [ ] `includes/config.php` has production-ready settings
- [ ] `DEBUG_MODE` is set to `false`
- [ ] Database credentials are placeholder values
- [ ] No test files included

## 🚀 Installation Process

Once `production.zip` is created and placed in the `install/` folder:

1. **Upload to Server:**
   - Upload the entire `install/` folder to your web server
   - Ensure `install/production.zip` and `install/database_migration.sql` are present

2. **Run Installer:**
   - Navigate to `yourdomain.com/install/install.php`
   - Follow the installation wizard
   - Choose "Automatic Installation" to use production.zip

3. **Post-Installation:**
   - Delete the `install/` folder for security
   - Change default admin password
   - Configure application settings

## 🔒 Security Notes

- **Never include** test files or development tools in production.zip
- **Always verify** that sensitive configuration is using placeholder values
- **Remove install folder** after successful installation
- **Change default passwords** immediately after installation

## 📊 Expected File Structure in production.zip

```
production.zip
├── admin/
│   ├── index.php
│   ├── login/
│   ├── dashboard/
│   ├── member_management/
│   └── [other admin modules]
├── user/
│   ├── login/
│   ├── register/
│   └── dashboard/
├── includes/
│   ├── config.php
│   ├── database.php
│   └── functions.php
├── assets/
│   ├── css/
│   ├── js/
│   └── images/
├── api/
├── uploads/
├── logs/
├── offline.html
└── sw.js
```

## 🆘 Troubleshooting

### Issue: ZIP file too large
- **Solution:** Remove unnecessary files from uploads/ folder
- **Keep:** Directory structure but remove large sample files

### Issue: Missing directories in ZIP
- **Solution:** Ensure you're selecting ALL contents of production/ folder
- **Check:** Use "Show hidden files" if some directories appear missing

### Issue: Installation fails
- **Check:** Verify production.zip contains all required files
- **Verify:** File permissions are correct after extraction
- **Test:** Try manual extraction to verify ZIP integrity

## 📞 Support

If you encounter issues creating production.zip:
1. Verify the production/ folder contains all necessary files
2. Check file permissions and access rights
3. Try different ZIP creation methods
4. Ensure sufficient disk space for ZIP creation

---

**Next Step:** After creating production.zip, run the installation by accessing `install/install.php` in your web browser.
