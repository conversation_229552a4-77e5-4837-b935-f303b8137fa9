-- BA<PERSON>OO DATABASE - <PERSON><PERSON>AN VERSION FOR SHARED HOSTING
-- Generated: 2025-07-08 11:30:00
-- Compatible with all shared hosting providers
-- No procedures, triggers, or SUPER privileges required

-- Disable foreign key checks for clean import
SET FOREIGN_KEY_CHECKS = 0;
SET SQL_MODE = 'NO_AUTO_VALUE_ON_ZERO';
SET AUTOCOMMIT = 0;
START TRANSACTION;

-- Drop existing tables (in correct order)
DROP TABLE IF EXISTS `task_completions`;
DROP TABLE IF EXISTS `user_salaries`;
DROP TABLE IF EXISTS `withdrawal_quotes`;
DROP TABLE IF EXISTS `negative_settings`;
DROP TABLE IF EXISTS `customer_service_contacts`;
DROP TABLE IF EXISTS `app_settings`;
DROP TABLE IF EXISTS `transactions`;
DROP TABLE IF EXISTS `tasks`;
DROP TABLE IF EXISTS `products`;
DROP TABLE IF EXISTS `product_categories`;
DROP TABLE IF EXISTS `notifications`;
DROP TABLE IF EXISTS `user_sessions`;
DROP TABLE IF EXISTS `users`;
DROP TABLE IF EXISTS `vip_levels`;
DROP TABLE IF EXISTS `settings`;
DROP TABLE IF EXISTS `admin_users`;

-- CREATE TABLES

-- Admin users table
CREATE TABLE `admin_users` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `username` varchar(50) NOT NULL,
  `email` varchar(100) NOT NULL,
  `password` varchar(255) NOT NULL,
  `full_name` varchar(100) DEFAULT NULL,
  `role` enum('super_admin','admin','moderator') DEFAULT 'admin',
  `status` enum('active','inactive') DEFAULT 'active',
  `last_login` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  UNIQUE KEY `username` (`username`),
  UNIQUE KEY `email` (`email`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- VIP levels table
CREATE TABLE `vip_levels` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `level` int(11) NOT NULL,
  `name` varchar(50) NOT NULL,
  `min_balance` decimal(10,2) NOT NULL DEFAULT 0.00,
  `max_daily_tasks` int(11) NOT NULL DEFAULT 5,
  `commission_multiplier` decimal(3,2) NOT NULL DEFAULT 1.00,
  `withdrawal_limit_daily` decimal(10,2) NOT NULL DEFAULT 100.00,
  `withdrawal_fee_percentage` decimal(5,2) NOT NULL DEFAULT 5.00,
  `benefits` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`),
  UNIQUE KEY `level` (`level`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Users table
CREATE TABLE `users` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `username` varchar(50) NOT NULL,
  `email` varchar(100) NOT NULL,
  `password` varchar(255) NOT NULL,
  `phone` varchar(20) DEFAULT NULL,
  `full_name` varchar(100) DEFAULT NULL,
  `balance` decimal(10,2) NOT NULL DEFAULT 0.00,
  `commission_balance` decimal(10,2) NOT NULL DEFAULT 0.00,
  `vip_level` int(11) NOT NULL DEFAULT 1,
  `status` enum('active','inactive','suspended') DEFAULT 'active',
  `invited_by` int(11) DEFAULT NULL,
  `referral_count` int(11) NOT NULL DEFAULT 0,
  `tasks_completed_today` int(11) NOT NULL DEFAULT 0,
  `last_task_date` date DEFAULT NULL,
  `total_deposited` decimal(10,2) NOT NULL DEFAULT 0.00,
  `total_withdrawn` decimal(10,2) NOT NULL DEFAULT 0.00,
  `total_commission_earned` decimal(10,2) NOT NULL DEFAULT 0.00,
  `wallet_address` varchar(255) DEFAULT NULL,
  `credit_score` int(11) NOT NULL DEFAULT 100,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  UNIQUE KEY `username` (`username`),
  UNIQUE KEY `email` (`email`),
  KEY `vip_level` (`vip_level`),
  KEY `invited_by` (`invited_by`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Product categories table
CREATE TABLE `product_categories` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL,
  `description` text DEFAULT NULL,
  `status` enum('active','inactive') DEFAULT 'active',
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Products table
CREATE TABLE `products` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(200) NOT NULL,
  `description` text DEFAULT NULL,
  `price` decimal(10,2) NOT NULL,
  `commission_rate` decimal(5,2) NOT NULL DEFAULT 5.00,
  `category_id` int(11) DEFAULT NULL,
  `image_url` varchar(500) DEFAULT NULL,
  `min_vip_level` int(11) NOT NULL DEFAULT 1,
  `weight` int(11) NOT NULL DEFAULT 1,
  `status` enum('active','inactive') DEFAULT 'active',
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `category_id` (`category_id`),
  KEY `min_vip_level` (`min_vip_level`),
  KEY `status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Tasks table
CREATE TABLE `tasks` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `product_id` int(11) NOT NULL,
  `status` enum('assigned','in_progress','completed','expired','cancelled') DEFAULT 'assigned',
  `commission_earned` decimal(10,2) NOT NULL DEFAULT 0.00,
  `base_commission` decimal(10,2) NOT NULL DEFAULT 0.00,
  `assigned_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `started_at` timestamp NULL DEFAULT NULL,
  `completed_at` timestamp NULL DEFAULT NULL,
  `expires_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  KEY `product_id` (`product_id`),
  KEY `status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Transactions table
CREATE TABLE `transactions` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `type` enum('deposit','withdrawal','commission','task_payment','bonus','referral_bonus') NOT NULL,
  `amount` decimal(10,2) NOT NULL,
  `balance_before` decimal(10,2) NOT NULL,
  `balance_after` decimal(10,2) NOT NULL,
  `transaction_id` varchar(100) DEFAULT NULL,
  `payment_channel` varchar(50) DEFAULT NULL,
  `description` text DEFAULT NULL,
  `status` enum('pending','completed','failed','cancelled') DEFAULT 'pending',
  `admin_id` int(11) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  UNIQUE KEY `transaction_id` (`transaction_id`),
  KEY `user_id` (`user_id`),
  KEY `type` (`type`),
  KEY `status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Settings table
CREATE TABLE `settings` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `key` varchar(100) NOT NULL,
  `value` text DEFAULT NULL,
  `description` text DEFAULT NULL,
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  UNIQUE KEY `key` (`key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Notifications table
CREATE TABLE `notifications` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `title` varchar(200) NOT NULL,
  `message` text NOT NULL,
  `type` enum('info','success','warning','error') DEFAULT 'info',
  `is_read` tinyint(1) NOT NULL DEFAULT 0,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  KEY `is_read` (`is_read`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- User sessions table
CREATE TABLE `user_sessions` (
  `id` varchar(128) NOT NULL,
  `user_id` int(11) NOT NULL,
  `ip_address` varchar(45) DEFAULT NULL,
  `user_agent` text DEFAULT NULL,
  `last_activity` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  KEY `last_activity` (`last_activity`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- INSERT ESSENTIAL DATA

-- Insert default admin user (password: admin123)
INSERT INTO `admin_users` (`username`, `email`, `password`, `full_name`, `role`, `status`) VALUES
('admin', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'System Administrator', 'super_admin', 'active');

-- Insert VIP levels
INSERT INTO `vip_levels` (`level`, `name`, `min_balance`, `max_daily_tasks`, `commission_multiplier`, `withdrawal_limit_daily`, `withdrawal_fee_percentage`, `benefits`) VALUES
(1, 'VIP 1', 0.00, 5, 1.00, 100.00, 5.00, 'Basic access to platform'),
(2, 'VIP 2', 100.00, 10, 1.20, 500.00, 4.00, 'Unlimited access to all features'),
(3, 'VIP 3', 500.00, 15, 1.50, 1000.00, 3.00, 'Premium features and higher commissions'),
(4, 'VIP 4', 1000.00, 20, 1.80, 2000.00, 2.50, 'Elite status with maximum benefits'),
(5, 'VIP 5', 2500.00, 30, 2.00, 5000.00, 2.00, 'Ultimate VIP with highest rewards');

-- Insert basic settings
INSERT INTO `settings` (`key`, `value`, `description`) VALUES
('app_name', 'Bamboo', 'Application name'),
('app_version', '1.0', 'Application version'),
('maintenance_mode', '0', 'Maintenance mode (0=off, 1=on)'),
('registration_enabled', '1', 'User registration enabled (0=off, 1=on)'),
('min_withdrawal', '10.00', 'Minimum withdrawal amount'),
('max_withdrawal', '10000.00', 'Maximum withdrawal amount');

-- Re-enable foreign key checks
COMMIT;
SET FOREIGN_KEY_CHECKS = 1;
SET SQL_MODE = '';
SET AUTOCOMMIT = 1;

-- CLEAN IMPORT COMPLETE - READY FOR SHARED HOSTING!
