-- SHARED HOSTING COMPATIBLE VERSION
-- Generated automatically for shared hosting compatibility
-- Instructions: Create your database first, then import this file

-- Disable foreign key checks for clean import
SET FOREIGN_KEY_CHECKS = 0;
SET SQL_MODE = 'NO_AUTO_VALUE_ON_ZERO';
SET AUTOCOMMIT = 0;
START TRANSACTION;

-- Bamboo Web Application Database Migration Script
-- SHARED HOSTING FRIENDLY VERSION
-- Version: 2.0 - Compatible with shared hosting restrictions
--
-- INSTRUCTIONS FOR SHARED HOSTING:
-- 1. Create your database through hosting control panel first
-- 2. Select your database in phpMyAdmin
-- 3. Import this file (it will -- USE database removed for shared hosting

-- Drop existing tables if they exist (for clean reinstall)
-- Order matters due to foreign key constraints
DROP TABLE IF EXISTS `task_completions`;
DROP TABLE IF EXISTS `user_salaries`;
DROP TABLE IF EXISTS `withdrawal_quotes`;
DROP TABLE IF EXISTS `negative_settings`;
DROP TABLE IF EXISTS `customer_service_contacts`;
DROP TABLE IF EXISTS `app_settings`;
DROP TABLE IF EXISTS `transactions`;
DROP TABLE IF EXISTS `tasks`;
DROP TABLE IF EXISTS `products`;
DROP TABLE IF EXISTS `product_categories`;
DROP TABLE IF EXISTS `notifications`;
DROP TABLE IF EXISTS `user_sessions`;
DROP TABLE IF EXISTS `users`;
DROP TABLE IF EXISTS `vip_levels`;
DROP TABLE IF EXISTS `settings`;
DROP TABLE IF EXISTS `admin_users`;

-- NOTE: No -- CREATE DATABASE removed for shared hosting

-- Insert default VIP levels
INSERT INTO `vip_levels` (`level`, `name`, `min_balance`, `max_daily_tasks`, `commission_multiplier`, `withdrawal_limit_daily`, `withdrawal_fee_percentage`, `benefits`) VALUES
(1, 'VIP 1', 0.00, 5, 1.00, 100.00, 5.00, 'Basic access to platform'),
(2, 'VIP 2', 100.00, 10, 1.20, 500.00, 4.00, 'Unlimited access to all features'),
(3, 'VIP 3', 500.00, 15, 1.50, 1000.00, 3.00, 'Premium features and higher commissions'),
(4, 'VIP 4', 1000.00, 20, 1.80, 2000.00, 2.50, 'Elite status with maximum benefits'),
(5, 'VIP 5', 2500.00, 30, 2.00, 5000.00, 2.00, 'Ultimate VIP with highest rewards');

-- =============================================
-- USERS TABLE
-- =============================================
CREATE TABLE `users` (
    `id` INT PRIMARY KEY AUTO_INCREMENT,
    `username` VARCHAR(50) NOT NULL UNIQUE,
    `phone` VARCHAR(20) NOT NULL UNIQUE,
    `email` VARCHAR(100) UNIQUE,
    `password_hash` VARCHAR(255) NOT NULL,
    `withdrawal_pin_hash` VARCHAR(255) NOT NULL,
    `gender` ENUM('male', 'female') NOT NULL,
    `invitation_code` VARCHAR(20) NOT NULL UNIQUE,
    `invited_by` INT DEFAULT NULL,
    `balance` DECIMAL(10,2) NOT NULL DEFAULT 0.00,
    `commission_balance` DECIMAL(10,2) NOT NULL DEFAULT 0.00,
    `frozen_balance` DECIMAL(10,2) NOT NULL DEFAULT 0.00,
    `total_deposited` DECIMAL(10,2) NOT NULL DEFAULT 0.00,
    `total_withdrawn` DECIMAL(10,2) NOT NULL DEFAULT 0.00,
    `total_commission_earned` DECIMAL(10,2) NOT NULL DEFAULT 0.00,
    `vip_level` INT NOT NULL DEFAULT 1,
    `tasks_completed_today` INT NOT NULL DEFAULT 0,
    `last_task_date` DATE DEFAULT NULL,
    `status` ENUM('pending', 'active', 'suspended', 'banned') NOT NULL DEFAULT 'pending',
    `email_verified` BOOLEAN NOT NULL DEFAULT FALSE,
    `phone_verified` BOOLEAN NOT NULL DEFAULT FALSE,
    `avatar_url` VARCHAR(500) DEFAULT NULL,
    `referral_count` INT NOT NULL DEFAULT 0,
    `credit_score` INT NOT NULL DEFAULT 100,
    `last_login` TIMESTAMP NULL,
    `login_count` INT NOT NULL DEFAULT 0,
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (`vip_level`) REFERENCES `vip_levels`(`level`) ON UPDATE CASCADE,
    FOREIGN KEY (`invited_by`) REFERENCES `users`(`id`) ON DELETE SET NULL,
    
    INDEX `idx_username` (`username`),
    INDEX `idx_phone` (`phone`),
    INDEX `idx_email` (`email`),
    INDEX `idx_invitation_code` (`invitation_code`),
    INDEX `idx_invited_by` (`invited_by`),
    INDEX `idx_vip_level` (`vip_level`),
    INDEX `idx_status` (`status`),
    INDEX `idx_created_at` (`created_at`)
) ENGINE=InnoDB;

-- =============================================
-- ADMIN USERS TABLE
-- =============================================
CREATE TABLE `admin_users` (
    `id` INT PRIMARY KEY AUTO_INCREMENT,
    `username` VARCHAR(50) NOT NULL UNIQUE,
    `email` VARCHAR(100) NOT NULL UNIQUE,
    `password_hash` VARCHAR(255) NOT NULL,
    `role` ENUM('super_admin', 'admin', 'moderator') NOT NULL DEFAULT 'admin',
    `permissions` JSON DEFAULT NULL,
    `last_login` TIMESTAMP NULL,
    `status` ENUM('active', 'inactive') NOT NULL DEFAULT 'active',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX `idx_username` (`username`),
    INDEX `idx_email` (`email`),
    INDEX `idx_role` (`role`)
) ENGINE=InnoDB;

-- Insert default admin user (password: admin123)
INSERT INTO `admin_users` (`username`, `email`, `password_hash`, `role`) VALUES
('admin', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'super_admin');

-- =============================================
-- PRODUCT CATEGORIES TABLE
-- =============================================
CREATE TABLE `product_categories` (
    `id` INT PRIMARY KEY AUTO_INCREMENT,
    `name` VARCHAR(100) NOT NULL,
    `description` TEXT,
    `status` ENUM('active', 'inactive') NOT NULL DEFAULT 'active',
    `sort_order` INT NOT NULL DEFAULT 0,
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB;

-- Insert default categories
INSERT INTO `product_categories` (`name`, `description`) VALUES
('Electronics', 'Electronic devices and gadgets'),
('Fashion', 'Clothing and accessories'),
('Home & Garden', 'Home improvement and garden items'),
('Sports', 'Sports and fitness equipment'),
('Beauty', 'Beauty and personal care products');

-- =============================================
-- PRODUCTS TABLE
-- =============================================
CREATE TABLE `products` (
    `id` INT PRIMARY KEY AUTO_INCREMENT,
    `name` VARCHAR(255) NOT NULL,
    `description` TEXT,
    `image_url` VARCHAR(500),
    `price` DECIMAL(10,2) NOT NULL,
    `commission_rate` DECIMAL(5,2) NOT NULL DEFAULT 5.00,
    `category_id` INT NOT NULL,
    `min_vip_level` INT NOT NULL DEFAULT 1,
    `max_daily_assignments` INT NOT NULL DEFAULT 100,
    `weight` INT NOT NULL DEFAULT 1,
    `stock` INT NOT NULL DEFAULT 0,
    `status` ENUM('active', 'inactive', 'out_of_stock') NOT NULL DEFAULT 'active',
    `total_assignments` INT NOT NULL DEFAULT 0,
    `total_completions` INT NOT NULL DEFAULT 0,
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (`category_id`) REFERENCES `product_categories`(`id`) ON DELETE CASCADE,
    FOREIGN KEY (`min_vip_level`) REFERENCES `vip_levels`(`level`) ON UPDATE CASCADE,
    
    INDEX `idx_category` (`category_id`),
    INDEX `idx_status` (`status`),
    INDEX `idx_min_vip_level` (`min_vip_level`),
    INDEX `idx_price` (`price`)
) ENGINE=InnoDB;

-- =============================================
-- TASKS TABLE
-- =============================================
CREATE TABLE `tasks` (
    `id` INT PRIMARY KEY AUTO_INCREMENT,
    `user_id` INT NOT NULL,
    `product_id` INT NOT NULL,
    `amount` DECIMAL(10,2) NOT NULL DEFAULT 0.00, -- Added for dashboard compatibility
    `commission_earned` DECIMAL(10,2) NOT NULL DEFAULT 0.00,
    `base_commission` DECIMAL(10,2) NOT NULL DEFAULT 0.00,
    `vip_bonus` DECIMAL(10,2) NOT NULL DEFAULT 0.00,
    `status` ENUM('assigned', 'in_progress', 'completed', 'failed', 'expired') NOT NULL DEFAULT 'assigned',
    `assigned_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `started_at` TIMESTAMP NULL,
    `completed_at` TIMESTAMP NULL,
    `expires_at` TIMESTAMP NULL,
    `submission_data` JSON DEFAULT NULL,
    `admin_notes` TEXT DEFAULT NULL,
    
    FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON DELETE CASCADE,
    FOREIGN KEY (`product_id`) REFERENCES `products`(`id`) ON DELETE CASCADE,
    
    INDEX `idx_user_id` (`user_id`),
    INDEX `idx_product_id` (`product_id`),
    INDEX `idx_status` (`status`),
    INDEX `idx_assigned_at` (`assigned_at`),
    INDEX `idx_completed_at` (`completed_at`)
) ENGINE=InnoDB;

-- =============================================
-- TRANSACTIONS TABLE
-- =============================================
CREATE TABLE `transactions` (
    `id` INT PRIMARY KEY AUTO_INCREMENT,
    `user_id` INT NOT NULL,
    `type` ENUM('deposit', 'withdrawal', 'commission', 'bonus', 'referral_bonus', 'penalty', 'adjustment') NOT NULL,
    `amount` DECIMAL(10,2) NOT NULL,
    `balance_before` DECIMAL(10,2) NOT NULL,
    `balance_after` DECIMAL(10,2) NOT NULL,
    `status` ENUM('pending', 'processing', 'completed', 'failed', 'cancelled') NOT NULL DEFAULT 'pending',
    `payment_method` VARCHAR(50) DEFAULT NULL,
    `transaction_id` VARCHAR(100) UNIQUE DEFAULT NULL,
    `external_transaction_id` VARCHAR(255) DEFAULT NULL,
    `fee_amount` DECIMAL(10,2) NOT NULL DEFAULT 0.00,
    `description` TEXT DEFAULT NULL,
    `admin_notes` TEXT DEFAULT NULL,
    `processed_by` INT DEFAULT NULL,
    `processed_at` TIMESTAMP NULL,
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON DELETE CASCADE,
    FOREIGN KEY (`processed_by`) REFERENCES `admin_users`(`id`) ON DELETE SET NULL,
    
    INDEX `idx_user_id` (`user_id`),
    INDEX `idx_type` (`type`),
    INDEX `idx_status` (`status`),
    INDEX `idx_transaction_id` (`transaction_id`),
    INDEX `idx_created_at` (`created_at`)
) ENGINE=InnoDB;

-- =============================================
-- USER SESSIONS TABLE
-- =============================================
CREATE TABLE `user_sessions` (
    `id` VARCHAR(128) PRIMARY KEY,
    `user_id` INT NOT NULL,
    `ip_address` VARCHAR(45) NOT NULL,
    `user_agent` TEXT NOT NULL,
    `payload` LONGTEXT NOT NULL,
    `last_activity` INT NOT NULL,
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON DELETE CASCADE,
    
    INDEX `idx_user_id` (`user_id`),
    INDEX `idx_last_activity` (`last_activity`)
) ENGINE=InnoDB;

-- =============================================
-- NOTIFICATIONS TABLE
-- =============================================
CREATE TABLE `notifications` (
    `id` INT PRIMARY KEY AUTO_INCREMENT,
    `type` ENUM('system', 'user', 'admin', 'banner') NOT NULL,
    `title` VARCHAR(255) NOT NULL,
    `message` TEXT NOT NULL,
    `target_user_id` INT DEFAULT NULL,
    `target_vip_level` INT DEFAULT NULL,
    `is_global` BOOLEAN NOT NULL DEFAULT FALSE,
    `is_popup` BOOLEAN NOT NULL DEFAULT FALSE,
    `is_banner` BOOLEAN NOT NULL DEFAULT FALSE,
    `banner_color` VARCHAR(7) DEFAULT '#007bff',
    `status` ENUM('active', 'inactive', 'scheduled') NOT NULL DEFAULT 'active',
    `start_date` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `end_date` TIMESTAMP NULL,
    `read_count` INT NOT NULL DEFAULT 0,
    `created_by` INT DEFAULT NULL,
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (`target_user_id`) REFERENCES `users`(`id`) ON DELETE CASCADE,
    FOREIGN KEY (`created_by`) REFERENCES `admin_users`(`id`) ON DELETE SET NULL,
    
    INDEX `idx_type` (`type`),
    INDEX `idx_target_user_id` (`target_user_id`),
    INDEX `idx_is_global` (`is_global`),
    INDEX `idx_status` (`status`),
    INDEX `idx_start_date` (`start_date`)
) ENGINE=InnoDB;

-- =============================================
-- SETTINGS TABLE
-- =============================================
CREATE TABLE `settings` (
    `id` INT PRIMARY KEY AUTO_INCREMENT,
    `key` VARCHAR(100) NOT NULL UNIQUE,
    `value` TEXT NOT NULL,
    `type` ENUM('string', 'integer', 'float', 'boolean', 'json') NOT NULL DEFAULT 'string',
    `description` TEXT DEFAULT NULL,
    `category` VARCHAR(50) NOT NULL DEFAULT 'general',
    `is_public` BOOLEAN NOT NULL DEFAULT FALSE,
    `updated_by` INT DEFAULT NULL,
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (`updated_by`) REFERENCES `admin_users`(`id`) ON DELETE SET NULL,
    
    INDEX `idx_key` (`key`),
    INDEX `idx_category` (`category`)
) ENGINE=InnoDB;

-- Insert default settings
INSERT INTO `settings` (`key`, `value`, `type`, `description`, `category`, `is_public`) VALUES
('app_name', 'Bamboo', 'string', 'Application name', 'general', TRUE),
('app_logo', '/assets/images/logo.png', 'string', 'Application logo URL', 'general', TRUE),
('welcome_bonus', '10.00', 'float', 'Welcome bonus for new users', 'financial', FALSE),
('min_withdrawal', '20.00', 'float', 'Minimum withdrawal amount', 'financial', TRUE),
('max_withdrawal_daily', '1000.00', 'float', 'Maximum daily withdrawal', 'financial', TRUE),
('negative_balance_trigger', '-50.00', 'float', 'Balance threshold to trigger negative balance', 'financial', FALSE),
('task_expiry_hours', '24', 'integer', 'Hours before task expires', 'tasks', FALSE),
('max_tasks_per_day', '10', 'integer', 'Maximum tasks per day for VIP 1', 'tasks', FALSE),
('referral_commission_rate', '5.00', 'float', 'Referral commission percentage', 'referral', FALSE),
('maintenance_mode', 'false', 'boolean', 'Enable maintenance mode', 'system', FALSE),
('registration_enabled', 'true', 'boolean', 'Enable user registration', 'system', TRUE),
('welcome_popup_text', 'Welcome to Bamboo! During our anniversary celebration, new users can get a bonus for the first time to complete group tasks.', 'string', 'Welcome popup message', 'general', TRUE),
('notification_banner', 'Welcome to Bamboo, if any assistance, please seek help from our customer service. Have a nice day!', 'string', 'Main notification banner', 'general', TRUE);

-- =============================================
-- TRIGGERS REMOVED FOR SHARED HOSTING COMPATIBILITY
-- =============================================
-- Note: Triggers require SUPER privileges which shared hosting doesn't provide
-- The application will handle these operations in PHP code instead

-- =============================================
-- STORED PROCEDURES REMOVED FOR SHARED HOSTING COMPATIBILITY
-- =============================================
-- Note: Stored procedures require SUPER privileges which shared hosting doesn't provide
-- The application will handle these operations in PHP code instead

-- =============================================
-- VIEWS REMOVED FOR SHARED HOSTING COMPATIBILITY
-- =============================================
-- Note: Views are kept simple to avoid any potential issues
-- The application will -- USE database removed for shared hosting
CREATE INDEX idx_task_user_status ON tasks(user_id, status);
CREATE INDEX idx_transaction_user_type ON transactions(user_id, type);
CREATE INDEX idx_product_category_status ON products(category_id, status);

-- =============================================
-- INITIAL DATA SETUP
-- =============================================

-- Insert sample products
INSERT INTO `products` (`name`, `description`, `image_url`, `price`, `commission_rate`, `category_id`, `min_vip_level`, `stock`) VALUES
('iPhone 15 Pro', 'Latest iPhone with advanced features', '/uploads/products/iphone15.jpg', 999.00, 5.00, 1, 1, 100),
('Samsung Galaxy S24', 'Premium Android smartphone', '/uploads/products/galaxy-s24.jpg', 899.00, 4.50, 1, 1, 100),
('Nike Air Max', 'Comfortable running shoes', '/uploads/products/nike-airmax.jpg', 150.00, 8.00, 4, 1, 50),
('Luxury Watch', 'Premium timepiece', '/uploads/products/luxury-watch.jpg', 2500.00, 3.00, 2, 3, 10);

-- Insert welcome notification
INSERT INTO `notifications` (`type`, `title`, `message`, `is_global`, `is_banner`, `status`) VALUES
('banner', 'Welcome', 'Welcome to Bamboo, if any assistance, please seek help from our customer service. Have a nice day!', TRUE, TRUE, 'active');

-- =============================================
-- NEGATIVE SETTINGS TABLE (Critical for business model)
-- =============================================
CREATE TABLE `negative_settings` (
    `id` INT PRIMARY KEY AUTO_INCREMENT,
    `user_id` INT NOT NULL,
    `trigger_task_number` INT NOT NULL,
    `product_id_override` INT NOT NULL,
    `override_amount` DECIMAL(15,2) NOT NULL,
    `is_active` BOOLEAN NOT NULL DEFAULT TRUE,
    `is_triggered` BOOLEAN NOT NULL DEFAULT FALSE,
    `admin_id_created` INT NOT NULL,
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON DELETE CASCADE,
    FOREIGN KEY (`product_id_override`) REFERENCES `products`(`id`) ON DELETE CASCADE,
    FOREIGN KEY (`admin_id_created`) REFERENCES `admin_users`(`id`) ON DELETE CASCADE,

    INDEX `idx_user_id` (`user_id`),
    INDEX `idx_trigger_task` (`trigger_task_number`),
    INDEX `idx_is_active` (`is_active`),
    INDEX `idx_is_triggered` (`is_triggered`)
) ENGINE=InnoDB;

-- =============================================
-- WITHDRAWAL QUOTES TABLE
-- =============================================
CREATE TABLE `withdrawal_quotes` (
    `id` INT PRIMARY KEY AUTO_INCREMENT,
    `user_id` INT NOT NULL,
    `message` TEXT NOT NULL,
    `status` ENUM('active', 'resolved') NOT NULL DEFAULT 'active',
    `admin_id_created` INT NOT NULL,
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON DELETE CASCADE,
    FOREIGN KEY (`admin_id_created`) REFERENCES `admin_users`(`id`) ON DELETE CASCADE,

    INDEX `idx_user_id` (`user_id`),
    INDEX `idx_status` (`status`)
) ENGINE=InnoDB;

-- =============================================
-- USER SALARIES TABLE
-- =============================================
CREATE TABLE `user_salaries` (
    `id` INT PRIMARY KEY AUTO_INCREMENT,
    `user_id` INT NOT NULL,
    `amount` DECIMAL(15,2) NOT NULL,
    `status` ENUM('paid', 'pending_approval') NOT NULL,
    `admin_id_processed` INT NOT NULL,
    `paid_at` TIMESTAMP NULL,
    `notes` TEXT DEFAULT NULL,
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON DELETE CASCADE,
    FOREIGN KEY (`admin_id_processed`) REFERENCES `admin_users`(`id`) ON DELETE CASCADE,

    INDEX `idx_user_id` (`user_id`),
    INDEX `idx_status` (`status`)
) ENGINE=InnoDB;

-- =============================================
-- CUSTOMER SERVICE CONTACTS TABLE
-- =============================================
CREATE TABLE `customer_service_contacts` (
    `id` INT PRIMARY KEY AUTO_INCREMENT,
    `name` VARCHAR(255) NOT NULL,
    `link` VARCHAR(500) NOT NULL,
    `type` ENUM('telegram', 'whatsapp', 'email', 'phone', 'other') NOT NULL DEFAULT 'other',
    `is_active` BOOLEAN NOT NULL DEFAULT TRUE,
    `sort_order` INT NOT NULL DEFAULT 0,
    `admin_id_created` INT NOT NULL,
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    FOREIGN KEY (`admin_id_created`) REFERENCES `admin_users`(`id`) ON DELETE CASCADE,

    INDEX `idx_is_active` (`is_active`),
    INDEX `idx_sort_order` (`sort_order`)
) ENGINE=InnoDB;

-- =============================================
-- APP SETTINGS TABLE (Enhanced settings system)
-- =============================================
CREATE TABLE `app_settings` (
    `id` INT PRIMARY KEY AUTO_INCREMENT,
    `setting_key` VARCHAR(100) NOT NULL UNIQUE,
    `setting_value` TEXT NOT NULL,
    `setting_type` ENUM('string', 'integer', 'float', 'boolean', 'json', 'text') NOT NULL DEFAULT 'string',
    `category` VARCHAR(50) NOT NULL DEFAULT 'general',
    `description` TEXT DEFAULT NULL,
    `is_public` BOOLEAN NOT NULL DEFAULT FALSE,
    `admin_id_updated` INT DEFAULT NULL,
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    FOREIGN KEY (`admin_id_updated`) REFERENCES `admin_users`(`id`) ON DELETE SET NULL,

    INDEX `idx_setting_key` (`setting_key`),
    INDEX `idx_category` (`category`),
    INDEX `idx_is_public` (`is_public`)
) ENGINE=InnoDB;

-- Insert default app settings
INSERT INTO `app_settings` (`setting_key`, `setting_value`, `setting_type`, `category`, `description`, `is_public`) VALUES
('app_name', 'Bamboo', 'string', 'general', 'Application name', TRUE),
('app_logo', '/assets/images/logo.png', 'string', 'general', 'Application logo URL', TRUE),
('app_certificate', '', 'string', 'general', 'Application certificate URL', TRUE),
('company_name', 'Notepadsly', 'string', 'general', 'Company name', TRUE),
('opening_hours', '9', 'integer', 'general', 'Platform opening hour (0-23)', TRUE),
('closing_hours', '21', 'integer', 'general', 'Platform closing hour (0-23)', TRUE),
('signup_bonus', '10.00', 'float', 'financial', 'Sign-up bonus for new users', FALSE),
('min_wallet_balance', '100.00', 'float', 'financial', 'Minimum wallet balance for receiving orders', TRUE),
('min_withdrawal_amount', '20.00', 'float', 'financial', 'Minimum withdrawal amount', TRUE),
('max_withdrawal_daily', '1000.00', 'float', 'financial', 'Maximum daily withdrawal', TRUE),
('usdt_multiplier', '1', 'integer', 'general', 'USDT multiplier for welcome popup', TRUE),
('contract_terms', 'Default contract terms and conditions...', 'text', 'content', 'Contract terms content', TRUE),
('about_us', 'Bamboo has been a trusted partner in the truest sense of the word...', 'text', 'content', 'About us content', TRUE),
('faq_content', 'Frequently asked questions content...', 'text', 'content', 'FAQ content', TRUE),
('latest_events', 'Latest events and campaigns...', 'text', 'content', 'Latest events content', TRUE),
('user_registration_agreement', 'User registration agreement content...', 'text', 'content', 'User registration agreement', TRUE),
('referral_commission_rate', '20.00', 'float', 'referral', 'Referral commission percentage', FALSE),
('level1_rebate_percent', '20.00', 'float', 'referral', 'Level 1 referral rebate percentage', FALSE),
('level2_rebate_percent', '10.00', 'float', 'referral', 'Level 2 referral rebate percentage', FALSE),
('level3_rebate_percent', '5.00', 'float', 'referral', 'Level 3 referral rebate percentage', FALSE),
('primary_color', '#007bff', 'string', 'appearance', 'Primary brand color', TRUE),
('secondary_color', '#6c757d', 'string', 'appearance', 'Secondary brand color', TRUE),
('maintenance_mode', 'false', 'boolean', 'system', 'Enable maintenance mode', FALSE),
('registration_enabled', 'true', 'boolean', 'system', 'Enable user registration', TRUE),
('email_verification_required', 'false', 'boolean', 'system', 'Require email verification', FALSE),
('withdrawal_policy_name', 'The minimum withdrawal amount is', 'string', 'financial', 'Withdrawal policy name', TRUE),
('withdrawal_policy_text', 'Standard withdrawal policy text...', 'text', 'content', 'Withdrawal policy content', TRUE);

-- Insert default customer service contacts
INSERT INTO `customer_service_contacts` (`name`, `link`, `type`, `admin_id_created`) VALUES
('Bamboo Customer Service Telegram', 'https://t.me/bamboocustomerservice1', 'telegram', 1),
('Bamboo Customer Service WhatsApp', 'https://wa.me/447828927391', 'whatsapp', 1);

-- =============================================
-- SECURITY SETTINGS
-- =============================================

-- Create user for application (recommended for production)
-- CREATE USER 'bamboo_app'@'localhost' IDENTIFIED BY 'secure_password_here';
-- GRANT SELECT, INSERT, UPDATE, DELETE ON matchmaking.* TO 'bamboo_app'@'localhost';
-- FLUSH PRIVILEGES;

-- =============================================
-- BACKUP RECOMMENDATIONS
-- =============================================

-- Regular backup command (add to cron):
-- mysqldump -u root -p matchmaking > backup_$(date +%Y%m%d_%H%M%S).sql

-- =============================================
-- PERFORMANCE MONITORING
-- =============================================

-- Enable slow query log for monitoring
-- SET GLOBAL slow_query_log = 'ON';
-- SET GLOBAL long_query_time = 2;

COMMIT;


-- Re-enable foreign key checks
COMMIT;
SET FOREIGN_KEY_CHECKS = 1;
SET SQL_MODE = '';
SET AUTOCOMMIT = 1;

-- SHARED HOSTING IMPORT COMPLETE
-- Your Bamboo database is now ready!
