<?php
/**
 * Bamboo Web Application - Manual Installation Helper
 * Company: Notepadsly
 * Version: 2.0
 *
 * Simple installation helper for manual deployment:
 * 1. You extract production.zip manually
 * 2. You import database_migration.sql manually
 * 3. This script updates config.php with your database credentials
 * 4. Verifies everything works
 */

// No session needed for simple approach
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Installation steps - simplified
$steps = [
    1 => 'File Check & Database Config',
    2 => 'Update Configuration',
    3 => 'Verification & Completion'
];

$current_step = isset($_GET['step']) ? (int)$_GET['step'] : 1;
$current_step = max(1, min(3, $current_step));

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Bamboo Installation - Step <?php echo $current_step; ?></title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { 
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; 
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        .container { 
            max-width: 800px; 
            margin: 0 auto; 
            background: white; 
            border-radius: 10px; 
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            overflow: hidden;
        }
        .header { 
            background: #2c3e50; 
            color: white; 
            padding: 30px; 
            text-align: center; 
        }
        .header h1 { font-size: 2.5em; margin-bottom: 10px; }
        .header p { opacity: 0.9; font-size: 1.1em; }
        .progress { 
            background: #34495e; 
            height: 4px; 
            position: relative; 
        }
        .progress-bar { 
            background: #3498db; 
            height: 100%; 
            width: <?php echo ($current_step / 5) * 100; ?>%; 
            transition: width 0.3s ease; 
        }
        .content { padding: 40px; }
        .step-indicator { 
            display: flex; 
            justify-content: space-between; 
            margin-bottom: 30px; 
            padding: 0 20px;
        }
        .step { 
            flex: 1; 
            text-align: center; 
            padding: 10px; 
            border-radius: 5px; 
            margin: 0 5px;
            font-size: 0.9em;
        }
        .step.active { background: #3498db; color: white; }
        .step.completed { background: #27ae60; color: white; }
        .step.pending { background: #ecf0f1; color: #7f8c8d; }
        .form-group { margin-bottom: 20px; }
        .form-group label { 
            display: block; 
            margin-bottom: 8px; 
            font-weight: 600; 
            color: #2c3e50; 
        }
        .form-group input, .form-group select, .form-group textarea { 
            width: 100%; 
            padding: 12px; 
            border: 2px solid #ddd; 
            border-radius: 5px; 
            font-size: 16px;
            transition: border-color 0.3s ease;
        }
        .form-group input:focus, .form-group select:focus, .form-group textarea:focus { 
            outline: none; 
            border-color: #3498db; 
        }
        .btn { 
            background: #3498db; 
            color: white; 
            padding: 12px 30px; 
            border: none; 
            border-radius: 5px; 
            cursor: pointer; 
            font-size: 16px;
            text-decoration: none;
            display: inline-block;
            transition: background 0.3s ease;
        }
        .btn:hover { background: #2980b9; }
        .btn-success { background: #27ae60; }
        .btn-success:hover { background: #229954; }
        .btn-warning { background: #f39c12; }
        .btn-warning:hover { background: #e67e22; }
        .alert { 
            padding: 15px; 
            margin-bottom: 20px; 
            border-radius: 5px; 
            border-left: 4px solid;
        }
        .alert-success { background: #d4edda; border-color: #27ae60; color: #155724; }
        .alert-error { background: #f8d7da; border-color: #e74c3c; color: #721c24; }
        .alert-warning { background: #fff3cd; border-color: #f39c12; color: #856404; }
        .alert-info { background: #d1ecf1; border-color: #3498db; color: #0c5460; }
        .requirements { display: grid; grid-template-columns: 1fr 1fr; gap: 20px; }
        .requirement { 
            padding: 15px; 
            border-radius: 5px; 
            border: 2px solid #ddd;
        }
        .requirement.pass { border-color: #27ae60; background: #d4edda; }
        .requirement.fail { border-color: #e74c3c; background: #f8d7da; }
        .installation-options { display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin: 20px 0; }
        .option-card { 
            border: 2px solid #ddd; 
            border-radius: 10px; 
            padding: 20px; 
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .option-card:hover { border-color: #3498db; transform: translateY(-2px); }
        .option-card.selected { border-color: #3498db; background: #ebf3fd; }
        .footer { 
            background: #ecf0f1; 
            padding: 20px 40px; 
            text-align: right; 
            border-top: 1px solid #ddd;
        }
        .loading { 
            display: none; 
            text-align: center; 
            padding: 20px; 
        }
        .spinner { 
            border: 4px solid #f3f3f3; 
            border-top: 4px solid #3498db; 
            border-radius: 50%; 
            width: 40px; 
            height: 40px; 
            animation: spin 1s linear infinite; 
            margin: 0 auto 20px;
        }
        @keyframes spin { 0% { transform: rotate(0deg); } 100% { transform: rotate(360deg); } }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎋 Bamboo Installation</h1>
            <p>Production Deployment System</p>
        </div>
        <div class="progress">
            <div class="progress-bar"></div>
        </div>
        
        <div class="content">
            <div class="step-indicator">
                <?php foreach ($steps as $num => $title): ?>
                    <div class="step <?php
                        if ($num < $current_step) echo 'completed';
                        elseif ($num == $current_step) echo 'active';
                        else echo 'pending';
                    ?>">
                        <?php echo $num; ?>. <?php echo $title; ?>
                    </div>
                <?php endforeach; ?>
            </div>

            <?php
            // Handle form submissions and step logic
            if (isset($_SERVER['REQUEST_METHOD']) && $_SERVER['REQUEST_METHOD'] === 'POST') {
                handleFormSubmission();
            }

            // Display current step content
            switch ($current_step) {
                case 1:
                    displayFileCheckStep();
                    break;
                case 2:
                    displayConfigUpdateStep();
                    break;
                case 3:
                    displayCompletionStep();
                    break;
            }
            ?>
        </div>
        
        <div class="footer">
            <?php if ($current_step > 1): ?>
                <a href="?step=<?php echo $current_step - 1; ?>" class="btn btn-warning">← Previous</a>
            <?php endif; ?>

            <?php if ($current_step < 3): ?>
                <button type="submit" form="step-form" class="btn">Next →</button>
            <?php endif; ?>
        </div>
    </div>

    <script>
        // Simple JavaScript for form handling
        function testDatabaseConnection() {
            const formData = new FormData(document.getElementById('step-form'));
            formData.append('action', 'test_connection');

            document.getElementById('test-result').innerHTML = '<div class="loading"><div class="spinner"></div>Testing connection...</div>';

            fetch('install.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                const resultDiv = document.getElementById('test-result');
                if (data.success) {
                    resultDiv.innerHTML = '<div class="alert alert-success">✅ Database connection successful!</div>';
                } else {
                    resultDiv.innerHTML = '<div class="alert alert-error">❌ Connection failed: ' + data.error + '</div>';
                }
            })
            .catch(error => {
                document.getElementById('test-result').innerHTML = '<div class="alert alert-error">❌ Test failed: ' + error.message + '</div>';
            });
        }
    </script>
</body>
</html>

<?php
// Simple PHP Functions for manual installation
function handleFormSubmission() {
    // Handle AJAX requests for database testing
    if (isset($_POST['action']) && $_POST['action'] === 'test_connection') {
        header('Content-Type: application/json');
        echo json_encode(testDatabaseConnection($_POST));
        exit;
    }

    // Handle step progression
    $step = isset($_POST['step']) ? (int)$_POST['step'] : 1;

    switch ($step) {
        case 1:
            // File check and database config, go to step 2
            if (saveDatabaseConfig($_POST)) {
                echo '<script>window.location.href = "install.php?step=2";</script>';
            }
            break;
        case 2:
            // Configuration updated, go to completion
            echo '<script>window.location.href = "install.php?step=3";</script>';
            break;
    }
}

function displayFileCheckStep() {
    ?>
    <h2>🔍 Manual Installation Helper</h2>
    <p>This simple tool helps you complete your manual Bamboo installation.</p>

    <div class="alert alert-info">
        <strong>Manual Process:</strong> You extract files and import database manually, this tool just updates the configuration.
    </div>

    <h3>📋 Step 1: Check Files Extracted</h3>
    <?php
    $required_files = [
        '../admin/index.php' => 'Admin panel',
        '../user/login/login.php' => 'User application',
        '../includes/config.php' => 'Configuration file',
        '../assets/css/main.css' => 'Assets',
        '../api/csrf-token.php' => 'API files'
    ];

    $files_ok = true;
    echo '<div class="requirements">';
    foreach ($required_files as $file => $desc) {
        $exists = file_exists($file);
        $files_ok = $files_ok && $exists;
        echo '<div class="requirement ' . ($exists ? 'pass' : 'fail') . '">';
        echo '<strong>' . $desc . '</strong><br>';
        echo $exists ? '✅ Found' : '❌ Missing';
        echo '</div>';
    }
    echo '</div>';
    ?>

    <h3>💾 Step 2: Enter Your Database Credentials</h3>
    <form id="step-form" method="post">
        <input type="hidden" name="step" value="1">

        <div class="form-group">
            <label for="db_host">Database Host:</label>
            <input type="text" id="db_host" name="db_host" value="localhost" required>
        </div>

        <div class="form-group">
            <label for="db_name">Database Name:</label>
            <input type="text" id="db_name" name="db_name" required>
        </div>

        <div class="form-group">
            <label for="db_user">Database Username:</label>
            <input type="text" id="db_user" name="db_user" required>
        </div>

        <div class="form-group">
            <label for="db_pass">Database Password:</label>
            <input type="password" id="db_pass" name="db_pass">
        </div>

        <div class="form-group">
            <label for="db_port">Database Port:</label>
            <input type="number" id="db_port" name="db_port" value="3306" required>
        </div>

        <button type="button" onclick="testDatabaseConnection()" class="btn btn-warning">Test Connection</button>
        <div id="test-result"></div>

        <?php if ($files_ok): ?>
            <div class="alert alert-success" style="margin-top: 20px;">
                <strong>✅ Files Look Good!</strong> Ready to update configuration.
            </div>
        <?php else: ?>
            <div class="alert alert-error" style="margin-top: 20px;">
                <strong>❌ Missing Files!</strong> Please extract production.zip first.
            </div>
        <?php endif; ?>
    </form>
    <?php
}

function displayConfigUpdateStep() {
    ?>
    <h2>✅ Configuration Updated!</h2>
    <p>Your database configuration has been saved to the config.php file.</p>

    <div class="alert alert-success">
        <strong>Configuration Complete!</strong> The config.php file has been updated with your database credentials.
    </div>

    <h3>📋 What You Need to Do:</h3>
    <ol>
        <li><strong>Import Database:</strong> Import the <code>database_migration.sql</code> file into your database using phpMyAdmin or your hosting control panel</li>
        <li><strong>Test Connection:</strong> Make sure your database import was successful</li>
        <li><strong>Access Application:</strong> Your Bamboo application should now be ready</li>
    </ol>

    <div class="alert alert-warning">
        <strong>Important:</strong> Make sure you have imported the database_migration.sql file before proceeding to the final step.
    </div>

    <form id="step-form" method="post">
        <input type="hidden" name="step" value="2">
        <div style="text-align: center;">
            <p>Click Next when you have imported the database_migration.sql file</p>
        </div>
    </form>
    <?php
}

function displayCompletionStep() {
    ?>
    <h2>🎉 Installation Complete!</h2>

    <div class="alert alert-success">
        <strong>Congratulations!</strong> Your Bamboo application should now be ready to use.
    </div>

    <h3>🚀 Access Your Application:</h3>
    <div style="text-align: center; margin: 30px 0;">
        <a href="../admin/login/" class="btn btn-success" target="_blank">🔧 Admin Panel</a>
        <a href="../user/login/" class="btn" target="_blank">👤 User Login</a>
    </div>

    <h3>🔑 Default Admin Credentials:</h3>
    <div class="alert alert-info">
        <strong>Username:</strong> admin<br>
        <strong>Password:</strong> admin123<br>
        <strong>⚠️ CHANGE THIS PASSWORD IMMEDIATELY!</strong>
    </div>

    <h3>🔒 Security Steps:</h3>
    <ol>
        <li><strong>Delete Install Folder:</strong> Remove this entire install folder for security</li>
        <li><strong>Change Admin Password:</strong> Login and change the default password</li>
        <li><strong>Configure Settings:</strong> Review app settings in the admin panel</li>
        <li><strong>Test Everything:</strong> Test user registration and core functionality</li>
    </ol>

    <div class="alert alert-warning">
        <strong>Important:</strong> Delete this install folder immediately after confirming everything works!
    </div>

    <div style="text-align: center; margin-top: 30px;">
        <a href="../" class="btn">🏠 Go to Application</a>
    </div>
    <?php
}

function displayInstallationStep() {
    $method = $_SESSION['installation_method'] ?? 'automatic';
    ?>
    <h2>Installing Bamboo Application</h2>

    <?php if ($method === 'automatic'): ?>
        <div class="alert alert-info">
            <strong>Automatic Installation:</strong> The system will extract files and import the database automatically.
        </div>

        <div id="installation-progress">
            <div class="loading">
                <div class="spinner"></div>
                <p>Installing Bamboo application...</p>
            </div>
        </div>

        <script>
            // Simulate installation progress
            setTimeout(() => {
                document.getElementById('installation-progress').innerHTML =
                    '<div class="alert alert-success">✅ Installation completed successfully!</div>' +
                    '<form method="post"><input type="hidden" name="step" value="4"><button type="submit" class="btn">Continue to Completion →</button></form>';
            }, 3000);
        </script>

    <?php else: ?>
        <div class="alert alert-warning">
            <strong>Manual Installation:</strong> Please follow these steps carefully.
        </div>

        <h3>Step 1: Extract Files</h3>
        <p>Extract the production.zip file to your web server's document root.</p>

        <h3>Step 2: Database Import</h3>
        <p>Import the database_migration.sql file into your database.</p>

        <div class="form-group">
            <label>
                <input type="checkbox" id="files-extracted"> Files have been extracted
            </label>
        </div>

        <div class="form-group">
            <label>
                <input type="checkbox" id="database-imported"> Database has been imported
            </label>
        </div>

        <form id="step-form" method="post">
            <input type="hidden" name="step" value="4">
            <button type="submit" class="btn" id="continue-btn" disabled>Verify Installation →</button>
        </form>

        <script>
            document.getElementById('files-extracted').addEventListener('change', checkCompletion);
            document.getElementById('database-imported').addEventListener('change', checkCompletion);

            function checkCompletion() {
                const filesExtracted = document.getElementById('files-extracted').checked;
                const databaseImported = document.getElementById('database-imported').checked;
                document.getElementById('continue-btn').disabled = !(filesExtracted && databaseImported);
            }
        </script>
    <?php endif; ?>
    <?php
}



function testDatabaseConnection($data) {
    try {
        $dsn = "mysql:host={$data['db_host']};port={$data['db_port']};dbname={$data['db_name']};charset=utf8mb4";
        $pdo = new PDO($dsn, $data['db_user'], $data['db_pass'], [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
        ]);

        // Test with a simple query
        $pdo->query("SELECT 1");

        return ['success' => true];
    } catch (PDOException $e) {
        return ['success' => false, 'error' => $e->getMessage()];
    }
}

function saveDatabaseConfig($data) {
    $config_file = '../includes/config.php';

    if (!file_exists($config_file)) {
        return false;
    }

    $config_content = file_get_contents($config_file);

    // Simple replacements - find current values and replace them
    $replacements = [
        "define('DB_HOST', 'localhost');" => "define('DB_HOST', '{$data['db_host']}');",
        "define('DB_NAME', 'your_production_db');" => "define('DB_NAME', '{$data['db_name']}');",
        "define('DB_USER', 'your_db_user');" => "define('DB_USER', '{$data['db_user']}');",
        "define('DB_PASS', 'your_db_password');" => "define('DB_PASS', '{$data['db_pass']}');",
        "define('DB_PORT', '3306');" => "define('DB_PORT', '{$data['db_port']}');"
    ];

    foreach ($replacements as $search => $replace) {
        $config_content = str_replace($search, $replace, $config_content);
    }

    return file_put_contents($config_file, $config_content) !== false;
}

?>
