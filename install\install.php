<?php
/**
 * Bamboo Web Application - Installation Script
 * Company: Notepadsly
 * Version: 1.0
 *
 * This script handles the complete installation of the Bamboo application
 * including database setup, file extraction, and configuration.
 */

// Start session only if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Handle form submissions FIRST before any output
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    handleFormSubmission();
}

// Security check - remove this file after installation
if (file_exists('../includes/config.php') && !isset($_GET['force'])) {
    $config_content = file_get_contents('../includes/config.php');
    if (strpos($config_content, 'your_production_db') === false) {
        die('<h1>Installation Already Complete</h1><p>The application appears to be already installed. If you need to reinstall, add ?force=1 to the URL.</p>');
    }
}

// Installation steps
$steps = [
    1 => 'Welcome & Requirements Check',
    2 => 'Database Configuration',
    3 => 'Installation Method Selection',
    4 => 'File Extraction & Database Import',
    5 => 'Verification & Completion'
];

$current_step = isset($_GET['step']) ? (int)$_GET['step'] : 1;
$current_step = max(1, min(5, $current_step));

// PHP Functions for handling installation logic - MOVED TO TOP
function handleFormSubmission() {
    // Handle AJAX requests
    if (isset($_POST['action']) && $_POST['action'] === 'test_connection') {
        header('Content-Type: application/json');
        echo json_encode(testDatabaseConnection($_POST));
        exit;
    }

    // Handle step progression
    $step = isset($_POST['step']) ? (int)$_POST['step'] : 1;

    switch ($step) {
        case 1:
            // Requirements check passed, go to step 2
            header('Location: install.php?step=2');
            exit;
        case 2:
            // Database configuration submitted
            if (saveDatabaseConfig($_POST)) {
                header('Location: install.php?step=3');
            }
            exit;
        case 3:
            // Installation method selected
            $_SESSION['installation_method'] = $_POST['installation_method'];
            header('Location: install.php?step=4');
            exit;
        case 4:
            // Perform installation
            if (performInstallation()) {
                header('Location: install.php?step=5');
            }
            exit;
    }
}

function testDatabaseConnection($data) {
    try {
        $dsn = "mysql:host={$data['db_host']};port={$data['db_port']};dbname={$data['db_name']};charset=utf8mb4";
        $pdo = new PDO($dsn, $data['db_user'], $data['db_pass'], [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
        ]);

        // Test with a simple query
        $pdo->query("SELECT 1");

        return ['success' => true];
    } catch (PDOException $e) {
        return ['success' => false, 'error' => $e->getMessage()];
    }
}

function saveDatabaseConfig($data) {
    $config_file = '../includes/config.php';

    if (!file_exists($config_file)) {
        return false;
    }

    $config_content = file_get_contents($config_file);

    // Replace database configuration
    $replacements = [
        "define('DB_HOST', 'localhost');" => "define('DB_HOST', '{$data['db_host']}');",
        "define('DB_NAME', 'your_production_db');" => "define('DB_NAME', '{$data['db_name']}');",
        "define('DB_USER', 'your_db_user');" => "define('DB_USER', '{$data['db_user']}');",
        "define('DB_PASS', 'your_db_password');" => "define('DB_PASS', '{$data['db_pass']}');",
        "define('DB_PORT', '3306');" => "define('DB_PORT', '{$data['db_port']}');"
    ];

    foreach ($replacements as $search => $replace) {
        $config_content = str_replace($search, $replace, $config_content);
    }

    return file_put_contents($config_file, $config_content) !== false;
}

function performInstallation() {
    $method = $_SESSION['installation_method'] ?? 'automatic';

    if ($method === 'automatic') {
        // Extract production.zip if it exists
        $zip_file = 'production.zip';
        if (file_exists($zip_file)) {
            $zip = new ZipArchive;
            if ($zip->open($zip_file) === TRUE) {
                $zip->extractTo('../');
                $zip->close();
            }
        }

        // Import database
        importDatabase();
    }

    // Verify installation
    return verifyInstallation();
}

function importDatabase() {
    $sql_file = 'database_migration.sql';

    if (!file_exists($sql_file)) {
        return false;
    }

    try {
        // Get database connection from config
        require_once '../includes/config.php';

        $dsn = "mysql:host=" . DB_HOST . ";port=" . DB_PORT . ";dbname=" . DB_NAME . ";charset=utf8mb4";
        $pdo = new PDO($dsn, DB_USER, DB_PASS, [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION
        ]);

        // Read and execute SQL file
        $sql = file_get_contents($sql_file);
        $pdo->exec($sql);

        return true;
    } catch (Exception $e) {
        error_log("Database import failed: " . $e->getMessage());
        return false;
    }
}

function verifyInstallation() {
    // Check if essential files exist
    $required_files = [
        '../includes/config.php',
        '../includes/database.php',
        '../admin/index.php',
        '../assets/css/main.css'
    ];

    foreach ($required_files as $file) {
        if (!file_exists($file)) {
            return false;
        }
    }

    // Check database connection
    try {
        require_once '../includes/config.php';
        $dsn = "mysql:host=" . DB_HOST . ";port=" . DB_PORT . ";dbname=" . DB_NAME . ";charset=utf8mb4";
        $pdo = new PDO($dsn, DB_USER, DB_PASS);

        // Check if admin_users table exists and has data
        $stmt = $pdo->query("SELECT COUNT(*) FROM admin_users");
        return $stmt->fetchColumn() > 0;
    } catch (Exception $e) {
        return false;
    }
}

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Bamboo Installation - Step <?php echo $current_step; ?></title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { 
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; 
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        .container { 
            max-width: 800px; 
            margin: 0 auto; 
            background: white; 
            border-radius: 10px; 
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            overflow: hidden;
        }
        .header { 
            background: #2c3e50; 
            color: white; 
            padding: 30px; 
            text-align: center; 
        }
        .header h1 { font-size: 2.5em; margin-bottom: 10px; }
        .header p { opacity: 0.9; font-size: 1.1em; }
        .progress { 
            background: #34495e; 
            height: 4px; 
            position: relative; 
        }
        .progress-bar { 
            background: #3498db; 
            height: 100%; 
            width: <?php echo ($current_step / 5) * 100; ?>%; 
            transition: width 0.3s ease; 
        }
        .content { padding: 40px; }
        .step-indicator { 
            display: flex; 
            justify-content: space-between; 
            margin-bottom: 30px; 
            padding: 0 20px;
        }
        .step { 
            flex: 1; 
            text-align: center; 
            padding: 10px; 
            border-radius: 5px; 
            margin: 0 5px;
            font-size: 0.9em;
        }
        .step.active { background: #3498db; color: white; }
        .step.completed { background: #27ae60; color: white; }
        .step.pending { background: #ecf0f1; color: #7f8c8d; }
        .form-group { margin-bottom: 20px; }
        .form-group label { 
            display: block; 
            margin-bottom: 8px; 
            font-weight: 600; 
            color: #2c3e50; 
        }
        .form-group input, .form-group select, .form-group textarea { 
            width: 100%; 
            padding: 12px; 
            border: 2px solid #ddd; 
            border-radius: 5px; 
            font-size: 16px;
            transition: border-color 0.3s ease;
        }
        .form-group input:focus, .form-group select:focus, .form-group textarea:focus { 
            outline: none; 
            border-color: #3498db; 
        }
        .btn { 
            background: #3498db; 
            color: white; 
            padding: 12px 30px; 
            border: none; 
            border-radius: 5px; 
            cursor: pointer; 
            font-size: 16px;
            text-decoration: none;
            display: inline-block;
            transition: background 0.3s ease;
        }
        .btn:hover { background: #2980b9; }
        .btn-success { background: #27ae60; }
        .btn-success:hover { background: #229954; }
        .btn-warning { background: #f39c12; }
        .btn-warning:hover { background: #e67e22; }
        .alert { 
            padding: 15px; 
            margin-bottom: 20px; 
            border-radius: 5px; 
            border-left: 4px solid;
        }
        .alert-success { background: #d4edda; border-color: #27ae60; color: #155724; }
        .alert-error { background: #f8d7da; border-color: #e74c3c; color: #721c24; }
        .alert-warning { background: #fff3cd; border-color: #f39c12; color: #856404; }
        .alert-info { background: #d1ecf1; border-color: #3498db; color: #0c5460; }
        .requirements { display: grid; grid-template-columns: 1fr 1fr; gap: 20px; }
        .requirement { 
            padding: 15px; 
            border-radius: 5px; 
            border: 2px solid #ddd;
        }
        .requirement.pass { border-color: #27ae60; background: #d4edda; }
        .requirement.fail { border-color: #e74c3c; background: #f8d7da; }
        .installation-options { display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin: 20px 0; }
        .option-card { 
            border: 2px solid #ddd; 
            border-radius: 10px; 
            padding: 20px; 
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .option-card:hover { border-color: #3498db; transform: translateY(-2px); }
        .option-card.selected { border-color: #3498db; background: #ebf3fd; }
        .footer { 
            background: #ecf0f1; 
            padding: 20px 40px; 
            text-align: right; 
            border-top: 1px solid #ddd;
        }
        .loading { 
            display: none; 
            text-align: center; 
            padding: 20px; 
        }
        .spinner { 
            border: 4px solid #f3f3f3; 
            border-top: 4px solid #3498db; 
            border-radius: 50%; 
            width: 40px; 
            height: 40px; 
            animation: spin 1s linear infinite; 
            margin: 0 auto 20px;
        }
        @keyframes spin { 0% { transform: rotate(0deg); } 100% { transform: rotate(360deg); } }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎋 Bamboo Installation</h1>
            <p>Production Deployment System</p>
        </div>
        <div class="progress">
            <div class="progress-bar"></div>
        </div>
        
        <div class="content">
            <div class="step-indicator">
                <?php foreach ($steps as $num => $title): ?>
                    <div class="step <?php 
                        if ($num < $current_step) echo 'completed';
                        elseif ($num == $current_step) echo 'active';
                        else echo 'pending';
                    ?>">
                        <?php echo $num; ?>. <?php echo $title; ?>
                    </div>
                <?php endforeach; ?>
            </div>

            <?php
            // Handle form submissions and step logic
            if ($_SERVER['REQUEST_METHOD'] === 'POST') {
                handleFormSubmission();
            }

            // Display current step content
            switch ($current_step) {
                case 1:
                    displayWelcomeStep();
                    break;
                case 2:
                    displayDatabaseStep();
                    break;
                case 3:
                    displayInstallationMethodStep();
                    break;
                case 4:
                    displayInstallationStep();
                    break;
                case 5:
                    displayCompletionStep();
                    break;
            }
            ?>
        </div>
        
        <div class="footer">
            <?php if ($current_step > 1): ?>
                <a href="?step=<?php echo $current_step - 1; ?>" class="btn btn-warning">← Previous</a>
            <?php endif; ?>
            
            <?php if ($current_step < 5): ?>
                <button type="submit" form="step-form" class="btn">Next →</button>
            <?php endif; ?>
        </div>
    </div>

    <script>
        // JavaScript for enhanced user experience
        function selectInstallationMethod(method) {
            document.querySelectorAll('.option-card').forEach(card => {
                card.classList.remove('selected');
            });
            event.target.closest('.option-card').classList.add('selected');
            document.getElementById('installation_method').value = method;
        }

        function testDatabaseConnection() {
            const formData = new FormData(document.getElementById('step-form'));
            formData.append('action', 'test_connection');
            
            document.getElementById('test-result').innerHTML = '<div class="loading"><div class="spinner"></div>Testing connection...</div>';
            
            fetch('install.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                const resultDiv = document.getElementById('test-result');
                if (data.success) {
                    resultDiv.innerHTML = '<div class="alert alert-success">✅ Database connection successful!</div>';
                } else {
                    resultDiv.innerHTML = '<div class="alert alert-error">❌ Connection failed: ' + data.error + '</div>';
                }
            })
            .catch(error => {
                document.getElementById('test-result').innerHTML = '<div class="alert alert-error">❌ Test failed: ' + error.message + '</div>';
            });
        }
    </script>

<?php
// Display functions
function displayWelcomeStep() {
    ?>
    <h2>Welcome to Bamboo Installation</h2>
    <p>This installer will help you deploy the Bamboo task management platform to your production server.</p>

    <div class="alert alert-info">
        <strong>Before you begin:</strong> Make sure you have your database credentials ready and that your hosting environment meets the requirements.
    </div>

    <h3>System Requirements Check</h3>
    <div class="requirements">
        <?php
        $requirements = [
            'PHP Version (8.0+)' => version_compare(PHP_VERSION, '8.0.0', '>='),
            'MySQL Extension' => extension_loaded('pdo_mysql'),
            'JSON Extension' => extension_loaded('json'),
            'File Uploads' => ini_get('file_uploads'),
            'Write Permissions' => is_writable(dirname(__DIR__)),
            'Memory Limit (128M+)' => (int)ini_get('memory_limit') >= 128
        ];

        $all_passed = true;
        foreach ($requirements as $name => $passed) {
            $all_passed = $all_passed && $passed;
            echo '<div class="requirement ' . ($passed ? 'pass' : 'fail') . '">';
            echo '<strong>' . $name . '</strong><br>';
            echo $passed ? '✅ Passed' : '❌ Failed';
            echo '</div>';
        }
        ?>
    </div>

    <?php if (!$all_passed): ?>
        <div class="alert alert-error">
            <strong>Requirements Not Met:</strong> Please fix the failed requirements before continuing.
        </div>
    <?php else: ?>
        <div class="alert alert-success">
            <strong>All Requirements Met:</strong> Your server is ready for Bamboo installation.
        </div>

        <form id="step-form" method="post">
            <input type="hidden" name="step" value="1">
        </form>
    <?php endif; ?>
    <?php
}

function displayDatabaseStep() {
    ?>
    <h2>Database Configuration</h2>
    <p>Enter your production database credentials. The installer will test the connection and create the necessary tables.</p>

    <form id="step-form" method="post">
        <input type="hidden" name="step" value="2">

        <div class="form-group">
            <label for="db_host">Database Host:</label>
            <input type="text" id="db_host" name="db_host" value="localhost" required>
        </div>

        <div class="form-group">
            <label for="db_name">Database Name:</label>
            <input type="text" id="db_name" name="db_name" required>
        </div>

        <div class="form-group">
            <label for="db_user">Database Username:</label>
            <input type="text" id="db_user" name="db_user" required>
        </div>

        <div class="form-group">
            <label for="db_pass">Database Password:</label>
            <input type="password" id="db_pass" name="db_pass">
        </div>

        <div class="form-group">
            <label for="db_port">Database Port:</label>
            <input type="number" id="db_port" name="db_port" value="3306" required>
        </div>

        <button type="button" onclick="testDatabaseConnection()" class="btn btn-warning">Test Connection</button>
        <div id="test-result"></div>
    </form>
    <?php
}

function displayInstallationMethodStep() {
    ?>
    <h2>Installation Method</h2>
    <p>Choose how you want to install the Bamboo application:</p>

    <form id="step-form" method="post">
        <input type="hidden" name="step" value="3">
        <input type="hidden" id="installation_method" name="installation_method" value="">

        <div class="installation-options">
            <div class="option-card" onclick="selectInstallationMethod('automatic')">
                <h3>🚀 Automatic Installation</h3>
                <p>Extract production.zip and import database automatically</p>
                <ul style="text-align: left; margin-top: 15px;">
                    <li>Extracts all files automatically</li>
                    <li>Imports database schema</li>
                    <li>Configures settings</li>
                    <li>Ready to use immediately</li>
                </ul>
            </div>

            <div class="option-card" onclick="selectInstallationMethod('manual')">
                <h3>🔧 Manual Installation</h3>
                <p>Manual file extraction with guided database import</p>
                <ul style="text-align: left; margin-top: 15px;">
                    <li>You extract production.zip manually</li>
                    <li>Guided database import process</li>
                    <li>Step-by-step verification</li>
                    <li>More control over the process</li>
                </ul>
            </div>
        </div>

        <div class="alert alert-warning">
            <strong>Note:</strong> Make sure production.zip is present in the install folder before proceeding.
        </div>
    </form>
    <?php
}

function displayInstallationStep() {
    $method = $_SESSION['installation_method'] ?? 'automatic';
    ?>
    <h2>Installing Bamboo Application</h2>

    <?php if ($method === 'automatic'): ?>
        <div class="alert alert-info">
            <strong>Automatic Installation:</strong> The system will extract files and import the database automatically.
        </div>

        <div id="installation-progress">
            <div class="loading">
                <div class="spinner"></div>
                <p>Installing Bamboo application...</p>
            </div>
        </div>

        <script>
            // Simulate installation progress
            setTimeout(() => {
                document.getElementById('installation-progress').innerHTML =
                    '<div class="alert alert-success">✅ Installation completed successfully!</div>' +
                    '<form method="post"><input type="hidden" name="step" value="4"><button type="submit" class="btn">Continue to Completion →</button></form>';
            }, 3000);
        </script>

    <?php else: ?>
        <div class="alert alert-warning">
            <strong>Manual Installation:</strong> Please follow these steps carefully.
        </div>

        <h3>Step 1: Extract Files</h3>
        <p>Extract the production.zip file to your web server's document root.</p>

        <h3>Step 2: Database Import</h3>
        <p>Import the database_migration.sql file into your database.</p>

        <div class="form-group">
            <label>
                <input type="checkbox" id="files-extracted"> Files have been extracted
            </label>
        </div>

        <div class="form-group">
            <label>
                <input type="checkbox" id="database-imported"> Database has been imported
            </label>
        </div>

        <form id="step-form" method="post">
            <input type="hidden" name="step" value="4">
            <button type="submit" class="btn" id="continue-btn" disabled>Verify Installation →</button>
        </form>

        <script>
            document.getElementById('files-extracted').addEventListener('change', checkCompletion);
            document.getElementById('database-imported').addEventListener('change', checkCompletion);

            function checkCompletion() {
                const filesExtracted = document.getElementById('files-extracted').checked;
                const databaseImported = document.getElementById('database-imported').checked;
                document.getElementById('continue-btn').disabled = !(filesExtracted && databaseImported);
            }
        </script>
    <?php endif; ?>
    <?php
}

function displayCompletionStep() {
    ?>
    <h2>🎉 Installation Complete!</h2>

    <div class="alert alert-success">
        <strong>Congratulations!</strong> Bamboo has been successfully installed on your server.
    </div>

    <h3>Next Steps:</h3>
    <ol>
        <li><strong>Admin Login:</strong> <a href="../admin/login/" target="_blank">Access Admin Panel</a></li>
        <li><strong>Default Credentials:</strong>
            <ul>
                <li>Username: <code>admin</code></li>
                <li>Password: <code>admin123</code></li>
            </ul>
        </li>
        <li><strong>Security:</strong> Change the default admin password immediately</li>
        <li><strong>Configuration:</strong> Review and update app settings in the admin panel</li>
    </ol>

    <div class="alert alert-warning">
        <strong>Important:</strong> Delete this install folder for security reasons after completing the setup.
    </div>

    <div style="text-align: center; margin-top: 30px;">
        <a href="../admin/login/" class="btn btn-success">Go to Admin Panel →</a>
    </div>
    <?php
}
?>
</body>
</html>
