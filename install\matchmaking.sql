-- p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.1.2
-- https://www.phpmyadmin.net/
--
-- Host: localhost:3306
-- Generation Time: Jul 08, 2025 at 09:33 AM
-- Server version: 5.7.24
-- PHP Version: 8.3.1

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- Database: `matchmaking`
--

DELIMITER $$
--
-- Procedures
--
CREATE DEFINER=`root`@`localhost` PROCEDURE `AssignRandomTask` (IN `p_user_id` INT)   BEGIN
    DECLARE v_user_vip_level INT;
    DECLARE v_user_balance DECIMAL(10,2);
    DECLARE v_tasks_today INT;
    DECLARE v_max_tasks INT;
    DECLARE v_product_id INT;
    DECLARE v_commission DECIMAL(10,2);
    DECLARE v_expires_at TIMESTAMP;
    
    
    SELECT vip_level, balance, tasks_completed_today 
    INTO v_user_vip_level, v_user_balance, v_tasks_today
    FROM users WHERE id = p_user_id;
    
    
    SELECT max_daily_tasks INTO v_max_tasks
    FROM vip_levels WHERE level = v_user_vip_level;
    
    
    IF v_tasks_today >= v_max_tasks THEN
        SIGNAL SQLSTATE '45000' SET MESSAGE_TEXT = 'Daily task limit reached';
    END IF;
    
    
    SELECT p.id, (p.price * p.commission_rate / 100 * vl.commission_multiplier)
    INTO v_product_id, v_commission
    FROM products p
    JOIN vip_levels vl ON vl.level = v_user_vip_level
    WHERE p.status = 'active' 
    AND p.min_vip_level <= v_user_vip_level
    ORDER BY RAND() * p.weight DESC
    LIMIT 1;
    
    
    SET v_expires_at = DATE_ADD(NOW(), INTERVAL 24 HOUR);
    
    
    INSERT INTO tasks (user_id, product_id, commission_earned, base_commission, expires_at)
    VALUES (p_user_id, v_product_id, v_commission, v_commission, v_expires_at);
    
    
    UPDATE users 
    SET tasks_completed_today = tasks_completed_today + 1,
        last_task_date = CURDATE()
    WHERE id = p_user_id;
    
END$$

CREATE DEFINER=`root`@`localhost` PROCEDURE `ProcessTransaction` (IN `p_user_id` INT, IN `p_type` VARCHAR(20), IN `p_amount` DECIMAL(10,2), IN `p_description` TEXT)   BEGIN
    DECLARE v_current_balance DECIMAL(10,2);
    DECLARE v_new_balance DECIMAL(10,2);
    DECLARE v_transaction_id VARCHAR(100);
    
    
    SELECT balance INTO v_current_balance FROM users WHERE id = p_user_id;
    
    
    IF p_type IN ('deposit', 'commission', 'bonus', 'referral_bonus') THEN
        SET v_new_balance = v_current_balance + p_amount;
    ELSE
        SET v_new_balance = v_current_balance - p_amount;
    END IF;
    
    
    SET v_transaction_id = CONCAT('TXN', UNIX_TIMESTAMP(), LPAD(p_user_id, 6, '0'));
    
    
    INSERT INTO transactions (
        user_id, type, amount, balance_before, balance_after, 
        transaction_id, description, status
    ) VALUES (
        p_user_id, p_type, p_amount, v_current_balance, v_new_balance,
        v_transaction_id, p_description, 'completed'
    );
    
    
    UPDATE users SET balance = v_new_balance WHERE id = p_user_id;
    
END$$

DELIMITER ;

-- --------------------------------------------------------

--
-- Table structure for table `admin_users`
--

CREATE TABLE `admin_users` (
  `id` int(11) NOT NULL,
  `username` varchar(50) NOT NULL,
  `email` varchar(100) NOT NULL,
  `full_name` varchar(100) DEFAULT NULL,
  `password_hash` varchar(255) NOT NULL,
  `role` enum('super_admin','admin','moderator') NOT NULL DEFAULT 'admin',
  `permissions` json DEFAULT NULL,
  `last_login` timestamp NULL DEFAULT NULL,
  `status` enum('active','inactive') NOT NULL DEFAULT 'active',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

--
-- Dumping data for table `admin_users`
--

INSERT INTO `admin_users` (`id`, `username`, `email`, `full_name`, `password_hash`, `role`, `permissions`, `last_login`, `status`, `created_at`, `updated_at`) VALUES
(1, 'admin', '<EMAIL>', NULL, '$2y$10$Qu/guvBOUODqL6g1aQ/zYeZ26DmHqWj2rmsTHv107d6QNk21/eJ1u', 'super_admin', NULL, '2025-07-07 14:26:54', 'active', '2025-06-27 08:23:08', '2025-07-07 14:26:54'),
(2, 'thedemohomexx', '<EMAIL>', NULL, '$2y$10$1x.DXRM.oEJZBoGRwaaFPeUjNw2kxqjKyB5T5udDo.Z0EJb5sKTDq', 'super_admin', NULL, NULL, 'active', '2025-07-02 09:58:07', '2025-07-02 11:58:07');

-- --------------------------------------------------------

--
-- Stand-in structure for view `admin_user_stats`
-- (See below for the actual view)
--
CREATE TABLE `admin_user_stats` (
`total_users` bigint(21)
,`active_users` bigint(21)
,`new_today` bigint(21)
,`total_balance` decimal(32,2)
,`total_deposits` decimal(32,2)
,`total_withdrawals` decimal(32,2)
);

-- --------------------------------------------------------

--
-- Table structure for table `customer_service_contacts`
--

CREATE TABLE `customer_service_contacts` (
  `id` int(11) NOT NULL,
  `name` varchar(255) NOT NULL,
  `link` varchar(500) NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

--
-- Dumping data for table `customer_service_contacts`
--

INSERT INTO `customer_service_contacts` (`id`, `name`, `link`, `created_at`) VALUES
(1, '1', '1', '2025-06-27 05:56:19'),
(2, 'WORLD BAMBOO CUSTOMER SERVICE TG1', 'https://t.me/BambooCS0', '2025-06-27 09:18:38');

-- --------------------------------------------------------

--
-- Table structure for table `negative_settings`
--

CREATE TABLE `negative_settings` (
  `id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `trigger_task_number` int(11) NOT NULL,
  `product_id_override` int(11) NOT NULL,
  `override_amount` decimal(15,2) NOT NULL,
  `is_active` tinyint(1) NOT NULL DEFAULT '1',
  `is_triggered` tinyint(1) NOT NULL DEFAULT '0',
  `admin_id_created` int(11) NOT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

--
-- Dumping data for table `negative_settings`
--

INSERT INTO `negative_settings` (`id`, `user_id`, `trigger_task_number`, `product_id_override`, `override_amount`, `is_active`, `is_triggered`, `admin_id_created`, `created_at`) VALUES
(2, 4, 12, 1, '1000.00', 1, 0, 1, '2025-06-27 11:16:09'),
(3, 8, 2, 1, '100.00', 1, 0, 1, '2025-06-29 17:49:33');

-- --------------------------------------------------------

--
-- Table structure for table `notifications`
--

CREATE TABLE `notifications` (
  `id` int(11) NOT NULL,
  `type` enum('system','user','admin','banner') NOT NULL,
  `title` varchar(255) NOT NULL,
  `message` text NOT NULL,
  `target_user_id` int(11) DEFAULT NULL,
  `target_vip_level` int(11) DEFAULT NULL,
  `is_global` tinyint(1) NOT NULL DEFAULT '0',
  `is_popup` tinyint(1) NOT NULL DEFAULT '0',
  `is_banner` tinyint(1) NOT NULL DEFAULT '0',
  `banner_color` varchar(7) DEFAULT '#007bff',
  `status` enum('active','inactive','scheduled') NOT NULL DEFAULT 'active',
  `start_date` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `end_date` timestamp NULL DEFAULT NULL,
  `read_count` int(11) NOT NULL DEFAULT '0',
  `created_by` int(11) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

--
-- Dumping data for table `notifications`
--

INSERT INTO `notifications` (`id`, `type`, `title`, `message`, `target_user_id`, `target_vip_level`, `is_global`, `is_popup`, `is_banner`, `banner_color`, `status`, `start_date`, `end_date`, `read_count`, `created_by`, `created_at`, `updated_at`) VALUES
(1, 'banner', 'Welcome', 'Welcome to Bamboo, if any assistance, please seek help from our customer service. Have a nice day!', NULL, NULL, 1, 0, 1, '#007bff', 'active', '2025-06-27 08:23:10', NULL, 0, NULL, '2025-06-27 08:23:10', '2025-06-27 08:23:10');

-- --------------------------------------------------------

--
-- Table structure for table `products`
--

CREATE TABLE `products` (
  `id` int(11) NOT NULL,
  `name` varchar(255) NOT NULL,
  `description` text,
  `image_url` varchar(500) DEFAULT NULL,
  `price` decimal(10,2) NOT NULL,
  `commission_rate` decimal(5,2) NOT NULL DEFAULT '5.00',
  `category_id` int(11) NOT NULL,
  `min_vip_level` int(11) NOT NULL DEFAULT '1',
  `max_daily_assignments` int(11) NOT NULL DEFAULT '100',
  `weight` int(11) NOT NULL DEFAULT '1',
  `stock` int(11) NOT NULL DEFAULT '0',
  `status` enum('active','inactive','out_of_stock') NOT NULL DEFAULT 'active',
  `total_assignments` int(11) NOT NULL DEFAULT '0',
  `total_completions` int(11) NOT NULL DEFAULT '0',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

--
-- Dumping data for table `products`
--

INSERT INTO `products` (`id`, `name`, `description`, `image_url`, `price`, `commission_rate`, `category_id`, `min_vip_level`, `max_daily_assignments`, `weight`, `stock`, `status`, `total_assignments`, `total_completions`, `created_at`, `updated_at`) VALUES
(1, 'iPhone 15 Pro', 'Latest iPhone with advanced features', 'http://localhost/bamboo/uploads/products/685e5bfbdde32.jpg', '999.00', '5.00', 1, 1, 100, 1, 100, 'active', 0, 0, '2025-06-27 08:23:10', '2025-06-27 06:53:16'),
(2, 'Samsung Galaxy S24', 'Premium Android smartphone', '/uploads/products/galaxy-s24.jpg', '899.00', '4.50', 1, 1, 100, 1, 100, 'active', 0, 0, '2025-06-27 08:23:10', '2025-06-27 08:23:10'),
(3, 'Nike Air Max', 'Comfortable running shoes', '/uploads/products/nike-airmax.jpg', '150.00', '8.00', 4, 1, 100, 1, 50, 'active', 0, 0, '2025-06-27 08:23:10', '2025-06-27 08:23:10'),
(4, 'Luxury Watch', 'Premium timepiece', '/uploads/products/luxury-watch.jpg', '2500.00', '3.00', 2, 3, 100, 1, 10, 'active', 0, 0, '2025-06-27 08:23:10', '2025-06-27 08:23:10');

-- --------------------------------------------------------

--
-- Table structure for table `product_categories`
--

CREATE TABLE `product_categories` (
  `id` int(11) NOT NULL,
  `name` varchar(100) NOT NULL,
  `description` text,
  `status` enum('active','inactive') NOT NULL DEFAULT 'active',
  `sort_order` int(11) NOT NULL DEFAULT '0',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

--
-- Dumping data for table `product_categories`
--

INSERT INTO `product_categories` (`id`, `name`, `description`, `status`, `sort_order`, `created_at`, `updated_at`) VALUES
(1, 'Electronics', 'Electronic devices and gadgets', 'active', 0, '2025-06-27 08:23:08', '2025-06-27 08:23:08'),
(2, 'Fashion', 'Clothing and accessories', 'active', 0, '2025-06-27 08:23:08', '2025-06-27 08:23:08'),
(3, 'Home & Garden', 'Home improvement and garden items', 'active', 0, '2025-06-27 08:23:08', '2025-06-27 08:23:08'),
(4, 'Sports', 'Sports and fitness equipment', 'active', 0, '2025-06-27 08:23:08', '2025-06-27 08:23:08'),
(5, 'Beauty', 'Beauty and personal care products', 'active', 0, '2025-06-27 08:23:08', '2025-06-27 08:23:08');

-- --------------------------------------------------------

--
-- Table structure for table `settings`
--

CREATE TABLE `settings` (
  `id` int(11) NOT NULL,
  `key` varchar(100) NOT NULL,
  `value` text NOT NULL,
  `type` enum('string','integer','float','boolean','json') NOT NULL DEFAULT 'string',
  `description` text,
  `category` varchar(50) NOT NULL DEFAULT 'general',
  `is_public` tinyint(1) NOT NULL DEFAULT '0',
  `updated_by` int(11) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

--
-- Dumping data for table `settings`
--

INSERT INTO `settings` (`id`, `key`, `value`, `type`, `description`, `category`, `is_public`, `updated_by`, `created_at`, `updated_at`) VALUES
(1, 'app_name', 'Kompyte', 'string', 'Application name', 'general', 1, NULL, '2025-06-27 08:23:09', '2025-06-28 08:02:47'),
(2, 'app_logo', '6861392b1bf47.png', 'string', 'Application logo URL', 'general', 1, NULL, '2025-06-27 08:23:09', '2025-06-29 13:01:31'),
(3, 'welcome_bonus', '10.00', 'float', 'Welcome bonus for new users', 'financial', 0, NULL, '2025-06-27 08:23:09', '2025-06-27 08:23:09'),
(4, 'min_withdrawal', '20.00', 'float', 'Minimum withdrawal amount', 'financial', 1, NULL, '2025-06-27 08:23:09', '2025-06-27 08:23:09'),
(5, 'max_withdrawal_daily', '1000.00', 'float', 'Maximum daily withdrawal', 'financial', 1, NULL, '2025-06-27 08:23:09', '2025-06-27 08:23:09'),
(6, 'negative_balance_trigger', '-50.00', 'float', 'Balance threshold to trigger negative balance', 'financial', 0, NULL, '2025-06-27 08:23:09', '2025-06-27 08:23:09'),
(7, 'task_expiry_hours', '24', 'integer', 'Hours before task expires', 'tasks', 0, NULL, '2025-06-27 08:23:09', '2025-06-27 08:23:09'),
(8, 'max_tasks_per_day', '10', 'integer', 'Maximum tasks per day for VIP 1', 'tasks', 0, NULL, '2025-06-27 08:23:09', '2025-06-27 08:23:09'),
(9, 'referral_commission_rate', '6', 'float', 'Referral commission percentage', 'referral', 0, NULL, '2025-06-27 08:23:09', '2025-06-27 23:31:56'),
(10, 'maintenance_mode', 'false', 'boolean', 'Enable maintenance mode', 'system', 0, NULL, '2025-06-27 08:23:09', '2025-06-27 08:23:09'),
(11, 'registration_enabled', 'true', 'boolean', 'Enable user registration', 'system', 1, NULL, '2025-06-27 08:23:09', '2025-06-27 08:23:09'),
(12, 'welcome_popup_text', 'Welcome to Bamboo! During our anniversary celebration, new users can get a bonus for the first time to complete group tasks.', 'string', 'Welcome popup message', 'general', 1, NULL, '2025-06-27 08:23:09', '2025-06-27 08:23:09'),
(13, 'notification_banner', 'Welcome to Bamboo, if any assistance, please seek help from our customer service. Have a nice day!', 'string', 'Main notification banner', 'general', 1, NULL, '2025-06-27 08:23:09', '2025-06-27 08:23:09'),
(14, 'appearance_primary_color', '#f07041', 'string', NULL, 'general', 0, NULL, '2025-06-27 17:00:17', '2025-06-29 15:44:03'),
(15, 'appearance_secondary_color', '#fea4ee', 'string', NULL, 'general', 0, NULL, '2025-06-27 17:00:17', '2025-06-28 19:12:52'),
(16, 'appearance_accent_color', '#ffffff', 'string', NULL, 'general', 0, NULL, '2025-06-27 17:00:17', '2025-06-28 19:16:55'),
(17, 'appearance_gradient_start', '#000000', 'string', NULL, 'general', 0, NULL, '2025-06-27 17:00:17', '2025-07-07 14:30:48'),
(18, 'appearance_gradient_end', '#c43a08', 'string', NULL, 'general', 0, NULL, '2025-06-27 17:00:17', '2025-07-07 14:30:48'),
(19, 'appearance_card_background', '#ffffff', 'string', NULL, 'general', 0, NULL, '2025-06-27 17:00:17', '2025-06-28 16:34:36'),
(20, 'appearance_sidebar_style', 'gradient', 'string', NULL, 'general', 0, NULL, '2025-06-27 17:00:18', '2025-06-27 17:00:18'),
(21, 'appearance_card_shadow', 'medium', 'string', NULL, 'general', 0, NULL, '2025-06-27 17:00:18', '2025-06-28 18:33:26'),
(22, 'appearance_border_radius', '0.5rem', 'string', NULL, 'general', 0, NULL, '2025-06-27 17:00:18', '2025-06-28 15:14:57'),
(23, 'appearance_theme_mode', 'light', 'string', NULL, 'general', 0, NULL, '2025-06-27 17:00:18', '2025-06-28 11:40:46'),
(54, 'withdrawal_policy_text', 'dd', 'string', NULL, 'general', 0, NULL, '2025-06-27 17:19:49', '2025-06-27 17:19:49'),
(56, 'contact_email', '<EMAIL>', 'string', NULL, 'general', 0, NULL, '2025-06-27 20:53:09', '2025-06-29 14:49:55'),
(57, 'contact_phone', '1010010101111', 'string', NULL, 'general', 0, NULL, '2025-06-27 20:53:09', '2025-06-29 14:57:37'),
(58, 'address', '11 Test klento roadx', 'string', NULL, 'general', 0, NULL, '2025-06-27 20:53:09', '2025-06-29 14:57:37'),
(59, 'default_currency', '$', 'string', NULL, 'general', 0, NULL, '2025-06-27 20:53:09', '2025-06-27 20:53:09'),
(60, 'decimal_places', '2', 'string', NULL, 'general', 0, NULL, '2025-06-27 20:53:09', '2025-06-27 20:53:09'),
(61, 'email_verification_required', '1', 'string', NULL, 'general', 0, NULL, '2025-06-27 20:53:09', '2025-06-27 20:53:09'),
(62, 'opening_hours', '9', 'string', NULL, 'general', 0, NULL, '2025-06-27 20:53:09', '2025-06-27 20:53:09'),
(63, 'closing_hours', '21', 'string', NULL, 'general', 0, NULL, '2025-06-27 20:53:09', '2025-06-27 20:53:09'),
(64, 'signup_bonus', '100', 'string', NULL, 'general', 0, NULL, '2025-06-27 20:53:10', '2025-06-29 14:56:32'),
(65, 'min_wallet_balance', '50', 'string', NULL, 'general', 0, NULL, '2025-06-27 20:53:10', '2025-06-29 14:56:32'),
(66, 'contract_terms', 'Contract Rules\r\n\r\n1) To optimize and reset your account, you must first complete all ratings with a minimum amount of 100 USDT and a minimum account reset amount of  100 USDT. \r\n\r\n1.1) If you need to reset your account, you must contact our online service to reset your account after you have completed all your optimization and withdrawn your funds.\r\n\r\n2) User withdrawals and system withdrawal requirements / security of user funds\r\n\r\n2.1) Each user needs to complete all the optimization rating before they can meet the system withdrawal requirements\r\n\r\n2.2) In order to avoid any loss of funds, all withdrawals are processed automatically by the system and not manually.\r\n\r\n2.3) All users are not allowed to apply for withdrawal in the middle of task to avoid affecting the merchant\'s operation\r\n\r\n2.4) Users\' funds are completely safe on the Platform and the Platform will be liable for any accidental loss\r\n\r\n(3) Please do not disclose your account password and withdrawal password to others. The platform will not be held responsible for any loss or damage caused.\r\n\r\n3.1) All users are advised to keep their accounts secure to avoid disclosure \r\n\r\n(3.2) The Platform is not responsible for any accidental disclosure of accounts\r\n\r\n3.3) Because of the financial implications of the accounts, it is important not to disclose them to avoid unnecessary problems.\r\n\r\n3.4) Withdrawal password It is recommended that you do not set a birthday password, ID card number or mobile phone number, etc. It is recommended that you set a more difficult password to protect your funds.\r\n\r\n3.5) If you forget your password, you can reset it by contacting the online service and be sure to change it yourself afterwards.\r\n\r\n(4) Optimization rating are randomly assigned by the system and therefore cannot be changed, canceled, controlled or skipped in any way\r\n\r\n4.1) Due to a large number of users on the platform, it is not possible to distribute group purchase items manually, so all group purchase items are distributed randomly by the system.\r\n\r\n(4.2) Group purchase/combination items are randomly released by the system and cannot be changed/cancelled/skipped by any user/staff.\r\n\r\n5. Legal action will be taken in the event of misuse of the account\r\n\r\n6. Each item comes from a different merchant, no deposit for more than 10 minutes, and each deposit must be made with the online service to confirm the merchant\'s cryptocurrency wallet address\r\n\r\n7. The platform will not be held responsible for any deposits made to the wrong wallet address\r\n\r\n8. Each time optimization rating, the user is required to complete it within 2   hours, if it is not completed without notifying the merchant to apply for a time extension, resulting in complaints from the merchant, the user is liable for breach of contract\r\n\r\n', 'string', NULL, 'general', 0, NULL, '2025-06-27 20:53:10', '2025-06-28 11:10:08'),
(67, 'about_us', 'ABOUT US\r\n\r\nBamboo has been a trusted partner in the truest sense of the word.\r\nWe approach growth from multiple angles.\r\nMost agencies only focus on channel management to determine success.\r\nThis is no longer enough. Bamboo looks beyond the channels to understand, forecast, and make smarter investments over time', 'string', NULL, 'general', 0, NULL, '2025-06-27 20:53:10', '2025-06-28 11:10:08'),
(68, 'faq_content', 'I. Start Product Optimization Task \r\n1.1) Minimum account balance of 100 USDT for the first 40 tasks/set \r\n1.2) A minimum renewal of 100 USDT is required to start the tasks\r\n1.3) Once one set of tasks has been completed, the user must request a full withdrawal and receive the withdrawal amount before requesting to reset the account.\r\n\r\nII. Withdrawal\r\n2.1) Full withdrawal amount can be requested after completing 1 group of task\r\n2.2) Users need to complete 1 group of tasks before they can request a withdrawal.\r\n2.3) You cannot request a withdrawal or refund if you choose to give up or withdraw in the middle of a task optimization. \r\n2.4) No withdrawals can be processed if the user\'s withdrawal request has not been received.\r\n2.5) If the withdrawal amount is 30000 USDT or above, please contact our online customer service to make a withdrawal. \r\n\r\nIII. Funds\r\n3.1) All funds will be held securely in the user\'s account and can be requested in full once all data has been completed\r\n3.2) To avoid any loss of funds, all data will be processed by the system and not manually\r\n3.3) The platform will take full responsibility for any accidental loss of funds.\r\n3.4) If the user\'s funds exceed the taxable amount of the UK government, the user will need to pay tax\r\n\r\nIV. Account Security\r\n(4.1) Please do not disclose your login password and withdrawal password to others, as the platform will not be held responsible for any loss.\r\n(4.2) Users are not recommended to set their birthday password, ID card number, or mobile phone number as their withdrawal password or login password.\r\n4.3) If you forget your login password or withdrawal password, please contact our online service to reset it.\r\n\r\nV. General Product Data\r\n5.1) Platform earnings are divided into normal earnings and  quintuple earnings\r\n5.2) Normal users will earn 0.5% of the profit for each normal task of optimizing\r\n5.3) VIP 2 users will earn 1% of the profit for each normal task of optimizing\r\n5.4) VIP 3 users will earn 1.5% of the profit for each normal task of optimizing\r\n5.5) VIP 4 users will earn 2% of the profit for each normal task of optimizing\r\n5.6) Funds and earnings will be refunded to the user\'s account for each completed task of optimizing\r\n5.7) The system will randomly allocate tasks to the user\'s account based on the total amount in the user\'s account\r\n5.8) Once the tasks of optimise product have been allocated to the user\'s account they cannot be cancelled or skipped\r\n\r\nVI. Combined Product Data\r\n6.1) Combined Product Data is composed of 0 to 3 data, the user may not necessarily get 3 products, the system will randomly allocate the data in the combined product the user has a higher chance of getting 1 or 2 product\r\n6.2) Users will receive five times the profit for each product data combined  than for the general product data\r\n6.3) Once you have received the combined product data, all funds will stop rolling and will be returned to your account after you have completed each product data in the combined.\r\n6.4) The system will randomly allocate the combined product data to the user\'s account according to the total balance on the user\'s account\r\n6.5) Once the combined product data has been allocated to the user\'s account, it cannot be cancelled or skipped.\r\n\r\nVII. Deposit\r\n(7.1) The amount of the deposit is the choice of the user, we cannot decide the amount of the deposit for the user, we suggest that the user can make the advance according to his ability or after he is familiar with the platform.\r\n(7.2) If a user needs to make a deposit when receiving combined product data, it is recommended that the user be able to make an advance based on the insufficient amount shown on the account.\r\n7.3) Before making an advance, you must request an advance from the online service and confirm the deposit detail.\r\n7.4) The platform will not be held responsible if the user deposits the wrong deposit detail.\r\n\r\nVIII. Merchant Cooperation\r\n(8.1) There are different products on the platform at each time, if the product is not optimized for a long period, the merchant will not be able to offload the task, which will affect the merchant product optimization progress, it is recommended that the user completes all product optimization and applies for a withdrawal as soon as possible to avoid affecting the merchant progress\r\n(8.2) The merchant will provide a deposit detail for the user to make a deposit \r\n(8.3) Any delay in completing the product optimization will be detrimental to the merchant and the process\r\n(8.4) User does not deal with it for a long time, the merchant has the right to complain about the user\'s account, resulting in a bad reputation for the user\'s account\r\n\r\nIX. Invitation\r\n9.1) Users will be able to invite other users using the invitation code on their account\r\n9.2) If the account has not yet completed all the data, it is not possible to invite other users\r\n9.3) The referrer will receive 20% of the referee\'s total earnings for the day\r\n\r\nX. Operating hours\r\n(10.1) Platform operating hours 9:00 to 21:59\r\n(10.2) Online service operating hours 9:00 to 21:59\r\n(10.3) Platform withdrawal hour 9:00 to 21:59', 'string', NULL, 'general', 0, NULL, '2025-06-27 20:53:10', '2025-06-28 11:10:08'),
(69, 'latest_events', 'I. Start Product Optimization Task \r\n1.1) Minimum account balance of 100 USDT for the first 40 tasks/set \r\n1.2) A minimum renewal of 100USDT is required to start the tasks\r\n1.3) Once one set of tasks has been completed, the user must request a full withdrawal and receive the withdrawal amount before requesting to reset the account.\r\n\r\nII. Withdrawal\r\n2.1) Full withdrawal amount can be requested after completing 1 group of task\r\n2.2) Users need to complete 1 group of tasks before they can request a withdrawal.\r\n2.3) You cannot request a withdrawal or refund if you choose to give up or withdraw in the middle of a task optimization. \r\n2.4) No withdrawals can be processed if the user\'s withdrawal request has not been received.\r\n2.5) If the withdrawal amount is 30000 USDT or above, please contact our online customer service to make a withdrawal. \r\n\r\nIII. Funds\r\n3.1) All funds will be held securely in the user\'s account and can be requested in full once all data has been completed\r\n3.2) To avoid any loss of funds, all data will be processed by the system and not manually\r\n3.3) The platform will take full responsibility for any accidental loss of funds.\r\n3.4) If the user\'s funds exceed the taxable amount of the UK government, the user will need to pay tax\r\n\r\nIV. Account Security\r\n(4.1) Please do not disclose your login password and withdrawal password to others, as the platform will not be held responsible for any loss.\r\n(4.2) Users are not recommended to set their birthday password, ID card number, or mobile phone number as their withdrawal password or login password.\r\n4.3) If you forget your login password or withdrawal password, please contact our online service to reset it.\r\n\r\nV. General Product Data\r\n5.1) Platform earnings are divided into normal earnings and  quintuple earnings\r\n5.2) Normal users will earn 0.5% of the profit for each normal task of optimizing\r\n5.3) VIP 2 users will earn 1% of the profit for each normal task of optimizing\r\n5.4) VIP 3 users will earn 1.5% of the profit for each normal task of optimizing\r\n5.5) VIP 4 users will earn 2% of the profit for each normal task of optimizing\r\n5.6) Funds and earnings will be refunded to the user\'s account for each completed task of optimizing\r\n5.7) The system will randomly allocate tasks to the user\'s account based on the total amount in the user\'s account\r\n5.8) Once the tasks of optimise product have been allocated to the user\'s account they cannot be cancelled or skipped\r\n\r\nVI. Combined Product Data\r\n6.1) Combined Product Data is composed of 0 to 3 data, the user may not necessarily get 3 products, the system will randomly allocate the data in the combined product the user has a higher chance of getting 1 or 2 product\r\n6.2) Users will receive five times the profit for each product data combined  than for the general product data\r\n6.3) Once you have received the combined product data, all funds will stop rolling and will be returned to your account after you have completed each product data in the combined.\r\n6.4) The system will randomly allocate the combined product data to the user\'s account according to the total balance on the user\'s account\r\n6.5) Once the combined product data has been allocated to the user\'s account, it cannot be cancelled or skipped.\r\n\r\nVII. Deposit\r\n(7.1) The amount of the deposit is the choice of the user, we cannot decide the amount of the deposit for the user, we suggest that the user can make the advance according to his ability or after he is familiar with the platform.\r\n(7.2) If a user needs to make a deposit when receiving combined product data, it is recommended that the user be able to make an advance based on the insufficient amount shown on the account.\r\n7.3) Before making an advance, you must request an advance from the online service and confirm the deposit detail.\r\n7.4) The platform will not be held responsible if the user deposits the wrong deposit detail.\r\n\r\nVIII. Merchant Cooperation\r\n(8.1) There are different products on the platform at each time, if the product is not optimized for a long period, the merchant will not be able to offload the task, which will affect the merchant product optimization progress, it is recommended that the user completes all product optimization and applies for a withdrawal as soon as possible to avoid affecting the merchant progress\r\n(8.2) The merchant will provide a deposit detail for the user to make a deposit \r\n(8.3) Any delay in completing the product optimization will be detrimental to the merchant and the process\r\n(8.4) User does not deal with it for a long time, the merchant has the right to complain about the user\'s account, resulting in a bad reputation for the user\'s account\r\n\r\nIX. Invitation\r\n9.1) Users will be able to invite other users using the invitation code on their account\r\n9.2) If the account has not yet completed all the data, it is not possible to invite other users\r\n9.3) The referrer will receive 20% of the referee\'s total earnings for the day\r\n\r\nX. Operating hours\r\n(10.1) Platform operating hours 9:00 to 21:59\r\n(10.2) Online service operating hours 9:00 to 21:59\r\n(10.3) Platform withdrawal hour 9:00 to 21:59', 'string', NULL, 'general', 0, NULL, '2025-06-27 20:53:10', '2025-06-28 11:10:08'),
(70, 'user_registration_agreement', 'Contract Rules\r\n\r\n1) To optimize and reset your account, you must first complete all ratings with a minimum amount of 100 USDT and a minimum account reset amount of  100 USDT. \r\n\r\n1.1) If you need to reset your account, you must contact our online service to reset your account after you have completed all your optimization and withdrawn your funds.\r\n\r\n2) User withdrawals and system withdrawal requirements / security of user funds\r\n\r\n2.1) Each user needs to complete all the optimization rating before they can meet the system withdrawal requirements\r\n\r\n2.2) In order to avoid any loss of funds, all withdrawals are processed automatically by the system and not manually.\r\n\r\n2.3) All users are not allowed to apply for withdrawal in the middle of task to avoid affecting the merchant\'s operation\r\n\r\n2.4) Users\' funds are completely safe on the Platform and the Platform will be liable for any accidental loss\r\n\r\n(3) Please do not disclose your account password and withdrawal password to others. The platform will not be held responsible for any loss or damage caused.\r\n\r\n3.1) All users are advised to keep their accounts secure to avoid disclosure \r\n\r\n(3.2) The Platform is not responsible for any accidental disclosure of accounts\r\n\r\n3.3) Because of the financial implications of the accounts, it is important not to disclose them to avoid unnecessary problems.\r\n\r\n3.4) Withdrawal password It is recommended that you do not set a birthday password, ID card number or mobile phone number, etc. It is recommended that you set a more difficult password to protect your funds.\r\n\r\n3.5) If you forget your password, you can reset it by contacting the online service and be sure to change it yourself afterwards.\r\n\r\n(4) Optimization rating are randomly assigned by the system and therefore cannot be changed, canceled, controlled or skipped in any way\r\n\r\n4.1) Due to a large number of users on the platform, it is not possible to distribute group purchase items manually, so all group purchase items are distributed randomly by the system.\r\n\r\n(4.2) Group purchase/combination items are randomly released by the system and cannot be changed/cancelled/skipped by any user/staff.\r\n\r\n5. Legal action will be taken in the event of misuse of the account\r\n\r\n6. Each item comes from a different merchant, no deposit for more than 10 minutes, and each deposit must be made with the online service to confirm the merchant\'s cryptocurrency wallet address\r\n\r\n7. The platform will not be held responsible for any deposits made to the wrong wallet address\r\n\r\n8. Each time optimization rating, the user is required to complete it within 2   hours, if it is not completed without notifying the merchant to apply for a time extension, resulting in complaints from the merchant, the user is liable for breach of contract\r\n\r\n', 'string', NULL, 'general', 0, NULL, '2025-06-27 20:53:10', '2025-06-28 11:10:08'),
(71, 'app_certificate', '685f11ec6b5ee.pdf', 'string', NULL, 'general', 0, NULL, '2025-06-27 20:58:02', '2025-06-27 21:49:32'),
(331, 'appearance_button_style', 'primary', 'string', NULL, 'general', 0, NULL, '2025-06-28 11:40:46', '2025-06-28 11:41:02'),
(384, 'appearance_sidebar_text_color', '#ffffff', 'string', NULL, 'general', 0, NULL, '2025-06-28 13:49:22', '2025-07-07 14:31:00'),
(638, 'footer_text', '© 2025 Company Name. All rights reserved.', 'string', NULL, 'general', 0, NULL, '2025-06-28 15:38:06', '2025-06-28 15:38:06'),
(639, 'footer_company_name', 'Kompyte', 'string', NULL, 'general', 0, NULL, '2025-06-28 15:38:06', '2025-06-28 15:38:06'),
(640, 'footer_version', 'v1.0', 'string', NULL, 'general', 0, NULL, '2025-06-28 15:38:06', '2025-06-28 15:38:06'),
(652, 'appearance_logo_size', 'extra-large', 'string', NULL, 'general', 0, NULL, '2025-06-28 16:11:26', '2025-06-28 16:11:26'),
(653, 'appearance_table_header_color', 'accent', 'string', NULL, 'general', 0, NULL, '2025-06-28 16:11:26', '2025-06-28 16:35:35'),
(654, 'appearance_level_badge_color', 'primary', 'string', NULL, 'general', 0, NULL, '2025-06-28 16:11:26', '2025-06-28 19:17:24'),
(700, 'appearance_footer_bg_color', '#f8f9fa', 'string', NULL, 'general', 0, NULL, '2025-06-28 16:30:03', '2025-06-28 16:30:03'),
(701, 'appearance_footer_text_color', '#6c757d', 'string', NULL, 'general', 0, NULL, '2025-06-28 16:30:03', '2025-06-28 16:30:03'),
(801, 'appearance_table_header_text_color', '#fe8b4d', 'string', NULL, 'general', 0, NULL, '2025-06-28 19:12:53', '2025-06-28 19:12:53');

-- --------------------------------------------------------

--
-- Table structure for table `superiors`
--

CREATE TABLE `superiors` (
  `id` int(11) NOT NULL,
  `name` varchar(100) NOT NULL,
  `phone` varchar(32) NOT NULL,
  `email` varchar(100) NOT NULL,
  `invitation_code` varchar(32) NOT NULL,
  `invited_count` int(11) NOT NULL DEFAULT '0',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

--
-- Dumping data for table `superiors`
--

INSERT INTO `superiors` (`id`, `name`, `phone`, `email`, `invitation_code`, `invited_count`, `created_at`) VALUES
(1, 'kentroad', '1010010101', '<EMAIL>', 'ea5673b3', 2, '2025-06-27 10:52:56');

-- --------------------------------------------------------

--
-- Table structure for table `tasks`
--

CREATE TABLE `tasks` (
  `id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `product_id` int(11) NOT NULL,
  `amount` decimal(10,2) NOT NULL DEFAULT '0.00',
  `commission_earned` decimal(10,2) NOT NULL DEFAULT '0.00',
  `base_commission` decimal(10,2) NOT NULL DEFAULT '0.00',
  `vip_bonus` decimal(10,2) NOT NULL DEFAULT '0.00',
  `status` enum('assigned','in_progress','completed','failed','expired') NOT NULL DEFAULT 'assigned',
  `assigned_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `started_at` timestamp NULL DEFAULT NULL,
  `completed_at` timestamp NULL DEFAULT NULL,
  `expires_at` timestamp NULL DEFAULT NULL,
  `submission_data` json DEFAULT NULL,
  `admin_notes` text
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

--
-- Dumping data for table `tasks`
--

INSERT INTO `tasks` (`id`, `user_id`, `product_id`, `amount`, `commission_earned`, `base_commission`, `vip_bonus`, `status`, `assigned_at`, `started_at`, `completed_at`, `expires_at`, `submission_data`, `admin_notes`) VALUES
(3, 4, 1, '50.00', '5.00', '5.00', '0.00', 'completed', '2025-06-27 08:50:21', '2025-06-27 08:50:21', '2025-06-27 08:50:21', '2025-06-28 08:50:21', NULL, 'Task completed by Alice'),
(4, 5, 2, '80.00', '8.00', '8.00', '0.00', 'completed', '2025-06-27 08:50:21', '2025-06-27 08:50:21', '2025-06-27 08:50:21', '2025-06-28 08:50:21', NULL, 'Task completed by Bob'),
(5, 4, 1, '0.00', '49.95', '49.95', '0.00', 'completed', '2025-06-27 09:13:09', NULL, '2025-07-01 19:39:13', '2025-06-28 09:13:09', NULL, NULL),
(6, 4, 1, '0.00', '49.95', '49.95', '0.00', 'completed', '2025-07-01 16:17:04', NULL, '2025-07-01 19:38:57', '2025-07-02 16:17:04', NULL, NULL),
(7, 9, 2, '899.00', '8.99', '8.99', '0.00', 'completed', '2025-07-01 17:14:40', '2025-07-01 17:14:40', '2025-07-01 20:03:09', '2025-07-01 18:14:40', NULL, NULL),
(8, 4, 3, '150.00', '1.50', '1.50', '0.00', 'completed', '2025-07-01 19:40:51', '2025-07-01 19:40:51', '2025-07-01 19:41:01', '2025-07-01 20:40:51', NULL, NULL),
(9, 4, 3, '150.00', '1.50', '1.50', '0.00', 'completed', '2025-07-01 20:13:06', '2025-07-01 20:13:06', '2025-07-01 20:22:11', '2025-07-01 21:13:06', NULL, NULL),
(10, 4, 1, '0.00', '49.95', '49.95', '0.00', 'completed', '2025-07-01 22:34:31', NULL, '2025-07-01 20:36:19', '2025-07-02 22:34:31', NULL, NULL),
(11, 9, 2, '899.00', '8.99', '8.99', '0.00', 'completed', '2025-07-02 07:27:04', '2025-07-02 07:27:04', '2025-07-02 10:15:04', '2025-07-02 08:27:04', NULL, NULL),
(12, 9, 3, '150.00', '1.50', '1.50', '0.00', 'completed', '2025-07-02 10:20:41', '2025-07-02 10:20:41', '2025-07-02 10:21:24', '2025-07-02 11:20:41', NULL, NULL);

-- --------------------------------------------------------

--
-- Table structure for table `transactions`
--

CREATE TABLE `transactions` (
  `id` int(11) NOT NULL,
  `order_no` varchar(32) DEFAULT NULL,
  `payment_channel` varchar(64) DEFAULT NULL,
  `credited_by` varchar(64) DEFAULT NULL,
  `state` varchar(32) DEFAULT NULL,
  `user_id` int(11) NOT NULL,
  `type` enum('deposit','withdrawal','commission','bonus','referral_bonus','penalty','adjustment','admin_credit','admin_deduction') NOT NULL,
  `amount` decimal(10,2) NOT NULL,
  `balance_before` decimal(10,2) NOT NULL DEFAULT '0.00',
  `balance_after` decimal(10,2) NOT NULL DEFAULT '0.00',
  `status` enum('pending','processing','completed','failed','cancelled') NOT NULL DEFAULT 'pending',
  `payment_method` varchar(50) DEFAULT NULL,
  `transaction_id` varchar(100) DEFAULT NULL,
  `external_transaction_id` varchar(255) DEFAULT NULL,
  `fee_amount` decimal(10,2) NOT NULL DEFAULT '0.00',
  `description` text,
  `admin_notes` text,
  `processed_by` int(11) DEFAULT NULL,
  `processed_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

--
-- Dumping data for table `transactions`
--

INSERT INTO `transactions` (`id`, `order_no`, `payment_channel`, `credited_by`, `state`, `user_id`, `type`, `amount`, `balance_before`, `balance_after`, `status`, `payment_method`, `transaction_id`, `external_transaction_id`, `fee_amount`, `description`, `admin_notes`, `processed_by`, `processed_at`, `created_at`, `updated_at`) VALUES
(1, NULL, NULL, NULL, NULL, 4, 'deposit', '200.00', '0.00', '200.00', 'completed', 'bank', 'TXN000001', NULL, '0.00', NULL, NULL, NULL, NULL, '2025-06-27 08:50:21', '2025-06-27 08:50:21'),
(2, NULL, NULL, NULL, NULL, 5, 'deposit', '300.00', '0.00', '300.00', 'completed', 'bank', 'TXN000002', NULL, '0.00', NULL, NULL, NULL, NULL, '2025-06-27 08:50:21', '2025-06-27 08:50:21'),
(3, NULL, NULL, NULL, NULL, 4, 'withdrawal', '50.00', '200.00', '150.00', 'completed', 'bank', 'TXN000003', NULL, '0.00', NULL, NULL, NULL, NULL, '2025-06-27 08:50:21', '2025-06-27 08:50:21'),
(4, NULL, NULL, NULL, NULL, 5, 'withdrawal', '100.00', '300.00', '200.00', 'completed', 'bank', 'TXN000004', NULL, '0.00', NULL, NULL, NULL, NULL, '2025-06-27 08:50:21', '2025-06-27 08:50:21'),
(5, NULL, NULL, NULL, NULL, 4, 'adjustment', '40000.00', '100.00', '40100.00', 'completed', NULL, NULL, NULL, '0.00', 'Admin balance adjustment (addition)', NULL, NULL, NULL, '2025-06-27 09:20:22', '2025-06-27 09:20:22'),
(6, NULL, NULL, NULL, NULL, 4, 'adjustment', '20000.00', '40100.00', '20100.00', 'completed', NULL, NULL, NULL, '0.00', 'Admin balance adjustment (deduction)', NULL, NULL, NULL, '2025-06-27 09:21:30', '2025-06-27 09:21:30'),
(7, NULL, NULL, NULL, NULL, 4, 'adjustment', '1223333.00', '20100.00', '1243433.00', 'completed', NULL, NULL, NULL, '0.00', 'Admin balance adjustment (addition)', NULL, NULL, NULL, '2025-06-27 09:27:56', '2025-06-27 09:27:56'),
(8, NULL, NULL, NULL, NULL, 4, 'adjustment', '10000.00', '1243433.00', '1233433.00', 'completed', NULL, NULL, NULL, '0.00', 'Admin balance adjustment (deduction)', NULL, NULL, NULL, '2025-06-27 09:32:41', '2025-06-27 09:32:41'),
(10, NULL, NULL, NULL, NULL, 5, 'deposit', '56000.00', '150.00', '56150.00', 'completed', NULL, NULL, NULL, '0.00', 'Admin Top-up', NULL, NULL, NULL, '2025-06-27 10:21:54', '2025-06-27 10:21:54'),
(11, NULL, NULL, NULL, NULL, 4, 'deposit', '76889000.00', '1233433.00', '78122433.00', 'completed', NULL, NULL, NULL, '0.00', 'Admin Top-up', NULL, NULL, NULL, '2025-06-27 10:29:49', '2025-06-27 10:29:49'),
(12, NULL, NULL, NULL, NULL, 4, 'deposit', '123444.00', '78122433.00', '78245877.00', 'completed', NULL, NULL, NULL, '0.00', 'Admin Top-up', NULL, NULL, NULL, '2025-06-27 10:31:41', '2025-06-27 10:31:41'),
(13, NULL, NULL, NULL, NULL, 4, 'withdrawal', '78245877.00', '78245877.00', '0.00', 'completed', NULL, NULL, NULL, '0.00', 'Admin Deduction', NULL, NULL, NULL, '2025-06-27 10:33:28', '2025-06-27 10:33:28'),
(14, NULL, NULL, NULL, NULL, 4, 'deposit', '1999283.00', '0.00', '1999283.00', 'completed', NULL, NULL, NULL, '0.00', 'Admin Top-up', NULL, NULL, NULL, '2025-06-27 10:33:56', '2025-06-27 10:33:56'),
(18, NULL, NULL, NULL, NULL, 4, 'deposit', '1.00', '1999283.00', '1999284.00', 'completed', NULL, NULL, NULL, '0.00', 'Admin Top-up', NULL, NULL, NULL, '2025-06-27 11:19:46', '2025-06-27 11:19:46'),
(19, 'yUya0omwpv', 'Admin Panel', 'admin', 'completed', 4, 'admin_credit', '1.00', '0.00', '0.00', 'pending', NULL, NULL, NULL, '0.00', 'Admin credit', NULL, NULL, NULL, '2025-06-27 11:19:46', '2025-06-27 13:19:46'),
(20, NULL, NULL, NULL, NULL, 4, 'withdrawal', '99283.00', '1999284.00', '1900001.00', 'completed', NULL, NULL, NULL, '0.00', 'Admin Deduction', NULL, NULL, NULL, '2025-06-27 11:20:21', '2025-06-27 11:20:21'),
(21, '7drpHTa2DJ', 'Admin Panel', 'admin', 'completed', 4, 'admin_deduction', '99283.00', '0.00', '0.00', 'pending', NULL, NULL, NULL, '0.00', 'Admin deduction', NULL, NULL, NULL, '2025-06-27 11:20:21', '2025-06-27 13:20:21'),
(22, NULL, NULL, NULL, NULL, 4, 'withdrawal', '1222.00', '1900001.00', '1898779.00', 'completed', NULL, NULL, NULL, '0.00', 'Admin Deduction', NULL, NULL, NULL, '2025-06-27 11:21:33', '2025-06-27 11:21:33'),
(23, 'ws7TWITNhh', 'Admin Panel', 'admin', 'completed', 4, 'admin_deduction', '1222.00', '0.00', '0.00', 'pending', NULL, NULL, NULL, '0.00', 'Admin deduction', NULL, NULL, NULL, '2025-06-27 11:21:33', '2025-06-27 13:21:33'),
(24, NULL, NULL, NULL, NULL, 4, 'withdrawal', '898779.00', '1898779.00', '1000000.00', 'completed', NULL, NULL, NULL, '0.00', 'Admin Deduction', NULL, NULL, NULL, '2025-06-27 11:22:19', '2025-06-27 11:22:19'),
(25, 'ka0N9xDn5O', 'Admin Panel', 'admin', 'completed', 4, 'admin_deduction', '898779.00', '0.00', '0.00', 'pending', NULL, NULL, NULL, '0.00', 'Admin deduction', NULL, NULL, NULL, '2025-06-27 11:22:19', '2025-06-27 13:22:19'),
(26, NULL, NULL, NULL, NULL, 4, 'deposit', '2999.00', '1000000.00', '1002999.00', 'completed', NULL, NULL, NULL, '0.00', 'Admin Top-up', NULL, NULL, NULL, '2025-06-27 11:36:58', '2025-06-27 11:36:58'),
(27, NULL, NULL, NULL, NULL, 4, 'deposit', '455.00', '1002999.00', '1003454.00', 'completed', NULL, NULL, NULL, '0.00', 'Admin Top-up', NULL, NULL, NULL, '2025-06-27 12:06:52', '2025-06-27 12:06:52'),
(28, 'JCLz5mNI4L', 'Admin Panel', 'admin', 'completed', 4, 'admin_credit', '455.00', '0.00', '0.00', 'pending', NULL, NULL, NULL, '0.00', 'Admin credit', NULL, NULL, NULL, '2025-06-27 12:06:52', '2025-06-27 14:06:52'),
(33, NULL, NULL, NULL, NULL, 4, 'deposit', '345.00', '0.00', '345.00', 'completed', NULL, NULL, NULL, '0.00', 'Admin Top-up', NULL, NULL, NULL, '2025-06-27 12:48:00', '2025-06-27 12:48:00'),
(34, 'zDYPd7lUNG', 'Admin Panel', 'admin', 'completed', 4, 'admin_credit', '345.00', '0.00', '0.00', 'pending', NULL, NULL, NULL, '0.00', 'Admin credit', NULL, NULL, NULL, '2025-06-27 12:48:00', '2025-06-27 14:48:00'),
(35, 'P23CKGJX6U', 'Admin Panel', 'admin', NULL, 4, 'admin_credit', '50000.00', '345.00', '50345.00', 'completed', NULL, NULL, NULL, '0.00', 'Admin Top-up', NULL, 1, NULL, '2025-06-27 12:55:58', '2025-06-27 12:55:58'),
(36, NULL, NULL, NULL, NULL, 9, 'admin_credit', '50.00', '1000.00', '1050.00', 'completed', NULL, NULL, NULL, '0.00', 'Admin Top-up', NULL, NULL, NULL, '2025-06-29 15:49:07', '2025-06-29 15:49:07'),
(37, NULL, NULL, NULL, NULL, 4, 'withdrawal', '-100.00', '0.00', '0.00', 'pending', NULL, NULL, NULL, '0.00', 'User withdrawal request', NULL, NULL, NULL, '2025-07-01 18:09:13', '2025-07-01 20:09:13'),
(38, NULL, NULL, NULL, NULL, 4, 'commission', '49.95', '50245.00', '50294.95', 'completed', NULL, NULL, NULL, '0.00', 'Task completed - refund + commission', NULL, NULL, NULL, '2025-07-01 19:38:57', '2025-07-01 21:38:57'),
(39, NULL, NULL, NULL, NULL, 4, 'commission', '49.95', '50294.95', '50344.90', 'completed', NULL, NULL, NULL, '0.00', 'Task completed - refund + commission', NULL, NULL, NULL, '2025-07-01 19:39:13', '2025-07-01 21:39:13'),
(40, NULL, NULL, NULL, NULL, 4, 'commission', '151.50', '50194.90', '50346.40', 'completed', NULL, NULL, NULL, '0.00', 'Task completed - refund + commission', NULL, NULL, NULL, '2025-07-01 19:41:01', '2025-07-01 21:41:01'),
(41, NULL, NULL, NULL, NULL, 9, 'commission', '907.99', '151.00', '1058.99', 'completed', NULL, NULL, NULL, '0.00', 'Task completed - refund + commission', NULL, NULL, NULL, '2025-07-01 20:03:09', '2025-07-01 22:03:09'),
(42, NULL, NULL, NULL, NULL, 4, 'commission', '151.50', '50196.40', '50347.90', 'completed', NULL, NULL, NULL, '0.00', 'Task completed - refund + commission', NULL, NULL, NULL, '2025-07-01 20:22:11', '2025-07-01 22:22:11'),
(43, NULL, NULL, NULL, NULL, 4, 'commission', '49.95', '50347.90', '50397.85', 'completed', NULL, NULL, NULL, '0.00', 'Task completed - refund + commission', NULL, NULL, NULL, '2025-07-01 20:36:19', '2025-07-01 22:36:19'),
(44, NULL, NULL, NULL, NULL, 9, 'commission', '907.99', '159.99', '1067.98', 'completed', NULL, NULL, NULL, '0.00', 'Task completed - refund + commission', NULL, NULL, NULL, '2025-07-02 10:15:04', '2025-07-02 12:15:05'),
(45, NULL, NULL, NULL, NULL, 9, 'commission', '151.50', '917.98', '1069.48', 'completed', NULL, NULL, NULL, '0.00', 'Task completed - refund + commission', NULL, NULL, NULL, '2025-07-02 10:21:24', '2025-07-02 12:21:24');

-- --------------------------------------------------------

--
-- Table structure for table `users`
--

CREATE TABLE `users` (
  `id` int(11) NOT NULL,
  `username` varchar(50) NOT NULL,
  `phone` varchar(20) NOT NULL,
  `usdt_wallet_address` varchar(255) DEFAULT NULL,
  `exchange_name` varchar(100) DEFAULT NULL,
  `email` varchar(100) DEFAULT NULL,
  `avatar` varchar(255) DEFAULT NULL,
  `password_hash` varchar(255) DEFAULT NULL,
  `withdrawal_pin_hash` varchar(255) DEFAULT NULL,
  `gender` enum('male','female') NOT NULL,
  `invitation_code` varchar(20) NOT NULL,
  `invited_by` varchar(32) DEFAULT NULL,
  `balance` decimal(10,2) NOT NULL DEFAULT '0.00',
  `commission_balance` decimal(10,2) NOT NULL DEFAULT '0.00',
  `frozen_balance` decimal(10,2) NOT NULL DEFAULT '0.00',
  `total_deposited` decimal(10,2) NOT NULL DEFAULT '0.00',
  `total_withdrawn` decimal(10,2) NOT NULL DEFAULT '0.00',
  `total_commission_earned` decimal(10,2) NOT NULL DEFAULT '0.00',
  `vip_level` int(11) NOT NULL DEFAULT '1',
  `tasks_completed_today` int(11) NOT NULL DEFAULT '0',
  `last_task_date` date DEFAULT NULL,
  `status` enum('pending','active','suspended','banned') NOT NULL DEFAULT 'pending',
  `email_verified` tinyint(1) NOT NULL DEFAULT '0',
  `phone_verified` tinyint(1) NOT NULL DEFAULT '0',
  `avatar_url` varchar(500) DEFAULT NULL,
  `referral_count` int(11) NOT NULL DEFAULT '0',
  `last_login` timestamp NULL DEFAULT NULL,
  `login_count` int(11) NOT NULL DEFAULT '0',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

--
-- Dumping data for table `users`
--

INSERT INTO `users` (`id`, `username`, `phone`, `usdt_wallet_address`, `exchange_name`, `email`, `avatar`, `password_hash`, `withdrawal_pin_hash`, `gender`, `invitation_code`, `invited_by`, `balance`, `commission_balance`, `frozen_balance`, `total_deposited`, `total_withdrawn`, `total_commission_earned`, `vip_level`, `tasks_completed_today`, `last_task_date`, `status`, `email_verified`, `phone_verified`, `avatar_url`, `referral_count`, `last_login`, `login_count`, `created_at`, `updated_at`) VALUES
(4, 'alice', '1234567890', '***************************', '111111111111111111', '<EMAIL>', NULL, '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'female', 'ALICE01', 'ea5673b3', '50397.85', '10.00', '1.00', '200.00', '50.00', '20.00', 1, 1, '2025-07-02', 'active', 1, 1, NULL, 0, '2025-07-01 15:05:02', 5, '2025-06-27 08:49:47', '2025-07-01 22:36:19'),
(5, 'bob', '2345678901', NULL, NULL, '<EMAIL>', NULL, '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'male', 'BOB01', NULL, '56150.00', '15.00', '0.00', '300.00', '100.00', '30.00', 2, 1, '2025-06-27', 'active', 1, 1, NULL, 1, '2025-07-01 20:39:40', 10, '2025-06-27 08:49:47', '2025-07-01 22:39:40'),
(8, 'jamesbong01', '10010101', '22444444444444444444444444444444', 'Mr Bong', '<EMAIL>', NULL, '$2y$10$C8czN8ayyDATacGhLiViBusHODPmxPWGKy0xhRLuGA86HZ7WQiS92', '$2y$10$.TmNDebSYZyt1Lzah4oksu9Y6IoCjRDLMR6/EvN6Pa3GQFxMmRLNS', 'male', 'B3CFC578', 'ea5673b3', '133000.00', '0.00', '0.00', '0.00', '0.00', '0.00', 3, 0, '2025-06-27', 'active', 0, 0, NULL, 0, NULL, 0, '2025-06-27 13:52:05', '2025-06-27 22:32:07'),
(9, 'demohomexx', '1010100101', '1488585675857957875858758', 'demohomexx', '<EMAIL>', NULL, '$2y$10$vOiQjME0Wxm4fT9JDFldteSUaGUBuSBk1SyP6B0QfYnThngXQA7fy', '$2y$10$oDBZlpDZQxuOWRx1eOZbyOXKmmdK/YOGiP6RrHtGW2UJ3JWyQwT2O', 'male', '44A26DFF', 'ea5673b3', '1069.48', '0.00', '0.00', '0.00', '0.00', '0.00', 1, 2, '2025-06-29', 'active', 0, 0, 'http://localhost/Bamboo/uploads/avatars/68652b66d1a96.png', 0, '2025-07-02 07:23:50', 0, '2025-06-29 15:42:48', '2025-07-02 12:51:50');

-- --------------------------------------------------------

--
-- Stand-in structure for view `user_dashboard_view`
-- (See below for the actual view)
--
CREATE TABLE `user_dashboard_view` (
`id` int(11)
,`username` varchar(50)
,`balance` decimal(10,2)
,`commission_balance` decimal(10,2)
,`vip_level` int(11)
,`vip_name` varchar(50)
,`max_daily_tasks` int(11)
,`tasks_completed_today` int(11)
,`referral_count` int(11)
,`total_commission_earned` decimal(10,2)
,`pending_tasks` bigint(21)
);

-- --------------------------------------------------------

--
-- Table structure for table `user_salaries`
--

CREATE TABLE `user_salaries` (
  `id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `amount` decimal(15,2) NOT NULL,
  `status` enum('paid','pending_approval') NOT NULL,
  `admin_id_processed` int(11) NOT NULL,
  `paid_at` datetime DEFAULT NULL,
  `notes` text,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

--
-- Dumping data for table `user_salaries`
--

INSERT INTO `user_salaries` (`id`, `user_id`, `amount`, `status`, `admin_id_processed`, `paid_at`, `notes`, `created_at`) VALUES
(3, 4, '2999.00', 'paid', 1, NULL, 'Your salary has been paid', '2025-06-27 15:36:58'),
(4, 9, '50.00', 'paid', 1, NULL, '', '2025-06-29 19:49:07');

-- --------------------------------------------------------

--
-- Table structure for table `user_sessions`
--

CREATE TABLE `user_sessions` (
  `id` varchar(128) NOT NULL,
  `user_id` int(11) NOT NULL,
  `ip_address` varchar(45) NOT NULL,
  `user_agent` text NOT NULL,
  `payload` longtext NOT NULL,
  `last_activity` int(11) NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Table structure for table `vip_levels`
--

CREATE TABLE `vip_levels` (
  `id` int(11) NOT NULL,
  `level` int(11) NOT NULL,
  `name` varchar(50) NOT NULL,
  `min_balance` decimal(10,2) NOT NULL DEFAULT '0.00',
  `max_daily_tasks` int(11) NOT NULL DEFAULT '10',
  `commission_multiplier` decimal(3,2) NOT NULL DEFAULT '1.00',
  `withdrawal_limit_daily` decimal(10,2) NOT NULL DEFAULT '1000.00',
  `withdrawal_fee_percentage` decimal(5,2) NOT NULL DEFAULT '2.00',
  `benefits` text,
  `icon_path` varchar(255) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

--
-- Dumping data for table `vip_levels`
--

INSERT INTO `vip_levels` (`id`, `level`, `name`, `min_balance`, `max_daily_tasks`, `commission_multiplier`, `withdrawal_limit_daily`, `withdrawal_fee_percentage`, `benefits`, `icon_path`, `created_at`, `updated_at`) VALUES
(1, 1, 'VIP 1', '0.00', 5, '1.00', '100.00', '5.00', 'Basic access to platform', '685e60c79e5a0.png', '2025-06-27 08:23:08', '2025-06-27 09:13:43'),
(2, 2, 'VIP 2', '100.00', 10, '1.20', '500.00', '4.00', 'Unlimited access to all features', NULL, '2025-06-27 08:23:08', '2025-06-27 08:23:08'),
(3, 3, 'VIP 3', '500.00', 15, '1.50', '1000.00', '3.00', 'Premium features and higher commissions', NULL, '2025-06-27 08:23:08', '2025-06-27 08:23:08'),
(4, 4, 'VIP 4', '1000.00', 20, '1.80', '2000.00', '2.50', 'Elite status with maximum benefits', NULL, '2025-06-27 08:23:08', '2025-06-27 08:23:08'),
(5, 5, 'VIP 5', '2500.00', 30, '2.00', '5000.00', '2.00', 'Ultimate VIP with highest rewards', NULL, '2025-06-27 08:23:08', '2025-06-27 08:23:08');

-- --------------------------------------------------------

--
-- Table structure for table `withdrawal_quotes`
--

CREATE TABLE `withdrawal_quotes` (
  `id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `message` text NOT NULL,
  `status` enum('active','resolved') NOT NULL DEFAULT 'active',
  `admin_id_created` int(11) NOT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- --------------------------------------------------------

--
-- Structure for view `admin_user_stats`
--
DROP TABLE IF EXISTS `admin_user_stats`;

CREATE ALGORITHM=UNDEFINED DEFINER=`root`@`localhost` SQL SECURITY DEFINER VIEW `admin_user_stats`  AS SELECT count(0) AS `total_users`, count((case when (`users`.`status` = 'active') then 1 end)) AS `active_users`, count((case when (cast(`users`.`created_at` as date) = curdate()) then 1 end)) AS `new_today`, sum(`users`.`balance`) AS `total_balance`, sum(`users`.`total_deposited`) AS `total_deposits`, sum(`users`.`total_withdrawn`) AS `total_withdrawals` FROM `users``users`  ;

-- --------------------------------------------------------

--
-- Structure for view `user_dashboard_view`
--
DROP TABLE IF EXISTS `user_dashboard_view`;

CREATE ALGORITHM=UNDEFINED DEFINER=`root`@`localhost` SQL SECURITY DEFINER VIEW `user_dashboard_view`  AS SELECT `u`.`id` AS `id`, `u`.`username` AS `username`, `u`.`balance` AS `balance`, `u`.`commission_balance` AS `commission_balance`, `u`.`vip_level` AS `vip_level`, `vl`.`name` AS `vip_name`, `vl`.`max_daily_tasks` AS `max_daily_tasks`, `u`.`tasks_completed_today` AS `tasks_completed_today`, `u`.`referral_count` AS `referral_count`, `u`.`total_commission_earned` AS `total_commission_earned`, (select count(0) from `tasks` `t` where ((`t`.`user_id` = `u`.`id`) and (`t`.`status` = 'assigned'))) AS `pending_tasks` FROM (`users` `u` join `vip_levels` `vl` on((`u`.`vip_level` = `vl`.`level`))) WHERE (`u`.`status` = 'active')  ;

--
-- Indexes for dumped tables
--

--
-- Indexes for table `admin_users`
--
ALTER TABLE `admin_users`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `username` (`username`),
  ADD UNIQUE KEY `email` (`email`),
  ADD KEY `idx_username` (`username`),
  ADD KEY `idx_email` (`email`),
  ADD KEY `idx_role` (`role`);

--
-- Indexes for table `customer_service_contacts`
--
ALTER TABLE `customer_service_contacts`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `negative_settings`
--
ALTER TABLE `negative_settings`
  ADD PRIMARY KEY (`id`),
  ADD KEY `user_id` (`user_id`),
  ADD KEY `product_id_override` (`product_id_override`);

--
-- Indexes for table `notifications`
--
ALTER TABLE `notifications`
  ADD PRIMARY KEY (`id`),
  ADD KEY `created_by` (`created_by`),
  ADD KEY `idx_type` (`type`),
  ADD KEY `idx_target_user_id` (`target_user_id`),
  ADD KEY `idx_is_global` (`is_global`),
  ADD KEY `idx_status` (`status`),
  ADD KEY `idx_start_date` (`start_date`);

--
-- Indexes for table `products`
--
ALTER TABLE `products`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_category` (`category_id`),
  ADD KEY `idx_status` (`status`),
  ADD KEY `idx_min_vip_level` (`min_vip_level`),
  ADD KEY `idx_price` (`price`),
  ADD KEY `idx_product_category_status` (`category_id`,`status`);

--
-- Indexes for table `product_categories`
--
ALTER TABLE `product_categories`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `settings`
--
ALTER TABLE `settings`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `key` (`key`),
  ADD KEY `updated_by` (`updated_by`),
  ADD KEY `idx_key` (`key`),
  ADD KEY `idx_category` (`category`);

--
-- Indexes for table `superiors`
--
ALTER TABLE `superiors`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `invitation_code` (`invitation_code`);

--
-- Indexes for table `tasks`
--
ALTER TABLE `tasks`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_user_id` (`user_id`),
  ADD KEY `idx_product_id` (`product_id`),
  ADD KEY `idx_status` (`status`),
  ADD KEY `idx_assigned_at` (`assigned_at`),
  ADD KEY `idx_completed_at` (`completed_at`),
  ADD KEY `idx_task_user_status` (`user_id`,`status`);

--
-- Indexes for table `transactions`
--
ALTER TABLE `transactions`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `transaction_id` (`transaction_id`),
  ADD UNIQUE KEY `idx_order_no` (`order_no`),
  ADD KEY `processed_by` (`processed_by`),
  ADD KEY `idx_user_id` (`user_id`),
  ADD KEY `idx_type` (`type`),
  ADD KEY `idx_status` (`status`),
  ADD KEY `idx_transaction_id` (`transaction_id`),
  ADD KEY `idx_created_at` (`created_at`),
  ADD KEY `idx_transaction_user_type` (`user_id`,`type`);

--
-- Indexes for table `users`
--
ALTER TABLE `users`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `username` (`username`),
  ADD UNIQUE KEY `phone` (`phone`),
  ADD UNIQUE KEY `invitation_code` (`invitation_code`),
  ADD UNIQUE KEY `email` (`email`),
  ADD KEY `idx_username` (`username`),
  ADD KEY `idx_phone` (`phone`),
  ADD KEY `idx_email` (`email`),
  ADD KEY `idx_invitation_code` (`invitation_code`),
  ADD KEY `idx_invited_by` (`invited_by`),
  ADD KEY `idx_vip_level` (`vip_level`),
  ADD KEY `idx_status` (`status`),
  ADD KEY `idx_created_at` (`created_at`),
  ADD KEY `idx_user_vip_status` (`vip_level`,`status`);

--
-- Indexes for table `user_salaries`
--
ALTER TABLE `user_salaries`
  ADD PRIMARY KEY (`id`),
  ADD KEY `user_id` (`user_id`);

--
-- Indexes for table `user_sessions`
--
ALTER TABLE `user_sessions`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_user_id` (`user_id`),
  ADD KEY `idx_last_activity` (`last_activity`);

--
-- Indexes for table `vip_levels`
--
ALTER TABLE `vip_levels`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `level` (`level`);

--
-- Indexes for table `withdrawal_quotes`
--
ALTER TABLE `withdrawal_quotes`
  ADD PRIMARY KEY (`id`),
  ADD KEY `user_id` (`user_id`);

--
-- AUTO_INCREMENT for dumped tables
--

--
-- AUTO_INCREMENT for table `admin_users`
--
ALTER TABLE `admin_users`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=3;

--
-- AUTO_INCREMENT for table `customer_service_contacts`
--
ALTER TABLE `customer_service_contacts`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=3;

--
-- AUTO_INCREMENT for table `negative_settings`
--
ALTER TABLE `negative_settings`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=4;

--
-- AUTO_INCREMENT for table `notifications`
--
ALTER TABLE `notifications`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2;

--
-- AUTO_INCREMENT for table `products`
--
ALTER TABLE `products`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=5;

--
-- AUTO_INCREMENT for table `product_categories`
--
ALTER TABLE `product_categories`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=6;

--
-- AUTO_INCREMENT for table `settings`
--
ALTER TABLE `settings`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=838;

--
-- AUTO_INCREMENT for table `superiors`
--
ALTER TABLE `superiors`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2;

--
-- AUTO_INCREMENT for table `tasks`
--
ALTER TABLE `tasks`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=13;

--
-- AUTO_INCREMENT for table `transactions`
--
ALTER TABLE `transactions`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=46;

--
-- AUTO_INCREMENT for table `users`
--
ALTER TABLE `users`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=10;

--
-- AUTO_INCREMENT for table `user_salaries`
--
ALTER TABLE `user_salaries`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=5;

--
-- AUTO_INCREMENT for table `vip_levels`
--
ALTER TABLE `vip_levels`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=6;

--
-- AUTO_INCREMENT for table `withdrawal_quotes`
--
ALTER TABLE `withdrawal_quotes`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- Constraints for dumped tables
--

--
-- Constraints for table `notifications`
--
ALTER TABLE `notifications`
  ADD CONSTRAINT `notifications_ibfk_1` FOREIGN KEY (`target_user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `notifications_ibfk_2` FOREIGN KEY (`created_by`) REFERENCES `admin_users` (`id`) ON DELETE SET NULL;

--
-- Constraints for table `products`
--
ALTER TABLE `products`
  ADD CONSTRAINT `products_ibfk_1` FOREIGN KEY (`category_id`) REFERENCES `product_categories` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `products_ibfk_2` FOREIGN KEY (`min_vip_level`) REFERENCES `vip_levels` (`level`) ON UPDATE CASCADE;

--
-- Constraints for table `settings`
--
ALTER TABLE `settings`
  ADD CONSTRAINT `settings_ibfk_1` FOREIGN KEY (`updated_by`) REFERENCES `admin_users` (`id`) ON DELETE SET NULL;

--
-- Constraints for table `tasks`
--
ALTER TABLE `tasks`
  ADD CONSTRAINT `tasks_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `tasks_ibfk_2` FOREIGN KEY (`product_id`) REFERENCES `products` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `transactions`
--
ALTER TABLE `transactions`
  ADD CONSTRAINT `transactions_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `transactions_ibfk_2` FOREIGN KEY (`processed_by`) REFERENCES `admin_users` (`id`) ON DELETE SET NULL;

--
-- Constraints for table `users`
--
ALTER TABLE `users`
  ADD CONSTRAINT `users_ibfk_1` FOREIGN KEY (`vip_level`) REFERENCES `vip_levels` (`level`) ON UPDATE CASCADE;

--
-- Constraints for table `user_sessions`
--
ALTER TABLE `user_sessions`
  ADD CONSTRAINT `user_sessions_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
