<?php
/**
 * Bamboo Deployment Validation Script
 * This script validates that the deployment system is ready for use
 */

// Prevent direct access in production
if (!isset($_GET['validate']) || $_GET['validate'] !== 'deployment') {
    die('Access denied. Use: validate_deployment.php?validate=deployment');
}

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Bamboo Deployment Validation</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        h1 { color: #2c3e50; text-align: center; margin-bottom: 30px; }
        .check { margin: 15px 0; padding: 15px; border-radius: 5px; border-left: 4px solid; }
        .pass { background: #d4edda; border-color: #27ae60; color: #155724; }
        .fail { background: #f8d7da; border-color: #e74c3c; color: #721c24; }
        .warning { background: #fff3cd; border-color: #f39c12; color: #856404; }
        .info { background: #d1ecf1; border-color: #3498db; color: #0c5460; }
        .summary { margin-top: 30px; padding: 20px; background: #ecf0f1; border-radius: 5px; }
        .file-list { background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0; }
        .file-list ul { margin: 10px 0; padding-left: 20px; }
        .btn { background: #3498db; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; display: inline-block; margin: 5px; }
        .btn:hover { background: #2980b9; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎋 Bamboo Deployment System Validation</h1>
        
        <?php
        $checks = [];
        $overall_status = true;
        
        // Check 1: Required files in install directory
        $install_files = [
            'install.php' => 'Main installation script',
            'database_migration.sql' => 'Database schema file',
            'README_PRODUCTION_ZIP.md' => 'Production zip creation guide'
        ];
        
        $missing_install_files = [];
        foreach ($install_files as $file => $description) {
            if (!file_exists($file)) {
                $missing_install_files[] = "$file ($description)";
            }
        }
        
        if (empty($missing_install_files)) {
            $checks[] = ['status' => 'pass', 'title' => 'Install Directory Files', 'message' => 'All required installation files are present.'];
        } else {
            $checks[] = ['status' => 'fail', 'title' => 'Install Directory Files', 'message' => 'Missing files: ' . implode(', ', $missing_install_files)];
            $overall_status = false;
        }
        
        // Check 2: Production.zip file
        if (file_exists('production.zip')) {
            $zip_size = filesize('production.zip');
            $zip_size_mb = round($zip_size / 1024 / 1024, 2);
            $checks[] = ['status' => 'pass', 'title' => 'Production.zip File', 'message' => "Production.zip exists ({$zip_size_mb} MB). Ready for automatic installation."];
        } else {
            $checks[] = ['status' => 'warning', 'title' => 'Production.zip File', 'message' => 'Production.zip not found. You will need to create it manually or use manual installation method.'];
        }
        
        // Check 3: Production directory structure
        $production_dirs = [
            '../production/admin' => 'Admin panel files',
            '../production/user' => 'User application files',
            '../production/includes' => 'Core PHP includes',
            '../production/assets' => 'CSS, JS, and images',
            '../production/api' => 'API endpoints',
            '../production/uploads' => 'File upload directories'
        ];
        
        $missing_prod_dirs = [];
        foreach ($production_dirs as $dir => $description) {
            if (!is_dir($dir)) {
                $missing_prod_dirs[] = "$dir ($description)";
            }
        }
        
        if (empty($missing_prod_dirs)) {
            $checks[] = ['status' => 'pass', 'title' => 'Production Directory Structure', 'message' => 'All required production directories are present.'];
        } else {
            $checks[] = ['status' => 'fail', 'title' => 'Production Directory Structure', 'message' => 'Missing directories: ' . implode(', ', $missing_prod_dirs)];
            $overall_status = false;
        }
        
        // Check 4: Production config file
        $prod_config = '../production/includes/config.php';
        if (file_exists($prod_config)) {
            $config_content = file_get_contents($prod_config);
            if (strpos($config_content, 'your_production_db') !== false) {
                $checks[] = ['status' => 'pass', 'title' => 'Production Configuration', 'message' => 'Production config.php is ready with placeholder values.'];
            } else {
                $checks[] = ['status' => 'warning', 'title' => 'Production Configuration', 'message' => 'Production config.php may already be configured. Installer will update it.'];
            }
            
            if (strpos($config_content, "define('DEBUG_MODE', false)") !== false) {
                $checks[] = ['status' => 'pass', 'title' => 'Debug Mode', 'message' => 'Debug mode is correctly set to false for production.'];
            } else {
                $checks[] = ['status' => 'warning', 'title' => 'Debug Mode', 'message' => 'Debug mode should be set to false for production.'];
            }
        } else {
            $checks[] = ['status' => 'fail', 'title' => 'Production Configuration', 'message' => 'Production config.php file is missing.'];
            $overall_status = false;
        }
        
        // Check 5: Database migration file validation
        if (file_exists('database_migration.sql')) {
            $sql_content = file_get_contents('database_migration.sql');
            $table_count = substr_count($sql_content, 'CREATE TABLE');
            if ($table_count >= 10) {
                $checks[] = ['status' => 'pass', 'title' => 'Database Schema', 'message' => "Database migration file contains {$table_count} tables. Schema appears complete."];
            } else {
                $checks[] = ['status' => 'warning', 'title' => 'Database Schema', 'message' => "Database migration file contains only {$table_count} tables. Verify completeness."];
            }
        }
        
        // Check 6: PHP Requirements
        $php_version = PHP_VERSION;
        if (version_compare($php_version, '8.0.0', '>=')) {
            $checks[] = ['status' => 'pass', 'title' => 'PHP Version', 'message' => "PHP {$php_version} meets requirements (8.0+)."];
        } else {
            $checks[] = ['status' => 'fail', 'title' => 'PHP Version', 'message' => "PHP {$php_version} is below minimum requirement (8.0+)."];
            $overall_status = false;
        }
        
        // Check 7: Required PHP Extensions
        $required_extensions = ['pdo_mysql', 'json', 'zip'];
        $missing_extensions = [];
        foreach ($required_extensions as $ext) {
            if (!extension_loaded($ext)) {
                $missing_extensions[] = $ext;
            }
        }
        
        if (empty($missing_extensions)) {
            $checks[] = ['status' => 'pass', 'title' => 'PHP Extensions', 'message' => 'All required PHP extensions are available.'];
        } else {
            $checks[] = ['status' => 'fail', 'title' => 'PHP Extensions', 'message' => 'Missing extensions: ' . implode(', ', $missing_extensions)];
            $overall_status = false;
        }
        
        // Check 8: File Permissions
        $writable_dirs = ['../production/uploads', '../production/logs'];
        $permission_issues = [];
        foreach ($writable_dirs as $dir) {
            if (is_dir($dir) && !is_writable($dir)) {
                $permission_issues[] = $dir;
            }
        }
        
        if (empty($permission_issues)) {
            $checks[] = ['status' => 'pass', 'title' => 'File Permissions', 'message' => 'Required directories are writable.'];
        } else {
            $checks[] = ['status' => 'warning', 'title' => 'File Permissions', 'message' => 'These directories may need write permissions: ' . implode(', ', $permission_issues)];
        }
        
        // Display all checks
        foreach ($checks as $check) {
            echo "<div class='check {$check['status']}'>";
            echo "<strong>{$check['title']}:</strong> {$check['message']}";
            echo "</div>";
        }
        ?>
        
        <div class="summary">
            <h3>📊 Validation Summary</h3>
            <?php if ($overall_status): ?>
                <div class="check pass">
                    <strong>✅ Deployment System Ready!</strong> All critical requirements are met. You can proceed with deployment.
                </div>
            <?php else: ?>
                <div class="check fail">
                    <strong>❌ Issues Found!</strong> Please resolve the failed checks before deploying.
                </div>
            <?php endif; ?>
            
            <h4>📋 Next Steps:</h4>
            <ol>
                <?php if (!file_exists('production.zip')): ?>
                    <li><strong>Create production.zip:</strong> Follow the guide in README_PRODUCTION_ZIP.md</li>
                <?php endif; ?>
                <li><strong>Upload to server:</strong> Upload the entire install/ folder to your web server</li>
                <li><strong>Run installer:</strong> Navigate to yourdomain.com/install/install.php</li>
                <li><strong>Complete setup:</strong> Follow the installation wizard</li>
                <li><strong>Security:</strong> Delete install/ folder after successful installation</li>
            </ol>
        </div>
        
        <div style="text-align: center; margin-top: 30px;">
            <?php if (file_exists('install.php')): ?>
                <a href="install.php" class="btn">🚀 Start Installation</a>
            <?php endif; ?>
            <a href="../DEPLOYMENT_SYSTEM_GUIDE.md" class="btn">📖 Deployment Guide</a>
        </div>
        
        <div class="info" style="margin-top: 20px;">
            <strong>💡 Tip:</strong> Run this validation script on your production server before starting the installation to ensure everything is ready.
        </div>
    </div>
</body>
</html>
