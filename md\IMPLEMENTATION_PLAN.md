# Implementation Plan

## 1. Fix Admin Dashboard Routing

### Issue:
- The URL `http://localhost/bamboo/admin/dashboard/` should take users to the dashboard page if they're logged in, or redirect to the login page if not.

### Solution:
1. Create an `index.php` file in the `admin/dashboard/` directory that:
   - Checks if the user is logged in
   - If logged in, includes the dashboard content
   - If not logged in, redirects to the admin login page

## 2. Implement Admin General Settings

### Issue:
- Need to implement the admin general settings as specified in the requirements.

### Solution:
1. Enhance the existing `admin/settings/index.php` to include:
   - App name configuration
   - App logo upload functionality
   - App certificate upload functionality
   - Opening/closing hours settings
   - Sign-up bonus configuration
   - Minimum wallet balance settings
   - Contract terms editor

2. Create a database table structure for storing these settings:
   ```sql
   CREATE TABLE IF NOT EXISTS `app_settings` (
     `id` int(11) NOT NULL AUTO_INCREMENT,
     `setting_key` varchar(100) NOT NULL,
     `setting_value` text,
     `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
     `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
     PRIMARY KEY (`id`),
     UNIQUE KEY `setting_key` (`setting_key`)
   ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
   ```

3. Implement file upload handling for logo and certificate files

## 3. Fix Member Management - Add User Functionality

### Issue:
- Need to implement the "Add User" functionality in the admin member management section.

### Solution:
1. Create `admin/member_management/add.php` with:
   - Form for adding new users with all required fields:
     - Username
     - Email
     - Phone number
     - Password
     - Gender
     - Status
     - VIP level
     - Balance
   - CSRF protection
   - Input validation
   - Success/error messages

2. Implement the backend logic to:
   - Validate input data
   - Hash passwords securely
   - Insert new user into the database
   - Handle errors gracefully

## Implementation Steps

### Step 1: Fix Admin Dashboard Routing
1. Create `admin/dashboard/index.php` that redirects to dashboard.php if logged in
2. Ensure proper authentication checks are in place

### Step 2: Implement Admin General Settings
1. Enhance `admin/settings/index.php` with all required fields
2. Create file upload handlers for logo and certificate
3. Implement database functions to save/retrieve settings
4. Add form validation and error handling

### Step 3: Implement Add User Functionality
1. Create `admin/member_management/add.php` with user form
2. Implement backend logic for user creation
3. Add validation and error handling
4. Link from member management page

## Technical Considerations
- Ensure all forms have CSRF protection
- Implement proper input validation and sanitization
- Use prepared statements for database operations
- Handle file uploads securely
- Provide clear feedback to users on success/failure
- Maintain consistent styling with the white theme