<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Offline - Bamboo</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #ff6900 0%, #ff8533 100%);
            color: white;
            height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            text-align: center;
            padding: 2rem;
        }
        
        .offline-container {
            max-width: 400px;
            animation: fadeIn 0.6s ease-out;
        }
        
        .offline-icon {
            font-size: 4rem;
            margin-bottom: 1.5rem;
            opacity: 0.9;
        }
        
        .offline-title {
            font-size: 2rem;
            font-weight: 600;
            margin-bottom: 1rem;
        }
        
        .offline-message {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 2rem;
            opacity: 0.9;
        }
        
        .retry-button {
            background: rgba(255, 255, 255, 0.2);
            border: 2px solid rgba(255, 255, 255, 0.3);
            color: white;
            padding: 0.75rem 2rem;
            border-radius: 2rem;
            font-size: 1rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
        }
        
        .retry-button:hover {
            background: rgba(255, 255, 255, 0.3);
            border-color: rgba(255, 255, 255, 0.5);
            transform: translateY(-2px);
        }
        
        .retry-button:active {
            transform: translateY(0);
        }
        
        .connection-status {
            margin-top: 2rem;
            padding: 1rem;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 1rem;
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
        }
        
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 0.5rem;
            animation: pulse 2s infinite;
        }
        
        .status-offline {
            background: #ff4757;
        }
        
        .status-online {
            background: #2ed573;
        }
        
        @keyframes fadeIn {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        @keyframes pulse {
            0%, 100% {
                opacity: 1;
            }
            50% {
                opacity: 0.5;
            }
        }
        
        .features-list {
            text-align: left;
            margin-top: 2rem;
            padding: 1.5rem;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 1rem;
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
        }
        
        .features-list h3 {
            margin-bottom: 1rem;
            text-align: center;
        }
        
        .features-list ul {
            list-style: none;
        }
        
        .features-list li {
            padding: 0.5rem 0;
            position: relative;
            padding-left: 1.5rem;
        }
        
        .features-list li::before {
            content: '✓';
            position: absolute;
            left: 0;
            color: #2ed573;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="offline-container">
        <div class="offline-icon">📱</div>
        <h1 class="offline-title">You're Offline</h1>
        <p class="offline-message">
            No internet connection detected. Some features may be limited, but you can still access cached content.
        </p>
        
        <button class="retry-button" onclick="checkConnection()">
            Try Again
        </button>
        
        <div class="connection-status">
            <span class="status-indicator status-offline" id="statusIndicator"></span>
            <span id="statusText">Offline</span>
        </div>
        
        <div class="features-list">
            <h3>Available Offline</h3>
            <ul>
                <li>View cached dashboard</li>
                <li>Browse previous transactions</li>
                <li>Access profile information</li>
                <li>View task history</li>
            </ul>
        </div>
    </div>

    <script>
        // Check connection status
        function checkConnection() {
            if (navigator.onLine) {
                // Try to fetch a small resource to verify connectivity
                fetch('/Bamboo/assets/images/favicon.ico', { 
                    method: 'HEAD',
                    cache: 'no-cache'
                })
                .then(() => {
                    // Connection restored, redirect to main app
                    window.location.href = '/Bamboo/user/dashboard/dashboard.php';
                })
                .catch(() => {
                    updateConnectionStatus(false);
                });
            } else {
                updateConnectionStatus(false);
            }
        }
        
        // Update connection status indicator
        function updateConnectionStatus(isOnline) {
            const indicator = document.getElementById('statusIndicator');
            const statusText = document.getElementById('statusText');
            
            if (isOnline) {
                indicator.className = 'status-indicator status-online';
                statusText.textContent = 'Online';
                
                // Auto-redirect after 2 seconds
                setTimeout(() => {
                    window.location.href = '/Bamboo/user/dashboard/dashboard.php';
                }, 2000);
            } else {
                indicator.className = 'status-indicator status-offline';
                statusText.textContent = 'Offline';
            }
        }
        
        // Listen for online/offline events
        window.addEventListener('online', () => {
            updateConnectionStatus(true);
        });
        
        window.addEventListener('offline', () => {
            updateConnectionStatus(false);
        });
        
        // Check initial connection status
        updateConnectionStatus(navigator.onLine);
        
        // Auto-check connection every 30 seconds
        setInterval(checkConnection, 30000);
    </script>
</body>
</html>
