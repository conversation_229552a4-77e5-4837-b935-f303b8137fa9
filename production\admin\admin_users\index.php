<?php
/**
 * Bamboo Web Application - Admin User Management
 * Company: Notepadsly
 * Version: 1.0
 */

define('BAMBOO_APP', true);
require_once '../../includes/config.php';
require_once '../../includes/functions.php';

if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Check if admin is logged in
if (!isAdminLoggedIn()) {
    redirect('admin/login/');
}

// Check if current admin has permission to manage admin users (only super_admin)
$current_admin_role = $_SESSION['admin_role'] ?? '';
if ($current_admin_role !== 'super_admin') {
    showError('Access denied. Only super administrators can manage admin users.');
    redirect('admin/dashboard/');
}

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['csrf_token'])) {
    if (!verifyCSRFToken($_POST['csrf_token'])) {
        showError('Invalid security token. Please try again.');
    } else {
        $action = $_POST['action'] ?? '';
        
        switch ($action) {
            case 'add_admin':
                $username = sanitizeInput($_POST['username'] ?? '');
                $email = sanitizeInput($_POST['email'] ?? '');
                $password = $_POST['password'] ?? '';
                $role = sanitizeInput($_POST['role'] ?? 'admin');
                $status = sanitizeInput($_POST['status'] ?? 'active');
                
                // Validate inputs
                if (empty($username) || empty($email) || empty($password)) {
                    showError('Username, email, and password are required.');
                    break;
                }
                
                if (strlen($password) < 8) {
                    showError('Password must be at least 8 characters long.');
                    break;
                }
                
                if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
                    showError('Please enter a valid email address.');
                    break;
                }
                
                // Check for duplicates
                $existing_admin = fetchRow("SELECT id FROM admin_users WHERE username = ? OR email = ?", [$username, $email]);
                if ($existing_admin) {
                    showError('Username or email already exists.');
                    break;
                }
                
                // Create new admin user
                $password_hash = password_hash($password, PASSWORD_DEFAULT);
                $admin_data = [
                    'username' => $username,
                    'email' => $email,
                    'password_hash' => $password_hash,
                    'role' => $role,
                    'status' => $status,
                    'created_at' => date('Y-m-d H:i:s')
                ];
                
                if (insertRecord('admin_users', $admin_data)) {
                    showSuccess("Admin user '{$username}' created successfully!");
                } else {
                    showError('Failed to create admin user. Please try again.');
                }
                break;
                
            case 'update_status':
                $admin_id = (int)($_POST['admin_id'] ?? 0);
                $new_status = sanitizeInput($_POST['status'] ?? '');
                
                if ($admin_id <= 0 || !in_array($new_status, ['active', 'inactive'])) {
                    showError('Invalid admin ID or status.');
                    break;
                }
                
                // Prevent deactivating self
                if ($admin_id == getCurrentAdminId()) {
                    showError('You cannot change your own status.');
                    break;
                }
                
                if (updateRecord('admin_users', ['status' => $new_status], 'id = ?', [$admin_id])) {
                    showSuccess('Admin status updated successfully!');
                } else {
                    showError('Failed to update admin status.');
                }
                break;
                
            case 'delete_admin':
                $admin_id = (int)($_POST['admin_id'] ?? 0);
                
                if ($admin_id <= 0) {
                    showError('Invalid admin ID.');
                    break;
                }
                
                // Prevent deleting self
                if ($admin_id == getCurrentAdminId()) {
                    showError('You cannot delete your own account.');
                    break;
                }
                
                // Get admin info before deletion
                $admin_to_delete = fetchRow("SELECT username FROM admin_users WHERE id = ?", [$admin_id]);
                
                if (deleteRecord('admin_users', 'id = ?', [$admin_id])) {
                    showSuccess("Admin user '{$admin_to_delete['username']}' deleted successfully!");
                } else {
                    showError('Failed to delete admin user.');
                }
                break;
        }
    }
}

// Fetch all admin users
$admin_users = fetchAll("SELECT id, username, email, role, status, last_login, created_at FROM admin_users ORDER BY created_at DESC");

// Page configuration
$page_title = 'Admin User Management';
$body_class = 'admin-page';
$additional_css = [
    BASE_URL . 'admin/assets/css/admin.css'
];

include '../includes/admin_header.php';
?>

<style>
.admin-user-card {
    transition: all 0.3s ease;
    border: 1px solid #e9ecef;
}

.admin-user-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.role-badge {
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
}

.status-indicator {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    display: inline-block;
    margin-right: 0.5rem;
}

.status-active { background-color: #28a745; }
.status-inactive { background-color: #dc3545; }

.add-admin-form {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 0.75rem;
    padding: 2rem;
    margin-bottom: 2rem;
}

.form-floating {
    margin-bottom: 1rem;
}

.btn-action {
    padding: 0.25rem 0.5rem;
    font-size: 0.875rem;
    margin: 0 0.125rem;
}
</style>

<div class="admin-wrapper">
    <?php include '../includes/admin_sidebar.php'; ?>
    <div class="admin-main">
        <?php include '../includes/admin_topbar.php'; ?>
        <div class="admin-content">
            <div class="container-fluid">
                
                <!-- Page Header -->
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <div>
                        <h1 class="h3 mb-0">
                            <i class="bi bi-shield-lock text-primary me-2"></i>
                            Admin User Management
                        </h1>
                        <small class="text-muted">Manage administrator accounts and permissions</small>
                    </div>
                    <div>
                        <span class="badge bg-info">Super Admin Only</span>
                    </div>
                </div>

                <!-- Add New Admin Form -->
                <div class="add-admin-form">
                    <h5 class="mb-3">
                        <i class="bi bi-person-plus me-2"></i>Add New Administrator
                    </h5>
                    <form method="POST" class="row g-3">
                        <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                        <input type="hidden" name="action" value="add_admin">
                        
                        <div class="col-md-3">
                            <div class="form-floating">
                                <input type="text" class="form-control" id="username" name="username" placeholder="Username" required>
                                <label for="username">Username</label>
                            </div>
                        </div>
                        
                        <div class="col-md-3">
                            <div class="form-floating">
                                <input type="email" class="form-control" id="email" name="email" placeholder="Email" required>
                                <label for="email">Email Address</label>
                            </div>
                        </div>
                        
                        <div class="col-md-2">
                            <div class="form-floating">
                                <input type="password" class="form-control" id="password" name="password" placeholder="Password" required minlength="8">
                                <label for="password">Password</label>
                            </div>
                        </div>
                        
                        <div class="col-md-2">
                            <div class="form-floating">
                                <select class="form-select" id="role" name="role">
                                    <option value="admin">Admin</option>
                                    <option value="moderator">Moderator</option>
                                    <option value="super_admin">Super Admin</option>
                                </select>
                                <label for="role">Role</label>
                            </div>
                        </div>
                        
                        <div class="col-md-2">
                            <button type="submit" class="btn btn-primary h-100 w-100">
                                <i class="bi bi-plus-circle me-2"></i>Add Admin
                            </button>
                        </div>
                    </form>
                </div>

                <!-- Admin Users List -->
                <div class="row">
                    <?php if (empty($admin_users)): ?>
                        <div class="col-12">
                            <div class="alert alert-info text-center">
                                <i class="bi bi-info-circle me-2"></i>
                                No admin users found.
                            </div>
                        </div>
                    <?php else: ?>
                        <?php foreach ($admin_users as $admin): ?>
                            <div class="col-md-6 col-lg-4 mb-4">
                                <div class="card admin-user-card h-100">
                                    <div class="card-body">
                                        <div class="d-flex justify-content-between align-items-start mb-3">
                                            <div>
                                                <h6 class="card-title mb-1">
                                                    <i class="bi bi-person-circle me-2"></i>
                                                    <?php echo htmlspecialchars($admin['username']); ?>
                                                </h6>
                                                <small class="text-muted"><?php echo htmlspecialchars($admin['email']); ?></small>
                                            </div>
                                            <span class="badge role-badge bg-<?php echo $admin['role'] === 'super_admin' ? 'danger' : ($admin['role'] === 'admin' ? 'primary' : 'secondary'); ?>">
                                                <?php echo ucfirst(str_replace('_', ' ', $admin['role'])); ?>
                                            </span>
                                        </div>
                                        
                                        <div class="mb-3">
                                            <div class="d-flex align-items-center mb-2">
                                                <span class="status-indicator status-<?php echo $admin['status']; ?>"></span>
                                                <span class="text-<?php echo $admin['status'] === 'active' ? 'success' : 'danger'; ?>">
                                                    <?php echo ucfirst($admin['status']); ?>
                                                </span>
                                            </div>
                                            <small class="text-muted">
                                                <i class="bi bi-clock me-1"></i>
                                                Last login: <?php echo $admin['last_login'] ? date('M j, Y g:i A', strtotime($admin['last_login'])) : 'Never'; ?>
                                            </small>
                                        </div>
                                        
                                        <div class="d-flex justify-content-between align-items-center">
                                            <small class="text-muted">
                                                Created: <?php echo date('M j, Y', strtotime($admin['created_at'])); ?>
                                            </small>
                                            
                                            <?php if ($admin['id'] != getCurrentAdminId()): ?>
                                                <div class="btn-group" role="group">
                                                    <!-- Status Toggle -->
                                                    <form method="POST" class="d-inline">
                                                        <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                                                        <input type="hidden" name="action" value="update_status">
                                                        <input type="hidden" name="admin_id" value="<?php echo $admin['id']; ?>">
                                                        <input type="hidden" name="status" value="<?php echo $admin['status'] === 'active' ? 'inactive' : 'active'; ?>">
                                                        <button type="submit" class="btn btn-sm btn-outline-<?php echo $admin['status'] === 'active' ? 'warning' : 'success'; ?> btn-action"
                                                                onclick="return confirm('Are you sure you want to <?php echo $admin['status'] === 'active' ? 'deactivate' : 'activate'; ?> this admin?')">
                                                            <i class="bi bi-<?php echo $admin['status'] === 'active' ? 'pause' : 'play'; ?>"></i>
                                                        </button>
                                                    </form>
                                                    
                                                    <!-- Delete -->
                                                    <form method="POST" class="d-inline">
                                                        <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                                                        <input type="hidden" name="action" value="delete_admin">
                                                        <input type="hidden" name="admin_id" value="<?php echo $admin['id']; ?>">
                                                        <button type="submit" class="btn btn-sm btn-outline-danger btn-action"
                                                                onclick="return confirm('Are you sure you want to delete admin user \'<?php echo htmlspecialchars($admin['username']); ?>\'? This action cannot be undone.')">
                                                            <i class="bi bi-trash"></i>
                                                        </button>
                                                    </form>
                                                </div>
                                            <?php else: ?>
                                                <span class="badge bg-info">Current User</span>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </div>
                
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Auto-hide alerts after 5 seconds
    setTimeout(function() {
        const alerts = document.querySelectorAll('.alert');
        alerts.forEach(alert => {
            alert.style.transition = 'opacity 0.5s';
            alert.style.opacity = '0';
            setTimeout(() => alert.remove(), 500);
        });
    }, 5000);
    
    // Form validation
    const addAdminForm = document.querySelector('form[action=""]');
    if (addAdminForm) {
        addAdminForm.addEventListener('submit', function(e) {
            const password = document.getElementById('password').value;
            if (password.length < 8) {
                e.preventDefault();
                alert('Password must be at least 8 characters long.');
                return false;
            }
        });
    }
});
</script>

<?php include '../includes/admin_footer.php'; ?>
