<?php
/**
 * Bamboo Web Application - Add Negative Setting API
 * Company: Notepadsly
 * Version: 1.0
 */

define('BAMBOO_APP', true);
require_once '../../../includes/config.php';
require_once '../../../includes/functions.php';

if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

header('Content-Type: application/json');

// Check if admin is logged in
if (!isAdminLoggedIn()) {
    jsonResponse(['success' => false, 'message' => 'Unauthorized access.'], 401);
}

// Only allow POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    jsonResponse(['success' => false, 'message' => 'Invalid request method.'], 405);
}

// Validate CSRF token
if (!verifyCSRFToken($_POST['csrf_token'] ?? '')) {
    jsonResponse(['success' => false, 'message' => 'Invalid security token. Please try again.'], 403);
}

$user_id = (int)($_POST['user_id'] ?? 0);
$trigger_number = (int)($_POST['trigger_number'] ?? 0);
$product_id = (int)($_POST['product_id'] ?? 0);
$amount = (float)($_POST['amount'] ?? 0);

if ($user_id <= 0 || $trigger_number <= 0 || $product_id <= 0 || $amount <= 0) {
    jsonResponse(['success' => false, 'message' => 'Invalid input data.'], 400);
}

try {
    $data = [
        'user_id' => $user_id,
        'trigger_task_number' => $trigger_number,
        'product_id_override' => $product_id,
        'override_amount' => $amount,
        'admin_id_created' => getCurrentAdminId()
    ];

    $insert_id = insertRecord('negative_settings', $data);

    if ($insert_id) {
        jsonResponse(['success' => true, 'message' => 'Negative setting added successfully!']);
    } else {
        jsonResponse(['success' => false, 'message' => 'Failed to add negative setting.'], 500);
    }

} catch (Exception $e) {
    logError('Error adding negative setting: ' . $e->getMessage());
    jsonResponse(['success' => false, 'message' => 'An unexpected error occurred.'], 500);
}

?>