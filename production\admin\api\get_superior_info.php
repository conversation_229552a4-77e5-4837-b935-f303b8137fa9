<?php
/**
 * Bamboo Web Application - Get Superior Info API
 * Company: Notepadsly
 * Version: 1.0
 */

define('BAMBOO_APP', true);
require_once '../../../includes/config.php';
require_once '../../../includes/functions.php';

if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

header('Content-Type: application/json');

// Check if admin is logged in
if (!isAdminLoggedIn()) {
    jsonResponse(['success' => false, 'message' => 'Unauthorized access.'], 401);
}

// Only allow GET requests
if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
    jsonResponse(['success' => false, 'message' => 'Invalid request method.'], 405);
}

$user_id = (int)($_GET['user_id'] ?? 0);

if ($user_id <= 0) {
    jsonResponse(['success' => false, 'message' => 'Invalid user ID.'], 400);
}

try {
    $sql = "SELECT id, username, phone FROM users WHERE id = ?";
    $superior_info = fetchRow($sql, [$user_id]);

    if ($superior_info) {
        jsonResponse(['success' => true, 'superior' => $superior_info]);
    } else {
        jsonResponse(['success' => false, 'message' => 'Superior not found.'], 404);
    }

} catch (Exception $e) {
    logError('Error fetching superior info: ' . $e->getMessage());
    jsonResponse(['success' => false, 'message' => 'An unexpected error occurred.'], 500);
}

?>