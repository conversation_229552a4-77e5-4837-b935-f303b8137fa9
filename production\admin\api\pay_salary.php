<?php
/**
 * Bamboo Web Application - Pay Salary API
 * Company: Notepadsly
 * Version: 1.0
 */

ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

define('BAMBOO_APP', true);
require_once '../../../includes/config.php';
require_once '../../../includes/functions.php';

if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

header('Content-Type: application/json');

// Check if admin is logged in
if (!isAdminLoggedIn()) {
    jsonResponse(['success' => false, 'message' => 'Unauthorized access.'], 401);
}

// Only allow POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    jsonResponse(['success' => false, 'message' => 'Invalid request method.'], 405);
}

// Validate CSRF token
if (!verifyCSRFToken($_POST['csrf_token'] ?? '')) {
    jsonResponse(['success' => false, 'message' => 'Invalid security token. Please try again.'], 403);
}

$user_id = (int)($_POST['user_id'] ?? 0);
$amount = (float)($_POST['amount'] ?? 0);
$notes = sanitizeInput($_POST['notes'] ?? '');

if ($user_id <= 0 || $amount <= 0) {
    jsonResponse(['success' => false, 'message' => 'Invalid input data.'], 400);
}

try {
    beginTransaction();

    // Add salary to user's balance
    $user = fetchRow("SELECT id, balance FROM users WHERE id = ?", [$user_id]);
    if (!$user) {
        rollbackTransaction();
        jsonResponse(['success' => false, 'message' => 'User not found.'], 404);
    }

    $new_balance = $user['balance'] + $amount;
    $update_success = updateRecord('users', ['balance' => $new_balance], 'id = ?', [$user_id]);

    if (!$update_success) {
        rollbackTransaction();
        jsonResponse(['success' => false, 'message' => 'Failed to update user balance.'], 500);
    }

    // Record salary payment
    $salary_data = [
        'user_id' => $user_id,
        'amount' => $amount,
        'status' => 'paid',
        'notes' => $notes,
        'admin_id_processed' => getCurrentAdminId()
    ];
    $salary_id = insertRecord('user_salaries', $salary_data);

    if (!$salary_id) {
        rollbackTransaction();
        jsonResponse(['success' => false, 'message' => 'Failed to record salary payment.'], 500);
    }

    // Record transaction as 'salary'
    $transaction_data = [
        'user_id' => $user_id,
        'type' => 'salary',
        'amount' => $amount,
        'status' => 'completed',
        'description' => 'Salary payment. Notes: ' . ($notes ?: 'N/A'),
        'admin_id_processed' => getCurrentAdminId()
    ];
    $transaction_id = insertRecord('transactions', $transaction_data);

    if (!$transaction_id) {
        rollbackTransaction();
        jsonResponse(['success' => false, 'message' => 'Failed to record transaction.'], 500);
    }

    commitTransaction();
    jsonResponse(['success' => true, 'message' => 'Salary paid successfully!']);

} catch (Exception $e) {
    rollbackTransaction();
    logError('Error paying salary: ' . $e->getMessage());
    jsonResponse(['success' => false, 'message' => 'An unexpected error occurred.'], 500);
}

?>
