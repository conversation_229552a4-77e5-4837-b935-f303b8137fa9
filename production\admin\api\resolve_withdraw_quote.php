<?php
/**
 * Bamboo Web Application - Resolve Withdrawal Quote API
 * Company: Notepadsly
 * Version: 1.0
 */

define('BAMBOO_APP', true);
require_once '../../../includes/config.php';
require_once '../../../includes/functions.php';

if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

header('Content-Type: application/json');

// Check if admin is logged in
if (!isAdminLoggedIn()) {
    jsonResponse(['success' => false, 'message' => 'Unauthorized access.'], 401);
}

// Only allow POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    jsonResponse(['success' => false, 'message' => 'Invalid request method.'], 405);
}

// Validate CSRF token
if (!verifyCSRFToken($_POST['csrf_token'] ?? '')) {
    jsonResponse(['success' => false, 'message' => 'Invalid security token. Please try again.'], 403);
}

$quote_id = (int)($_POST['quote_id'] ?? 0);

if ($quote_id <= 0) {
    jsonResponse(['success' => false, 'message' => 'Invalid quote ID.'], 400);
}

try {
    $update_success = updateRecord('withdrawal_quotes', ['status' => 'resolved'], 'id = ?', [$quote_id]);

    if ($update_success) {
        jsonResponse(['success' => true, 'message' => 'Withdrawal quote marked as resolved!']);
    } else {
        jsonResponse(['success' => false, 'message' => 'Failed to resolve withdrawal quote.'], 500);
    }

} catch (Exception $e) {
    logError('Error resolving withdrawal quote: ' . $e->getMessage());
    jsonResponse(['success' => false, 'message' => 'An unexpected error occurred.'], 500);
}

?>