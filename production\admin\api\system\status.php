<?php
/**
 * Bamboo Web Application - System Status API
 * Company: Notepadsly
 * Version: 1.0
 */

// Define app constant
define('BAMBOO_APP', true);

// Include required files
require_once '../../../includes/config.php';
require_once '../../../includes/functions.php';

// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Check if admin is logged in
if (!isAdminLoggedIn()) {
    header('Content-Type: application/json');
    echo json_encode([
        'success' => false,
        'message' => 'Unauthorized access',
        'status' => 'offline'
    ]);
    exit;
}

// Check database connection
$db_status = testDatabaseConnection() ? 'online' : 'offline';

// Check if maintenance mode is enabled
$maintenance_mode = getAppSetting('maintenance_mode', '0') === '1';
$status = $maintenance_mode ? 'maintenance' : $db_status;

// Return status
header('Content-Type: application/json');
echo json_encode([
    'success' => true,
    'status' => $status,
    'database' => $db_status,
    'maintenance_mode' => $maintenance_mode,
    'timestamp' => date('Y-m-d H:i:s')
]);
exit;
?>