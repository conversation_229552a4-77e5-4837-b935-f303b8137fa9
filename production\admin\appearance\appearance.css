/**
 * Bamboo Web Application - Appearance Settings Styles
 * Company: Notepadsly
 * Version: 1.0
 */

/* Fix footer spacing on appearance page */
.admin-content {
    padding-bottom: 2rem !important;
}

/* Ensure proper spacing for appearance sections */
.appearance-section:last-child {
    margin-bottom: 1rem !important;
}

/* Ensure table header text color works on appearance page */
.table thead th {
    color: var(--admin-table-header-text, #ffffff) !important;
}

/* Fix any potential color conflicts */
.admin-content .table thead th {
    color: var(--admin-table-header-text, #ffffff) !important;
}

/* Mini preview table styling */
.preview-container .table {
    font-size: 0.75rem;
    margin-bottom: 0;
}

.preview-container .table thead th {
    padding: 0.5rem 0.25rem;
    font-size: 0.7rem;
    background: var(--admin-table-header-bg, var(--admin-primary)) !important;
    color: var(--admin-table-header-text, #ffffff) !important;
    border: none;
}

.preview-container .table tbody td {
    padding: 0.4rem 0.25rem;
    font-size: 0.7rem;
    border-top: 1px solid #dee2e6;
}

.preview-container .btn {
    font-size: 0.6rem;
    padding: 0.2rem 0.4rem;
}

.preview-container .badge {
    font-size: 0.6rem;
    padding: 0.2rem 0.4rem;
}

/* ===== APPEARANCE SETTINGS STYLES ===== */

/* Appearance Cards */
.appearance-card {
    border: none;
    border-radius: 1rem;
    box-shadow: 0 0.125rem 0.5rem rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
    background: #ffffff;
    overflow: hidden;
}

.appearance-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 0.5rem 1.5rem rgba(0, 0, 0, 0.12);
}

.appearance-card .card-header {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    padding: 1.25rem 1.5rem;
}

.appearance-card .card-body {
    padding: 1.5rem;
}

/* Color Input Groups */
.color-input-group {
    display: flex;
    gap: 0.5rem;
    align-items: center;
}

.form-control-color {
    width: 60px;
    height: 40px;
    border: 2px solid #e9ecef;
    border-radius: 0.5rem;
    cursor: pointer;
    transition: all 0.3s ease;
}

.form-control-color:hover {
    border-color: var(--admin-primary);
    transform: scale(1.05);
}

.color-text {
    flex: 1;
    font-family: 'Courier New', monospace;
    font-size: 0.875rem;
    text-transform: uppercase;
}

/* Gradient Preview */
.gradient-preview {
    height: 80px;
    border-radius: 0.75rem;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 600;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
    margin-top: 1rem;
    background: linear-gradient(135deg, var(--admin-primary, #ff6900) 0%, var(--dynamic-gradient-end, #ff8533) 100%);
    transition: all 0.3s ease;
}

/* Live Preview Styles */
.preview-container {
    background: #f8f9fa;
    border-radius: 0.75rem;
    padding: 1rem;
    border: 2px dashed #dee2e6;
}

.mini-dashboard {
    display: flex;
    height: 200px;
    border-radius: 0.5rem;
    overflow: hidden;
    box-shadow: 0 0.25rem 0.75rem rgba(0, 0, 0, 0.1);
    background: white;
}

.mini-sidebar {
    width: 80px;
    background: linear-gradient(180deg, #ff6900 0%, #ff8533 100%);
    color: white;
    padding: 0.75rem 0.5rem;
    display: flex;
    flex-direction: column;
    transition: all 0.3s ease;
}

.mini-brand {
    font-size: 0.75rem;
    font-weight: 700;
    margin-bottom: 1rem;
    text-align: center;
    padding: 0.25rem;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 0.25rem;
}

.mini-nav {
    flex: 1;
}

.mini-nav-item {
    font-size: 0.625rem;
    padding: 0.5rem 0.25rem;
    margin-bottom: 0.25rem;
    border-radius: 0.25rem;
    text-align: center;
    cursor: pointer;
    transition: all 0.2s ease;
}

.mini-nav-item:hover {
    background: rgba(255, 255, 255, 0.1);
}

.mini-nav-item.active {
    background: rgba(255, 255, 255, 0.2);
    font-weight: 600;
}

.mini-content {
    flex: 1;
    padding: 0.75rem;
    background: #f8f9fa;
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.mini-card {
    background: white;
    border-radius: 0.375rem;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    overflow: hidden;
    transition: all 0.3s ease;
}

.mini-card-header {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    padding: 0.5rem 0.75rem;
    font-size: 0.625rem;
    font-weight: 600;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.mini-card-body {
    padding: 0.75rem;
    font-size: 0.625rem;
    color: #6c757d;
    text-align: center;
}

/* Form Controls */
.form-select {
    border: 1px solid #e9ecef;
    border-radius: 0.5rem;
    padding: 0.75rem 1rem;
    transition: all 0.3s ease;
}

.form-select:focus {
    border-color: var(--admin-primary);
    box-shadow: 0 0 0 0.2rem rgba(255, 105, 0, 0.25);
}

/* Buttons */
.btn {
    border-radius: 0.5rem;
    font-weight: 500;
    padding: 0.75rem 1.5rem;
    transition: all 0.3s ease;
}

.btn:hover {
    transform: translateY(-1px);
}

.btn-primary {
    background: linear-gradient(135deg, var(--admin-primary) 0%, rgba(var(--admin-primary-rgb), 0.8) 100%);
    border: none;
    box-shadow: 0 0.25rem 0.75rem rgba(var(--admin-primary-rgb), 0.3);
}

.btn-primary:hover {
    box-shadow: 0 0.5rem 1.5rem rgba(var(--admin-primary-rgb), 0.4);
}

.btn-outline-secondary {
    border: 2px solid #e9ecef;
    color: #6c757d;
}

.btn-outline-secondary:hover {
    background: #f8f9fa;
    border-color: #dee2e6;
    color: #495057;
}

/* Responsive Design */
@media (max-width: 768px) {
    .color-input-group {
        flex-direction: column;
        align-items: stretch;
    }
    
    .form-control-color {
        width: 100%;
        height: 50px;
    }
    
    .mini-dashboard {
        height: 150px;
    }
    
    .mini-sidebar {
        width: 60px;
    }
    
    .mini-brand {
        font-size: 0.625rem;
    }
    
    .mini-nav-item {
        font-size: 0.5rem;
        padding: 0.375rem 0.125rem;
    }
    
    .appearance-card .card-body {
        padding: 1rem;
    }
}

/* Animation for form changes */
.form-control, .form-select {
    transition: all 0.3s ease;
}

.form-control:focus, .form-select:focus {
    transform: scale(1.02);
}

/* Color picker enhancements */
.color-input-group .form-control-color::-webkit-color-swatch-wrapper {
    padding: 0;
    border-radius: 0.375rem;
}

.color-input-group .form-control-color::-webkit-color-swatch {
    border: none;
    border-radius: 0.375rem;
}

/* Loading states */
.loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: inherit;
    z-index: 10;
}

.loading-spinner {
    width: 2rem;
    height: 2rem;
    border: 0.25rem solid #f3f3f3;
    border-top: 0.25rem solid var(--admin-primary);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Success states */
.success-feedback {
    color: #28a745;
    font-size: 0.875rem;
    margin-top: 0.5rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.success-feedback i {
    font-size: 1rem;
}

/* Card hover effects */
.appearance-card {
    position: relative;
    overflow: hidden;
}

.appearance-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 2px;
    background: linear-gradient(90deg, transparent, var(--admin-primary), transparent);
    transition: left 0.5s ease;
}

.appearance-card:hover::before {
    left: 100%;
}

/* Form validation styles */
.is-invalid {
    border-color: #dc3545;
}

.invalid-feedback {
    color: #dc3545;
    font-size: 0.875rem;
    margin-top: 0.25rem;
}

/* Tooltip styles */
.tooltip-inner {
    background: var(--admin-text-dark);
    border-radius: 0.375rem;
    font-size: 0.75rem;
}

.tooltip.bs-tooltip-top .tooltip-arrow::before {
    border-top-color: var(--admin-text-dark);
}

/* Color Palette Buttons */
.color-palette-container {
    border-top: 1px solid var(--admin-border-light);
    padding-top: 1rem;
    margin-top: 1rem;
}

.color-palette-container h6 {
    color: var(--admin-text-dark);
    font-weight: 600;
    font-size: 0.875rem;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.color-dot {
    display: inline-block;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    margin-right: 0.5rem;
    border: 1px solid rgba(0, 0, 0, 0.1);
}

.color-palette-container .btn {
    display: flex;
    align-items: center;
    font-size: 0.75rem;
    padding: 0.375rem 0.75rem;
    border-radius: 0.375rem;
    transition: all 0.2s ease;
}

.color-palette-container .btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 0.25rem 0.75rem rgba(0, 0, 0, 0.1);
}
