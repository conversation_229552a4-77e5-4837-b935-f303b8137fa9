/* Member Details Styling */
.info-group {
    margin-bottom: 1rem;
}

.info-label {
    font-weight: bold;
    color: #555;
}

.info-value {
    color: #333;
}

.info-row {
    display: flex;
    justify-content: space-between;
    padding: 0.5rem 0;
    border-bottom: 1px solid #eee;
}

.info-row:last-child {
    border-bottom: none;
}

/* Quick Actions Styling */
.quick-actions-card {
    box-shadow: 0 0.5rem 1.5rem rgba(0, 0, 0, 0.1);
    border: none;
    border-radius: 1rem;
    overflow: hidden;
}

.quick-actions-card .card-header {
    border-bottom: none;
    padding: 1.5rem;
    position: relative;
    overflow: hidden;
}

.quick-actions-card .card-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, rgba(255,255,255,0.1) 0%, transparent 50%, rgba(255,255,255,0.1) 100%);
    pointer-events: none;
}

.quick-action-item {
    display: flex;
    align-items: center;
    padding: 1.5rem;
    text-decoration: none;
    color: inherit;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    border-right: 1px solid rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.quick-action-item:hover {
    background: rgba(0, 123, 255, 0.05);
    color: inherit;
    text-decoration: none;
    transform: translateY(-2px);
    box-shadow: 0 0.25rem 0.75rem rgba(0, 0, 0, 0.1);
}

.quick-action-item.danger:hover {
    background: rgba(220, 53, 69, 0.05);
}

.quick-action-icon {
    width: 50px;
    height: 50px;
    border-radius: 0.75rem;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.25rem;
    margin-right: 1rem;
    flex-shrink: 0;
    box-shadow: 0 0.25rem 0.75rem rgba(0, 0, 0, 0.2);
}

.quick-action-content {
    flex: 1;
}

.quick-action-title {
    font-weight: 600;
    font-size: 0.95rem;
    margin-bottom: 0.25rem;
    color: #2c3e50;
}

.quick-action-desc {
    font-size: 0.8rem;
    color: #6c757d;
    margin: 0;
}

/* Remove borders from last items in each row */
.quick-action-item:nth-child(4n) {
    border-right: none;
}

/* Financial Items Styling */
.financial-item {
    padding: 1rem;
    background: rgba(0, 0, 0, 0.02);
    border-radius: 0.5rem;
    margin-bottom: 1rem;
    border: 1px solid rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
}

.financial-item:hover {
    background: rgba(0, 0, 0, 0.03);
    border-color: rgba(0, 123, 255, 0.2);
    transform: translateY(-1px);
    box-shadow: 0 0.125rem 0.5rem rgba(0, 0, 0, 0.05);
}

.financial-label {
    font-size: 0.875rem;
    color: #6c757d;
    margin-bottom: 0.5rem;
    font-weight: 500;
}

.financial-value {
    font-size: 1.25rem;
    margin: 0;
}

/* Header Avatar Styling */
.user-avatar-header {
    flex-shrink: 0;
}

.header-avatar {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    object-fit: cover;
    border: 3px solid rgba(0, 123, 255, 0.2);
    transition: all 0.3s ease;
}

.header-avatar:hover {
    transform: scale(1.05);
    border-color: #007bff;
}

.header-avatar-initials {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: linear-gradient(135deg, #007bff, #0056b3);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    font-size: 1.25rem;
    border: 3px solid rgba(0, 123, 255, 0.2);
    transition: all 0.3s ease;
}

.header-avatar-initials:hover {
    transform: scale(1.05);
    box-shadow: 0 0.5rem 1rem rgba(0, 123, 255, 0.3);
}

/* Wallet Address Styling */
.wallet-address-container {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    flex: 1;
}

.wallet-address {
    font-family: 'Courier New', monospace;
    background: rgba(0, 123, 255, 0.05);
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
    border: 1px solid rgba(0, 123, 255, 0.1);
    font-size: 0.875rem;
    word-break: break-all;
    flex: 1;
}

.copy-btn {
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
    border-radius: 0.25rem;
    transition: all 0.3s ease;
}

.copy-btn:hover {
    background: #007bff;
    border-color: #007bff;
    color: white;
    transform: scale(1.05);
}

.copy-btn.copied {
    background: #28a745;
    border-color: #28a745;
    color: white;
}

/* Enhanced Card Headers */
.card-header h5 {
    display: flex;
    align-items: center;
}

.card-header h5 i {
    color: #007bff;
}

/* Enhanced Info Rows */
.info-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem 0;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
}

.info-row:hover {
    background: rgba(0, 123, 255, 0.02);
    padding-left: 0.5rem;
    padding-right: 0.5rem;
    margin-left: -0.5rem;
    margin-right: -0.5rem;
    border-radius: 0.25rem;
}

.info-row:last-child {
    border-bottom: none;
}

.info-label {
    font-weight: 600;
    color: #495057;
    font-size: 0.9rem;
}

.info-value {
    color: #212529;
    font-weight: 500;
}

/* Info Item Styling for Member Management Pages */
.info-item {
    padding: 1rem;
    background: rgba(0, 0, 0, 0.02);
    border-radius: 0.5rem;
    border: 1px solid rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
    margin-bottom: 1rem;
}

.info-item:hover {
    background: rgba(0, 123, 255, 0.02);
    border-color: rgba(0, 123, 255, 0.1);
    transform: translateY(-1px);
    box-shadow: 0 0.125rem 0.5rem rgba(0, 0, 0, 0.05);
}

.info-item .info-label {
    font-size: 0.875rem;
    color: #6c757d;
    margin-bottom: 0.5rem;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.info-item .info-value {
    font-size: 1.1rem;
    color: #212529;
    font-weight: 600;
    margin: 0;
}

/* Task Progress Styling */
.task-progress {
    display: flex;
    align-items: center;
    gap: 0.25rem;
    margin-bottom: 0.5rem;
}

.completed-tasks {
    font-size: 1.25rem;
}

.task-separator {
    font-size: 1rem;
    color: #6c757d;
}

.total-tasks {
    font-size: 1rem;
}

/* Enhanced Form Styling */
.form-label i {
    font-size: 0.875rem;
}

.form-text {
    font-size: 0.8rem;
    color: #6c757d;
    margin-top: 0.25rem;
}

/* Placeholder Styling */
.form-control::placeholder,
.form-select::placeholder,
textarea::placeholder {
    color: #6c757d !important;
    opacity: 0.7;
    font-style: italic;
}

.form-control:focus::placeholder,
.form-select:focus::placeholder,
textarea:focus::placeholder {
    color: #adb5bd !important;
    opacity: 0.5;
}

/* Enhanced Table Styling */
.table th {
    background: rgba(0, 123, 255, 0.05);
    border-bottom: 2px solid rgba(0, 123, 255, 0.1);
    font-weight: 600;
    color: #495057;
    font-size: 0.9rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.table td {
    vertical-align: middle;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.table-hover tbody tr:hover {
    background: rgba(0, 123, 255, 0.02);
}

/* Responsive Design */
@media (max-width: 768px) {
    .quick-action-item {
        border-right: none;
        border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    }

    .quick-action-icon {
        width: 40px;
        height: 40px;
        font-size: 1rem;
    }

    .quick-action-title {
        font-size: 0.875rem;
    }

    .quick-action-desc {
        font-size: 0.75rem;
    }

    .financial-value {
        font-size: 1.1rem;
    }
}
