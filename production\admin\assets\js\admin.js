$(document).ready(function() {
    // Add a loading indicator to all forms
    $('form').on('submit', function() {
        // Disable the submit button to prevent multiple submissions
        $(this).find('button[type="submit"]').prop('disabled', true).html('<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Loading...');
    });

    // Sidebar scroll position preservation
    const SIDEBAR_SCROLL_KEY = 'admin_sidebar_scroll_position';
    const sidebar = $('.admin-sidebar');

    // Restore scroll position on page load
    if (sidebar.length) {
        const savedScrollPosition = sessionStorage.getItem(SIDEBAR_SCROLL_KEY);
        if (savedScrollPosition) {
            sidebar.scrollTop(parseInt(savedScrollPosition, 10));
        }

        // Save scroll position when scrolling
        sidebar.on('scroll', function() {
            sessionStorage.setItem(SIDEBAR_SCROLL_KEY, $(this).scrollTop());
        });

        // Save scroll position before navigation
        $('.admin-sidebar .nav-link').on('click', function() {
            sessionStorage.setItem(SIDEBAR_SCROLL_KEY, sidebar.scrollTop());
        });

        // Also save on window beforeunload (for browser navigation)
        $(window).on('beforeunload', function() {
            sessionStorage.setItem(SIDEBAR_SCROLL_KEY, sidebar.scrollTop());
        });
    }
});
