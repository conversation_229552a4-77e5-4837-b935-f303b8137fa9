<?php
/**
 * Bamboo Web Application - Admin Database Backup Page
 * Company: Notepadsly
 * Version: 1.0
 */

// Prevent direct access
if (!defined('BAMBOO_APP')) {
    define('BAMBOO_APP', true); // Define BAMBOO_APP to allow includes
}

require_once '../../includes/config.php';
require_once '../../includes/functions.php';
require_once '../../includes/database.php'; // For tableExists and other DB functions

// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Check if admin is logged in
if (!isAdminLoggedIn()) {
    redirect('admin/login/');
    exit;
}

$page_title = 'Database Backup';
$page_section = 'backup';

$backup_message = '';
$backup_error = '';

// Ensure UPLOAD_PATH is defined and create backups directory if it doesn't exist
$backup_dir = UPLOAD_PATH . 'backups/';
if (!is_dir($backup_dir)) {
    if (!mkdir($backup_dir, 0755, true)) {
        $backup_error = "Error: Could not create backup directory: {$backup_dir}";
    }
}

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['backup_database'])) {
    if (empty($backup_error)) {
        $db_host = DB_HOST;
        $db_name = DB_NAME;
        $db_user = DB_USER;
        $db_pass = DB_PASS;
        $db_port = DB_PORT;

        $timestamp = date('Y-m-d_H-i-s');
        $backup_file = $backup_dir . 'db_backup_' . $timestamp . '.sql';

        // Command to execute mysqldump
        // For Windows (MAMP), specify the full path to mysqldump.exe
        // For Linux/macOS, "mysqldump" should be sufficient if it's in the PATH.
        // You might need to adjust this path based on your MAMP installation.
        $mysqldump_path = 'C:\\MAMP\\bin\\mysql\\bin\\mysqldump.exe'; // Assuming MAMP default path

        // If mysqldump is not found at the specified path, try the default system path
        if (!file_exists($mysqldump_path)) {
            $mysqldump_path = 'mysqldump'; // Fallback to system PATH
        }
        
        $command = "\"{$mysqldump_path}\" --host={$db_host} --port={$db_port} --user={$db_user} --password={$db_pass} {$db_name} > \"{$backup_file}\" 2>&1";

        // Execute the command
        $output = [];
        $return_var = 0;
        exec($command, $output, $return_var);

        if ($return_var === 0) {
            $backup_message = "Database backup successful! File: " . basename($backup_file);
        } else {
            $backup_error = "Database backup failed. Return code: {$return_var}. Output: " . implode("\n", $output);
            // Log the error for debugging
            error_log("Database backup failed: Return code: {$return_var}. Output: " . implode("\n", $output));
        }
    }
}

// Get list of existing backup files
$backup_files = [];
if (is_dir($backup_dir)) {
    $files = scandir($backup_dir);
    foreach ($files as $file) {
        if (preg_match('/^db_backup_(\d{4}-\d{2}-\d{2}_\d{2}-\d{2}-\d{2})\.sql$/', $file)) {
            $backup_files[] = [
                'name' => $file,
                'path' => BASE_URL . 'uploads/backups/' . $file,
                'size' => filesize($backup_dir . $file),
                'date' => filemtime($backup_dir . $file)
            ];
        }
    }
    // Sort by date, newest first
    usort($backup_files, function($a, $b) {
        return $b['date'] <=> $a['date'];
    });
}
?>

<?php include '../includes/admin_header.php'; ?>

<div class="admin-wrapper">
    <!-- Sidebar -->
    <?php include '../includes/admin_sidebar.php'; ?>

    <!-- Main Content -->
    <div class="admin-main">
        <!-- Top Header -->
        <?php include '../includes/admin_topbar.php'; ?>

        <!-- Content Area -->
        <div class="admin-content">
            <div class="container-fluid">

                <!-- Page Header -->
                <div class="page-header">
                    <h1><?php echo $page_title; ?></h1>
                    <p class="text-muted">Create and manage database backups</p>
                </div>

            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-database-fill-add me-2"></i>Trigger New Backup
                    </h5>
                </div>
                <div class="card-body">
                    <?php if ($backup_message): ?>
                        <div class="alert alert-success" role="alert">
                            <?php echo htmlspecialchars($backup_message); ?>
                        </div>
                    <?php endif; ?>
                    <?php if ($backup_error): ?>
                        <div class="alert alert-danger" role="alert">
                            <?php echo htmlspecialchars($backup_error); ?>
                        </div>
                    <?php endif; ?>

                    <form method="POST">
                        <p>Click the button below to create a new backup of the database.</p>
                        <button type="submit" name="backup_database" class="btn btn-primary">
                            <i class="bi bi-database-fill-add"></i> Create Database Backup
                        </button>
                    </form>
                </div>
            </div>

            <div class="card mt-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-archive me-2"></i>Existing Backups
                    </h5>
                </div>
                <div class="card-body">
                    <?php if (empty($backup_files)): ?>
                        <p>No database backups found.</p>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead>
                                    <tr>
                                        <th>File Name</th>
                                        <th>Size</th>
                                        <th>Date Created</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($backup_files as $file): ?>
                                        <tr>
                                            <td><?php echo htmlspecialchars($file['name']); ?></td>
                                            <td><?php echo formatBytes($file['size']); ?></td>
                                            <td><?php echo date('Y-m-d H:i:s', $file['date']); ?></td>
                                            <td>
                                                <a href="<?php echo htmlspecialchars($file['path']); ?>" class="btn btn-sm btn-info" download>
                                                    <i class="bi bi-download"></i> Download
                                                </a>
                                                <!-- Add delete functionality if desired -->
                                                <!-- <a href="?delete=<?php echo urlencode($file['name']); ?>" class="btn btn-sm btn-danger">Delete</a> -->
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            </div>
        </div>

        <!-- Footer -->
        <?php include '../includes/admin_footer.php'; ?>
    </div>
</div>

<?php
$additional_js = [
    BASE_URL . 'admin/assets/js/admin.js'
];
include '../includes/admin_footer_scripts.php';
?>

<?php
// Helper function to format file sizes
function formatBytes($bytes, $precision = 2) {
    $units = array('B', 'KB', 'MB', 'GB', 'TB');
    $bytes = max($bytes, 0);
    $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
    $pow = min($pow, count($units) - 1);
    $bytes /= (1 << (10 * $pow));
    return round($bytes, $precision) . ' ' . $units[$pow];
}
?>
