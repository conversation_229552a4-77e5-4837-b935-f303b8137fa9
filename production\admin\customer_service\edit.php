<?php
define('BAMBOO_APP', true);
require_once '../../includes/config.php';
require_once '../../includes/functions.php';

if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

if (!isAdminLoggedIn()) {
    redirect('admin/login/');
}

$id = (int)$_GET['id'];
$contact = fetchRow('SELECT * FROM customer_service_contacts WHERE id = ?', [$id]);

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (!verifyCSRFToken($_POST['csrf_token'] ?? '')) {
        showError('Invalid security token. Please try again.');
    } else {
        $name = sanitizeInput($_POST['name']);
        $link = sanitizeInput($_POST['link']);

        if (updateRecord('customer_service_contacts', ['name' => $name, 'link' => $link], 'id = ?', [$id])) {
            showSuccess('Contact updated successfully!');
            redirect('admin/customer_service/');
        } else {
            showError('Failed to update contact.');
        }
    }
}

$page_title = 'Edit Customer Service Contact';
include '../includes/admin_header.php';

?>

<div class="admin-wrapper">
    <?php include '../includes/admin_sidebar.php'; ?>
    <div class="admin-main">
        <?php include '../includes/admin_topbar.php'; ?>
        <div class="admin-content">
            <div class="container-fluid">
                <h1 class="h3 mb-4">Edit Customer Service Contact</h1>

                <div class="card">
                    <div class="card-body">
                        <form action="" method="POST">
                            <div class="mb-3">
                                <label for="name" class="form-label">Name</label>
                                <input type="text" class="form-control" id="name" name="name" value="<?php echo htmlspecialchars($contact['name']); ?>" required>
                            </div>
                            <div class="mb-3">
                                <label for="link" class="form-label">Link</label>
                                <input type="text" class="form-control" id="link" name="link" value="<?php echo htmlspecialchars($contact['link']); ?>" required>
                            </div>
                            <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                            <button type="submit" class="btn btn-primary">Submit</button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
        <?php include '../includes/admin_footer.php'; ?>
    </div>
</div>

<?php include '../includes/admin_footer_scripts.php'; ?>
