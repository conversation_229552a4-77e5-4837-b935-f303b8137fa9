<?php
define('BAMBOO_APP', true);
require_once '../../includes/config.php';
require_once '../../includes/functions.php';

if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

if (!isAdminLoggedIn()) {
    redirect('admin/login/');
}

$page_title = 'Customer Service Management';
include '../includes/admin_header.php';

$contacts = fetchAll('SELECT * FROM customer_service_contacts ORDER BY id DESC');

?>

<div class="admin-wrapper">
    <?php include '../includes/admin_sidebar.php'; ?>
    <div class="admin-main">
        <?php include '../includes/admin_topbar.php'; ?>
        <div class="admin-content">
            <div class="container-fluid">
                <h1 class="h3 mb-4">Customer Service Management</h1>

                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">All Contacts</h5>
                        <a href="add.php" class="btn btn-primary mt-3">Add New Contact</a>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>S/N</th>
                                        <th>Name</th>
                                        <th>Link</th>
                                        <th>Creation Time</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php if (!empty($contacts)): foreach ($contacts as $contact): ?>
                                        <tr>
                                            <td><?php echo htmlspecialchars($contact['id']); ?></td>
                                            <td><?php echo htmlspecialchars($contact['name']); ?></td>
                                            <td><?php echo htmlspecialchars($contact['link']); ?></td>
                                            <td><?php echo formatDate($contact['created_at']); ?></td>
                                            <td>
                                                <a href="edit.php?id=<?php echo $contact['id']; ?>" class="btn btn-sm btn-primary">Revise</a>
                                                <a href="delete.php?id=<?php echo $contact['id']; ?>" class="btn btn-sm btn-danger">Delete</a>
                                            </td>
                                        </tr>
                                    <?php endforeach; else: ?>
                                        <tr><td colspan="5" class="text-center text-muted">No contacts found.</td></tr>
                                    <?php endif; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <?php include '../includes/admin_footer.php'; ?>
    </div>
</div>

<?php include '../includes/admin_footer_scripts.php'; ?>