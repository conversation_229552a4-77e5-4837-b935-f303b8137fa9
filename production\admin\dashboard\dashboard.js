/**
 * Bamboo Web Application - Admin Dashboard JavaScript
 * Company: Notepadsly
 * Version: 1.0
 */

$(document).ready(function() {
    
    // Initialize dashboard
    initDashboard();
    
    // Auto-refresh dashboard data every 5 minutes
    setInterval(refreshDashboardData, 300000);
    
});

/**
 * Initialize dashboard functionality
 */
function initDashboard() {
    // Animate statistics cards on load
    animateStatCards();
    
    // Initialize tooltips
    initTooltips();
    
    // Setup real-time updates
    setupRealTimeUpdates();
    
    // Initialize charts if Chart.js is available
    if (typeof Chart !== 'undefined') {
        initDashboardCharts();
    }
}

/**
 * Animate statistics cards
 */
function animateStatCards() {
    $('.stat-card').each(function(index) {
        const $card = $(this);
        setTimeout(function() {
            $card.addClass('animate__animated animate__fadeInUp');
        }, index * 100);
    });
}

/**
 * Initialize tooltips
 */
function initTooltips() {
    $('[data-bs-toggle="tooltip"]').tooltip();
}

/**
 * Setup real-time updates
 */
function setupRealTimeUpdates() {
    // Update timestamps
    updateTimestamps();
    setInterval(updateTimestamps, 60000); // Update every minute
    
    // Check for new notifications
    checkNotifications();
    setInterval(checkNotifications, 30000); // Check every 30 seconds
}

/**
 * Update relative timestamps
 */
function updateTimestamps() {
    $('.timestamp').each(function() {
        const $element = $(this);
        const timestamp = $element.data('timestamp');
        if (timestamp) {
            $element.text(getRelativeTime(timestamp));
        }
    });
}

/**
 * Get relative time string
 */
function getRelativeTime(timestamp) {
    const now = new Date();
    const time = new Date(timestamp);
    const diff = Math.floor((now - time) / 1000);
    
    if (diff < 60) return 'Just now';
    if (diff < 3600) return Math.floor(diff / 60) + ' minutes ago';
    if (diff < 86400) return Math.floor(diff / 3600) + ' hours ago';
    if (diff < 2592000) return Math.floor(diff / 86400) + ' days ago';
    
    return time.toLocaleDateString();
}

/**
 * Check for new notifications
 */
function checkNotifications() {
    // For now, just use a placeholder since the API endpoint doesn't exist yet
    updateNotificationBadge(0);
    
    /* Commented out until API endpoint is created
    $.ajax({
        url: AdminApp.adminUrl + 'api/notifications/check',
        method: 'GET',
        headers: {
            'X-CSRF-Token': AdminApp.csrfToken
        },
        success: function(response) {
            if (response.success && response.count > 0) {
                updateNotificationBadge(response.count);
            }
        },
        error: function() {
            // Silently fail for notifications
        }
    });
    */
}

/**
 * Update notification badge
 */
function updateNotificationBadge(count) {
    const $badge = $('.notification-badge');
    if (count > 0) {
        $badge.text(count > 99 ? '99+' : count).show();
    } else {
        $badge.hide();
    }
}

/**
 * Refresh dashboard data
 */
function refreshDashboardData() {
    $.ajax({
        url: AdminApp.adminUrl + 'api/dashboard/refresh',
        method: 'GET',
        headers: {
            'X-CSRF-Token': AdminApp.csrfToken
        },
        success: function(response) {
            if (response.success) {
                updateDashboardStats(response.data);
            }
        },
        error: function() {
            console.log('Failed to refresh dashboard data');
        }
    });
}

/**
 * Update dashboard statistics
 */
function updateDashboardStats(data) {
    // Update stat values with animation
    Object.keys(data.stats).forEach(function(key) {
        const $element = $('[data-stat="' + key + '"]');
        if ($element.length) {
            animateNumber($element, parseInt($element.text().replace(/,/g, '')), data.stats[key]);
        }
    });
}

/**
 * Animate number changes
 */
function animateNumber($element, from, to) {
    const duration = 1000;
    const steps = 20;
    const increment = (to - from) / steps;
    let current = from;
    let step = 0;
    
    const timer = setInterval(function() {
        current += increment;
        step++;
        
        $element.text(Math.floor(current).toLocaleString());
        
        if (step >= steps) {
            clearInterval(timer);
            $element.text(to.toLocaleString());
        }
    }, duration / steps);
}

/**
 * Initialize dashboard charts
 */
function initDashboardCharts() {
    // Users chart
    initUsersChart();
    
    // Revenue chart
    initRevenueChart();
    
    // Tasks chart
    initTasksChart();
}

/**
 * Initialize users chart
 */
function initUsersChart() {
    const ctx = document.getElementById('usersChart');
    if (!ctx) return;
    
    new Chart(ctx, {
        type: 'line',
        data: {
            labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
            datasets: [{
                label: 'New Users',
                data: [12, 19, 3, 5, 2, 3],
                borderColor: AdminApp.primaryColor,
                backgroundColor: AdminApp.primaryColor + '20',
                tension: 0.4
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                }
            },
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });
}

/**
 * Initialize revenue chart
 */
function initRevenueChart() {
    const ctx = document.getElementById('revenueChart');
    if (!ctx) return;
    
    new Chart(ctx, {
        type: 'bar',
        data: {
            labels: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
            datasets: [{
                label: 'Revenue',
                data: [1200, 1900, 800, 1500, 2000, 1800, 2400],
                backgroundColor: AdminApp.primaryColor + '80',
                borderColor: AdminApp.primaryColor,
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                }
            },
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });
}

/**
 * Initialize tasks chart
 */
function initTasksChart() {
    const ctx = document.getElementById('tasksChart');
    if (!ctx) return;
    
    new Chart(ctx, {
        type: 'doughnut',
        data: {
            labels: ['Completed', 'Pending', 'Failed'],
            datasets: [{
                data: [65, 25, 10],
                backgroundColor: [
                    '#28a745',
                    '#ffc107',
                    '#dc3545'
                ]
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom'
                }
            }
        }
    });
}

/**
 * Quick action handlers
 */
$(document).on('click', '.quick-action', function(e) {
    e.preventDefault();
    const action = $(this).data('action');
    
    switch(action) {
        case 'add-user':
            window.location.href = AdminApp.adminUrl + 'member_management/add/';
            break;
        case 'add-product':
            window.location.href = AdminApp.adminUrl + 'products/add/';
            break;
        case 'system-settings':
            window.location.href = AdminApp.adminUrl + 'settings/';
            break;
        default:
            console.log('Unknown quick action:', action);
    }
});

/**
 * Search functionality
 */
$(document).on('keyup', '#admin-search', function() {
    const query = $(this).val();
    if (query.length >= 3) {
        performSearch(query);
    }
});

/**
 * Perform search
 */
function performSearch(query) {
    $.ajax({
        url: AdminApp.adminUrl + 'api/search',
        method: 'GET',
        data: { q: query },
        headers: {
            'X-CSRF-Token': AdminApp.csrfToken
        },
        success: function(response) {
            if (response.success) {
                displaySearchResults(response.results);
            }
        },
        error: function() {
            console.log('Search failed');
        }
    });
}

/**
 * Display search results
 */
function displaySearchResults(results) {
    // Implement search results display
    console.log('Search results:', results);
}

/**
 * System status check
 */
function checkSystemStatus() {
    // For now, just assume the system is online since the API endpoint doesn't exist yet
    updateSystemStatus('online');
    
    /* Commented out until API endpoint is created
    $.ajax({
        url: AdminApp.adminUrl + 'api/system/status',
        method: 'GET',
        headers: {
            'X-CSRF-Token': AdminApp.csrfToken
        },
        success: function(response) {
            updateSystemStatus(response.status);
        },
        error: function() {
            updateSystemStatus('offline');
        }
    });
    */
}

/**
 * Update system status indicator
 */
function updateSystemStatus(status) {
    const $indicator = $('.system-status');
    $indicator.removeClass('bg-success bg-warning bg-danger');
    
    switch(status) {
        case 'online':
            $indicator.addClass('bg-success').text('System Online');
            break;
        case 'maintenance':
            $indicator.addClass('bg-warning').text('Maintenance Mode');
            break;
        default:
            $indicator.addClass('bg-danger').text('System Offline');
    }
}

// Check system status on load
$(document).ready(function() {
    checkSystemStatus();
    setInterval(checkSystemStatus, 60000); // Check every minute
});