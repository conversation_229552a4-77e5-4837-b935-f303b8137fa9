<?php
/**
 * Bamboo Web Application - Admin Dashboard
 * Company: Notepadsly
 * Version: 1.0
 */

// Prevent direct access
if (!defined('BAMBOO_APP')) {
    die('Direct access not permitted');
}

// Get dashboard statistics
try {
    checkDatabase();
    
    // Check if tables exist before querying
    $stats = [];
    $recent_users = [];
    $recent_transactions = [];
    
    if (tableExists('admin_user_stats')) {
        $user_stats = fetchRow('SELECT * FROM admin_user_stats');
        $stats['total_users'] = $user_stats['total_users'];
        $stats['active_users'] = $user_stats['active_users'];
        $stats['total_deposits'] = $user_stats['total_deposits'];
        $stats['total_withdrawals'] = $user_stats['total_withdrawals'];
    } else {
        $stats['total_users'] = 0;
        $stats['active_users'] = 0;
        $stats['total_deposits'] = 0;
        $stats['total_withdrawals'] = 0;
    }
    
    // --- Dashboard Statistics Queries ---
    // Total users
    $stats['total_users'] = getRecordCount('users');
    // Current active users (status = 'active')
    $stats['active_users'] = getRecordCount('users', 'status = ?', ['active']);
    // Total amount of goods (sum of all product stock)
    $stats['total_goods'] = fetchRow("SELECT SUM(stock) as total FROM products")['total'] ?? 0;
    // Current total quantity of goods (active products)
    $stats['current_goods'] = fetchRow("SELECT SUM(stock) as total FROM products WHERE status = 'active'")['total'] ?? 0;
    // Total completed orders (order = completed tasks)
    $stats['completed_orders'] = getRecordCount('tasks', 'status = ?', ['completed']);
    // Total orders submitted (all tasks)
    $stats['total_orders'] = getRecordCount('tasks');
    // Completed order amount (sum of completed task amounts)
    $stats['completed_order_amount'] = fetchRow("SELECT SUM(amount) as total FROM tasks WHERE status = 'completed'")['total'] ?? 0;
    // Total order amount submitted (all tasks)
    $stats['total_order_amount'] = fetchRow("SELECT SUM(amount) as total FROM tasks")['total'] ?? 0;
    // Recharge order amount (approved deposits)
    $stats['recharge_order_amount'] = fetchRow("SELECT SUM(amount) as total FROM transactions WHERE type = 'deposit' AND status = 'approved'")['total'] ?? 0;
    // Cash withdrawal order amount (approved withdrawals)
    $stats['withdrawal_order_amount'] = fetchRow("SELECT SUM(amount) as total FROM transactions WHERE type = 'withdrawal' AND status = 'approved'")['total'] ?? 0;
    // Total amount issued (all order revenue + activity rewards)
    $stats['total_issued'] = ($stats['completed_order_amount'] ?? 0) + fetchRow("SELECT SUM(amount) as total FROM transactions WHERE type = 'reward'")['total'] ?? 0;
    
    // Products stats
    if (tableExists('products')) {
        $stats['total_products'] = getRecordCount('products');
        $stats['active_products'] = getRecordCount('products', 'status = ?', ['active']);
    } else {
        $stats['total_products'] = 0;
        $stats['active_products'] = 0;
    }
    
    // Tasks stats
    if (tableExists('tasks')) {
        $stats['total_tasks'] = getRecordCount('tasks');
    } else {
        $stats['total_tasks'] = 0;
    }
    
    // Task completions
    if (tableExists('task_completions')) {
        $stats['completed_tasks'] = getRecordCount('task_completions');
    } else {
        $stats['completed_tasks'] = 0;
    }
    
    // Transactions
    if (tableExists('transactions')) {
        $stats['total_transactions'] = getRecordCount('transactions');
        $stats['pending_withdrawals'] = getRecordCount('transactions', 'type = ? AND status = ?', ['withdrawal', 'pending']);
        $recent_transactions = fetchAll("SELECT t.*, u.username, u.avatar FROM transactions t LEFT JOIN users u ON t.user_id = u.id ORDER BY t.created_at DESC LIMIT 5");
    } else {
        $stats['total_transactions'] = 0;
        $stats['pending_withdrawals'] = 0;
    }
    
    // --- Recent Users (last 5) ---
    $recent_users = fetchAll("SELECT username, avatar, created_at FROM users ORDER BY created_at DESC LIMIT 5");
    
} catch (Exception $e) {
    $stats = [
        'total_users' => 0,
        'active_users' => 0,
        'total_products' => 0,
        'active_products' => 0,
        'total_tasks' => 0,
        'completed_tasks' => 0,
        'total_transactions' => 0,
        'pending_withdrawals' => 0
    ];
    $recent_users = [];
    $recent_transactions = [];
    showError('Dashboard data could not be loaded: ' . $e->getMessage());
}

// Page configuration
$page_title = 'Admin Dashboard';
$body_class = 'admin-page';
$additional_css = [
    BASE_URL . 'admin/assets/css/admin.css',
    BASE_URL . 'admin/dashboard/dashboard.css'
];

// Include admin header
include '../includes/admin_header.php';
?>

<style>
/* Enhanced Recent Items Styling with Visible Borders */
.recent-items-list {
    border: 1px solid rgba(0, 0, 0, 0.08);
    border-radius: 0.5rem;
    overflow: hidden;
}

.recent-item {
    border-bottom: 2px solid #e9ecef !important;
    padding: 1rem 1.25rem !important;
    transition: all 0.3s ease;
    position: relative;
}

.recent-item:last-child {
    border-bottom: none !important;
}

.recent-item:hover {
    background: linear-gradient(135deg, rgba(var(--admin-primary-rgb, 255, 105, 0), 0.03) 0%, rgba(var(--admin-primary-rgb, 255, 105, 0), 0.01) 100%);
    transform: translateX(2px);
    border-left: 3px solid var(--admin-primary, #ff6900);
}

/* Enhanced separator lines */
.recent-item::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 1rem;
    right: 1rem;
    height: 1px;
    background: linear-gradient(90deg, transparent 0%, #dee2e6 10%, #dee2e6 90%, transparent 100%);
}

.recent-item:last-child::after {
    display: none;
}

/* Enhanced card styling for recent items */
.card .card-body {
    padding: 1.5rem;
}

.card .card-header {
    background: linear-gradient(135deg, rgba(var(--admin-primary-rgb, 255, 105, 0), 0.05) 0%, rgba(var(--admin-primary-rgb, 255, 105, 0), 0.02) 100%);
    border-bottom: 2px solid rgba(var(--admin-primary-rgb, 255, 105, 0), 0.1);
    padding: 1rem 1.5rem;
}

.card .card-header h5 {
    color: var(--admin-primary, #ff6900);
    font-weight: 600;
}
</style>

<div class="admin-wrapper">
    <!-- Sidebar -->
    <?php include '../includes/admin_sidebar.php'; ?>
    
    <!-- Main Content -->
    <div class="admin-main">
        <!-- Top Header -->
        <?php include '../includes/admin_topbar.php'; ?>
        
        <!-- Content Area -->
        <div class="admin-content">
            <div class="container-fluid">
                
                <!-- Page Header -->
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <div>
                        <h1 class="h3 mb-0">Dashboard</h1>
                        <p class="text-muted">Welcome back, <?php echo htmlspecialchars($_SESSION['admin_username'] ?? 'Admin'); ?>!</p>
                    </div>
                    <div>
                        <span class="badge bg-success">System Online</span>
                    </div>
                </div>
                
                <!-- Redesigned Dashboard Overview -->
                <div class="row mb-4 dashboard-grid">
                    <div class="col">
                        <div class="card stat-card stat-card-primary">
                            <div class="card-body">
                                <div class="d-flex align-items-center">
                                    <div class="stat-icon"><i class="bi bi-people-fill"></i></div>
                                    <div class="ms-3">
                                        <div class="stat-value"><?php echo number_format($stats['total_users'] ?? 0); ?></div>
                                        <div class="stat-label">Total Users</div>
                                        <small class="text-success"><i class="bi bi-check-circle"></i> <?php echo number_format($stats['active_users'] ?? 0); ?> active</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col">
                        <div class="card stat-card stat-card-success">
                            <div class="card-body">
                                <div class="d-flex align-items-center">
                                    <div class="stat-icon"><i class="bi bi-box-seam"></i></div>
                                    <div class="ms-3">
                                        <div class="stat-value"><?php echo number_format($stats['total_goods'] ?? 0); ?></div>
                                        <div class="stat-label">Total Goods (pcs)</div>
                                        <small class="text-success"><i class="bi bi-check-circle"></i> <?php echo number_format($stats['current_goods'] ?? 0); ?> current</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col">
                        <div class="card stat-card stat-card-info">
                            <div class="card-body">
                                <div class="d-flex align-items-center">
                                    <div class="stat-icon"><i class="bi bi-list-task"></i></div>
                                    <div class="ms-3">
                                        <div class="stat-value"><?php echo number_format($stats['completed_orders'] ?? 0); ?></div>
                                        <div class="stat-label">Completed Orders</div>
                                        <small class="text-info"><i class="bi bi-graph-up"></i> <?php echo number_format($stats['total_orders'] ?? 0); ?> submitted</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col">
                        <div class="card stat-card stat-card-warning">
                            <div class="card-body">
                                <div class="d-flex align-items-center">
                                    <div class="stat-icon"><i class="bi bi-cash-coin"></i></div>
                                    <div class="ms-3">
                                        <div class="stat-value"><?php echo formatCurrency($stats['completed_order_amount'] ?? 0); ?></div>
                                        <div class="stat-label">Completed Order Amount</div>
                                        <small class="text-warning"><i class="bi bi-currency-dollar"></i> <?php echo formatCurrency($stats['total_order_amount'] ?? 0); ?> submitted</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col">
                        <div class="card stat-card stat-card-success">
                            <div class="card-body">
                                <div class="d-flex align-items-center">
                                    <div class="stat-icon"><i class="bi bi-wallet2"></i></div>
                                    <div class="ms-3">
                                        <div class="stat-value"><?php echo formatCurrency($stats['recharge_order_amount'] ?? 0); ?></div>
                                        <div class="stat-label">Recharge Order Amount</div>
                                        <small class="text-success"><i class="bi bi-check-circle"></i> Approved Top-ups</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col">
                        <div class="card stat-card stat-card-danger">
                            <div class="card-body">
                                <div class="d-flex align-items-center">
                                    <div class="stat-icon"><i class="bi bi-bank"></i></div>
                                    <div class="ms-3">
                                        <div class="stat-value"><?php echo formatCurrency($stats['withdrawal_order_amount'] ?? 0); ?></div>
                                        <div class="stat-label">Withdrawal Order Amount</div>
                                        <small class="text-danger"><i class="bi bi-cash"></i> Approved Withdrawals</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col">
                        <div class="card stat-card stat-card-info">
                            <div class="card-body">
                                <div class="d-flex align-items-center">
                                    <div class="stat-icon"><i class="bi bi-gift"></i></div>
                                    <div class="ms-3">
                                        <div class="stat-value"><?php echo formatCurrency($stats['total_issued'] ?? 0); ?></div>
                                        <div class="stat-label">Total Amount Issued</div>
                                        <small class="text-info"><i class="bi bi-award"></i> All order revenue + rewards</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- End Redesigned Dashboard Overview -->
                
                <!-- Recent Activity -->
                <div class="row">
                    <div class="col-lg-6 mb-4">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="card-title mb-0">
                                    <i class="bi bi-person-plus me-2"></i>Recent Users
                                </h5>
                            </div>
                            <div class="card-body">
                                <?php if (!empty($recent_users)): ?>
                                    <div class="list-group list-group-flush recent-items-list">
                                        <?php foreach ($recent_users as $user): ?>
                                            <div class="list-group-item recent-item d-flex justify-content-between align-items-center">
                                                <div class="d-flex align-items-center">
                                                    <div class="user-avatar me-3">
                                                        <?php if (!empty($user['avatar'])): ?>
                                                            <img src="<?php echo BASE_URL . 'uploads/avatars/' . $user['avatar']; ?>" alt="Avatar" class="dashboard-avatar">
                                                        <?php else: ?>
                                                            <div class="dashboard-avatar-initials">
                                                                <?php echo strtoupper(substr($user['username'], 0, 2)); ?>
                                                            </div>
                                                        <?php endif; ?>
                                                    </div>
                                                    <div>
                                                        <strong><?php echo htmlspecialchars(ucfirst($user['username'])); ?></strong>
                                                        <br>
                                                        <small class="text-muted">
                                                            Joined <?php echo formatDate($user['created_at'], 'M j, Y'); ?>
                                                        </small>
                                                    </div>
                                                </div>
                                                <span class="badge bg-primary rounded-pill">New</span>
                                            </div>
                                        <?php endforeach; ?>
                                    </div>
                                    <div class="text-center mt-3">
                                        <a href="<?php echo BASE_URL; ?>admin/member_management/" class="btn btn-outline-primary btn-sm">
                                            View All Users
                                        </a>
                                    </div>
                                <?php else: ?>
                                    <div class="text-center text-muted py-4">
                                        <i class="bi bi-people" style="font-size: 2rem;"></i>
                                        <p class="mt-2">No users yet</p>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-lg-6 mb-4">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="card-title mb-0">
                                    <i class="bi bi-arrow-left-right me-2"></i>Recent Transactions
                                </h5>
                            </div>
                            <div class="card-body">
                                <?php if (!empty($recent_transactions)): ?>
                                    <div class="list-group list-group-flush recent-items-list">
                                        <?php foreach ($recent_transactions as $transaction): ?>
                                            <div class="list-group-item recent-item d-flex justify-content-between align-items-center">
                                                <div class="d-flex align-items-center">
                                                    <div class="user-avatar me-3">
                                                        <?php if (!empty($transaction['avatar'])): ?>
                                                            <img src="<?php echo BASE_URL . 'uploads/avatars/' . $transaction['avatar']; ?>" alt="Avatar" class="dashboard-avatar">
                                                        <?php else: ?>
                                                            <div class="dashboard-avatar-initials">
                                                                <?php echo strtoupper(substr($transaction['username'] ?? 'UN', 0, 2)); ?>
                                                            </div>
                                                        <?php endif; ?>
                                                    </div>
                                                    <div>
                                                        <strong><?php echo htmlspecialchars(ucfirst($transaction['username'] ?? 'Unknown')); ?></strong>
                                                        <br>
                                                        <small class="text-muted">
                                                            <?php echo ucfirst($transaction['type']); ?> -
                                                            <?php echo formatDate($transaction['created_at'], 'M j, Y'); ?>
                                                        </small>
                                                    </div>
                                                </div>
                                                <div class="text-end">
                                                    <div class="fw-bold">
                                                        <?php echo formatCurrency($transaction['amount']); ?>
                                                    </div>
                                                    <span class="badge bg-<?php echo $transaction['status'] === 'completed' ? 'success' : ($transaction['status'] === 'pending' ? 'warning' : 'danger'); ?>">
                                                        <?php echo ucfirst($transaction['status']); ?>
                                                    </span>
                                                </div>
                                            </div>
                                        <?php endforeach; ?>
                                    </div>
                                    <div class="text-center mt-3">
                                        <a href="<?php echo BASE_URL; ?>admin/transactions/" class="btn btn-outline-primary btn-sm">
                                            View All Transactions
                                        </a>
                                    </div>
                                <?php else: ?>
                                    <div class="text-center text-muted py-4">
                                        <i class="bi bi-arrow-left-right" style="font-size: 2rem;"></i>
                                        <p class="mt-2">No transactions yet</p>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Quick Actions -->
                <div class="row">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="card-title mb-0">
                                    <i class="bi bi-lightning me-2"></i>Quick Actions
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-3 mb-3">
                                        <a href="<?php echo BASE_URL; ?>admin/member_management/" class="btn btn-outline-primary w-100">
                                            <i class="bi bi-people me-2"></i>Manage Users
                                        </a>
                                    </div>
                                    <div class="col-md-3 mb-3">
                                        <a href="<?php echo BASE_URL; ?>admin/products/" class="btn btn-outline-success w-100">
                                            <i class="bi bi-box-seam me-2"></i>Manage Products
                                        </a>
                                    </div>
                                    <div class="col-md-3 mb-3">
                                        <a href="<?php echo BASE_URL; ?>admin/settings/" class="btn btn-outline-info w-100">
                                            <i class="bi bi-gear me-2"></i>System Settings
                                        </a>
                                    </div>
                                    <div class="col-md-3 mb-3">
                                        <a href="<?php echo BASE_URL; ?>admin/appearance/" class="btn btn-outline-warning w-100">
                                            <i class="bi bi-palette me-2"></i>Appearance
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                

                <!-- End Dashboard Charts Row -->
                
            </div>
        </div>
        
        <!-- Footer -->
        <?php include '../includes/admin_footer.php'; ?>
    </div>
</div>

<?php 
$additional_js = [
    BASE_URL . 'admin/assets/js/admin.js',
    BASE_URL . 'admin/dashboard/dashboard.js'
];
include '../includes/admin_footer_scripts.php';
?>
