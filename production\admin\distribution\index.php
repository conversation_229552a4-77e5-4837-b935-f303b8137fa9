<?php
/**
 * Bamboo Web Application - Distribution Settings
 * Company: Notepadsly
 * Version: 1.0
 */

define('BAMBOO_APP', true);
require_once '../../includes/config.php';
require_once '../../includes/functions.php';

if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

if (!isAdminLoggedIn()) {
    redirect('admin/login/');
}

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['csrf_token'])) {
    if (!verifyCSRFToken($_POST['csrf_token'])) {
        showError('Invalid security token. Please try again.');
    } else {
        $settings_to_update = [
            'referral_commission_rate' => (float)$_POST['referral_commission_rate'],
            // Add other distribution settings here
        ];

        foreach ($settings_to_update as $key => $value) {
            updateSetting($key, $value);
        }

        showSuccess('Distribution settings updated successfully!');
        redirect('admin/distribution/');
        exit();
    }
}

$settings = getSettings('referral');
$page_title = 'Distribution Settings';
include '../includes/admin_header.php';
?>

<div class="admin-wrapper">
    <?php include '../includes/admin_sidebar.php'; ?>
    <div class="admin-main">
        <?php include '../includes/admin_topbar.php'; ?>
        <div class="admin-content">
            <div class="container-fluid">
                <h1 class="h3 mb-4">Distribution Settings</h1>

                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">Referral Commission</h5>
                    </div>
                    <div class="card-body">
                        <form action="" method="POST">
                            <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                            <div class="mb-3">
                                <label for="referral_commission_rate" class="form-label">Referral Commission Rate</label>
                                <div class="input-group">
                                    <input type="number" class="form-control" id="referral_commission_rate" name="referral_commission_rate" step="0.01" min="0" value="<?php echo htmlspecialchars($settings['referral_commission_rate'] ?? 0); ?>">
                                    <span class="input-group-text">%</span>
                                </div>
                                <div class="form-text">The percentage of a referred user's earnings that the referrer receives.</div>
                            </div>
                            <button type="submit" class="btn btn-primary">Save Settings</button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
        <?php include '../includes/admin_footer.php'; ?>
    </div>
</div>

<?php include '../includes/admin_footer_scripts.php'; ?>
