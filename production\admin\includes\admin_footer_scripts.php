<?php
/**
 * Bamboo Web Application - Admin Footer Scripts
 * Company: Notepadsly
 * Version: 1.0
 */

// Prevent direct access
if (!defined('BAMBOO_APP')) {
    die('Direct access not permitted');
}
?>

    <!-- Bootstrap 5 JS Bundle -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.7.0.min.js"></script>
    
    <!-- Chart.js for dashboard charts -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <!-- Admin JavaScript -->
    <script src="<?php echo BASE_URL; ?>admin/assets/js/admin.js"></script>

    <!-- Dynamic Theme System -->
    <script src="<?php echo BASE_URL; ?>admin/assets/js/dynamic-theme.js"></script>
    
    <!-- Additional JavaScript files -->
    <?php if (isset($additional_js) && is_array($additional_js)): ?>
        <?php foreach ($additional_js as $js_file): ?>
            <script src="<?php echo $js_file; ?>"></script>
        <?php endforeach; ?>
    <?php endif; ?>
    
    <!-- Global Admin JavaScript Variables -->
    <script>
        // Global configuration for Admin JavaScript
        window.AdminApp = window.AdminApp || {};
        
        // Extend AdminApp with configuration
        Object.assign(window.AdminApp, {
            baseUrl: '<?php echo BASE_URL; ?>',
            adminUrl: '<?php echo BASE_URL; ?>admin/',
            assetsUrl: '<?php echo BASE_URL; ?>admin/assets/',
            csrfToken: '<?php echo generateCSRFToken(); ?>',
            userId: <?php echo getCurrentAdminId() ?? 'null'; ?>,
            username: '<?php echo htmlspecialchars($_SESSION['admin_username'] ?? ''); ?>',
            role: '<?php echo htmlspecialchars($_SESSION['admin_role'] ?? ''); ?>',
            appName: '<?php echo htmlspecialchars(getAppSetting('app_name', APP_NAME)); ?>',
            primaryColor: '<?php echo getAppSetting('appearance_primary_color', '#ff6900'); ?>',
            secondaryColor: '<?php echo getAppSetting('appearance_secondary_color', '#ffffff'); ?>',
            defaultCurrency: '<?php echo getAppSetting('default_currency', DEFAULT_CURRENCY); ?>',
            decimalPlaces: <?php echo getAppSetting('decimal_places', DECIMAL_PLACES); ?>,
            // Appearance settings for dynamic theme
            appearanceSettings: {
                primaryColor: '<?php echo getAppSetting('appearance_primary_color', '#ff6900'); ?>',
                secondaryColor: '<?php echo getAppSetting('appearance_secondary_color', '#ffffff'); ?>',
                accentColor: '<?php echo getAppSetting('appearance_accent_color', '#007bff'); ?>',
                gradientStart: '<?php echo getAppSetting('appearance_gradient_start', '#ff6900'); ?>',
                gradientEnd: '<?php echo getAppSetting('appearance_gradient_end', '#ff8533'); ?>',
                cardBackground: '<?php echo getAppSetting('appearance_card_background', '#ffffff'); ?>',
                sidebarStyle: '<?php echo getAppSetting('appearance_sidebar_style', 'gradient'); ?>',
                sidebarTextColor: '<?php echo getAppSetting('appearance_sidebar_text_color', '#ffffff'); ?>',
                cardShadow: '<?php echo getAppSetting('appearance_card_shadow', 'subtle'); ?>',
                borderRadius: '<?php echo getAppSetting('appearance_border_radius', '0.5rem'); ?>',
                buttonStyle: '<?php echo getAppSetting('appearance_button_style', 'gradient'); ?>',
                themeMode: '<?php echo getAppSetting('appearance_theme_mode', 'light'); ?>',
                logoSize: '<?php echo getAppSetting('appearance_logo_size', 'medium'); ?>',
                tableHeaderColor: '<?php echo getAppSetting('appearance_table_header_color', 'primary'); ?>',
                tableHeaderTextColor: '<?php echo getAppSetting('appearance_table_header_text_color', '#ffffff'); ?>',
                levelBadgeColor: '<?php echo getAppSetting('appearance_level_badge_color', 'primary'); ?>',
                footerBgColor: '<?php echo getAppSetting('appearance_footer_bg_color', '#f8f9fa'); ?>',
                footerTextColor: '<?php echo getAppSetting('appearance_footer_text_color', '#6c757d'); ?>'
            }
        });
        
        // Initialize AdminApp when document is ready
        $(document).ready(function() {
            // Initialize the admin application if init function exists
            if (typeof AdminApp.init === 'function') {
                AdminApp.init();
            }

            // Initialize dynamic theme after AdminApp is ready
            if (window.BambooDynamicTheme) {
                window.BambooDynamicTheme.loadThemeSettings();
                window.BambooDynamicTheme.applyTheme();
            }

            // Auto-hide flash messages
            setTimeout(function() {
                $('.admin-flash-messages .alert').fadeOut();
            }, 5000);
        });
    </script>
    
</body>
</html>