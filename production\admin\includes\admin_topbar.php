<?php
/**
 * Bamboo Web Application - Admin Top Bar
 * Company: Notepadsly
 * Version: 1.0
 */

// Prevent direct access
if (!defined('BAMBOO_APP')) {
    die('Direct access not permitted');
}

// Get notifications for the bell icon
$notifications = [];
$notifications_count = 0;
if (tableExists('users')) {
    $pending_users = getRecordCount('users', 'status = ?', ['pending']);
    if ($pending_users > 0) {
        $notifications[] = [
            'title' => $pending_users . ' pending user(s) awaiting approval',
            'created_at' => date('Y-m-d H:i:s'),
        ];
        $notifications_count += 1;
    }
}
?>

<div class="admin-topbar">
    <div class="d-flex justify-content-between align-items-center">
        
        <!-- Left Side - Mobile Menu Toggle & Breadcrumb -->
        <div class="d-flex align-items-center">
            <!-- Mobile Menu Toggle -->
            <button class="btn btn-link d-lg-none me-3" id="sidebar-toggle">
                <i class="bi bi-list fs-4"></i>
            </button>
            
            <!-- Breadcrumb -->
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb mb-0">
                    <li class="breadcrumb-item">
                        <a href="<?php echo BASE_URL; ?>admin/dashboard/">
                            <i class="bi bi-house"></i> Admin
                        </a>
                    </li>
                    <?php
                    $path_parts = explode('/', trim($_SERVER['REQUEST_URI'], '/'));
                    if (count($path_parts) > 2) {
                        $section = $path_parts[1] ?? '';
                        $page = $path_parts[2] ?? '';
                        
                        if ($section && $section !== 'dashboard') {
                            echo '<li class="breadcrumb-item">';
                            echo '<a href="' . BASE_URL . 'admin/' . $section . '/">';
                            echo ucwords(str_replace('_', ' ', $section));
                            echo '</a>';
                            echo '</li>';
                        }
                        
                        if ($page && $page !== $section) {
                            echo '<li class="breadcrumb-item active" aria-current="page">';
                            echo ucwords(str_replace('_', ' ', $page));
                            echo '</li>';
                        }
                    } else {
                        echo '<li class="breadcrumb-item active" aria-current="page">Dashboard</li>';
                    }
                    ?>
                </ol>
            </nav>
        </div>
        
        <!-- Right Side - Search, Notifications, User Menu -->
        <div class="d-flex align-items-center">
            
            <!-- Search -->
            <div class="me-3 d-none d-md-block">
                <div class="input-group">
                    <input type="text" class="form-control form-control-sm" placeholder="Search..." id="admin-search">
                    <button class="btn btn-outline-secondary btn-sm" type="button">
                        <i class="bi bi-search"></i>
                    </button>
                </div>
            </div>
            
            <!-- Quick Actions -->
            <div class="dropdown me-3">
                <button class="btn btn-outline-primary btn-sm dropdown-toggle" type="button" data-bs-toggle="dropdown">
                    <i class="bi bi-plus-circle"></i>
                    <span class="d-none d-md-inline">Quick Add</span>
                </button>
                <ul class="dropdown-menu">
                    <li>
                        <a class="dropdown-item" href="<?php echo BASE_URL; ?>admin/member_management/add.php">
                            <i class="bi bi-person-plus me-2"></i>Add User
                        </a>
                    </li>
                    <li>
                        <a class="dropdown-item" href="<?php echo BASE_URL; ?>admin/products/add.php">
                            <i class="bi bi-box-seam me-2"></i>Add Product
                        </a>
                    </li>
                    <li>
                        <a class="dropdown-item" href="<?php echo BASE_URL; ?>admin/tasks/assign.php">
                            <i class="bi bi-list-task me-2"></i>Assign Task
                        </a>
                    </li>
                    <li><hr class="dropdown-divider"></li>
                    <li>
                        <a class="dropdown-item" href="<?php echo BASE_URL; ?>admin/notifications/send/">
                            <i class="bi bi-bell me-2"></i>Send Notification
                        </a>
                    </li>
                </ul>
            </div>
            
            <!-- Notifications -->
            <div class="dropdown me-3">
                <button class="btn btn-outline-secondary btn-sm position-relative" type="button" data-bs-toggle="dropdown">
                    <i class="bi bi-bell"></i>
                    <?php if ($notifications_count > 0): ?>
                        <span class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger">
                            <?php echo $notifications_count > 99 ? '99+' : $notifications_count; ?>
                        </span>
                    <?php endif; ?>
                </button>
                <ul class="dropdown-menu dropdown-menu-end notification-dropdown">
                    <li class="dropdown-header">
                        <strong>Notifications</strong>
                        <?php if ($notifications_count > 0): ?>
                            <span class="badge bg-primary rounded-pill float-end"><?php echo $notifications_count; ?></span>
                        <?php endif; ?>
                    </li>
                    <li><hr class="dropdown-divider"></li>
                    <?php if ($notifications_count > 0): ?>
                        <?php foreach ($notifications as $notif): ?>
                        <li>
                            <a class="dropdown-item" href="#">
                                <div class="d-flex">
                                    <div class="flex-shrink-0">
                                        <i class="bi bi-bell text-primary"></i>
                                    </div>
                                    <div class="flex-grow-1 ms-2">
                                        <div class="fw-bold"><?php echo htmlspecialchars($notif['title'] ?? 'Notification'); ?></div>
                                        <small class="text-muted"><?php echo date('Y-m-d H:i', strtotime($notif['created_at'])); ?></small>
                                    </div>
                                </div>
                            </a>
                        </li>
                        <?php endforeach; ?>
                        <li><hr class="dropdown-divider"></li>
                        <li>
                            <a class="dropdown-item text-center" href="<?php echo BASE_URL; ?>admin/notifications/">
                                View all notifications
                            </a>
                        </li>
                    <?php else: ?>
                        <li>
                            <div class="dropdown-item-text text-center text-muted py-3">
                                <i class="bi bi-bell-slash"></i><br>
                                No new notifications
                            </div>
                        </li>
                    <?php endif; ?>
                </ul>
            </div>
            
            <!-- User Menu -->
            <div class="dropdown">
                <button class="btn btn-outline-secondary btn-sm dropdown-toggle" type="button" data-bs-toggle="dropdown">
                    <i class="bi bi-person-circle"></i>
                    <span class="d-none d-md-inline ms-1"><?php echo htmlspecialchars($_SESSION['admin_username'] ?? 'Admin'); ?></span>
                </button>
                <ul class="dropdown-menu dropdown-menu-end">
                    <li class="dropdown-header">
                        <strong><?php echo htmlspecialchars($_SESSION['admin_full_name'] ?? $_SESSION['admin_username'] ?? 'Administrator'); ?></strong><br>
                        <small class="text-muted"><?php echo htmlspecialchars($_SESSION['admin_email'] ?? ''); ?></small>
                    </li>
                    <li><hr class="dropdown-divider"></li>
                    <li>
                        <a class="dropdown-item" href="<?php echo BASE_URL; ?>admin/profile/">
                            <i class="bi bi-person me-2"></i>My Profile
                        </a>
                    </li>
                    <li>
                        <a class="dropdown-item" href="<?php echo BASE_URL; ?>admin/settings/">
                            <i class="bi bi-gear me-2"></i>Settings
                        </a>
                    </li>
                    <li><hr class="dropdown-divider"></li>
                    <li>
                        <a class="dropdown-item" href="<?php echo BASE_URL; ?>" target="_blank">
                            <i class="bi bi-eye me-2"></i>View Site
                        </a>
                    </li>
                    <li>
                        <a class="dropdown-item text-danger" href="<?php echo BASE_URL; ?>admin/logout/logout.php">
                            <i class="bi bi-box-arrow-right me-2"></i>Logout
                        </a>
                    </li>
                </ul>
            </div>
            
        </div>
    </div>
</div>