<?php
/**
 * Bamboo Web Application - Add User
 * Company: Notepadsly
 * Version: 1.1
 */

// Define app constant
define('BAMBOO_APP', true);

// Include required files
require_once '../../includes/config.php';
require_once '../../includes/functions.php';

// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Check if admin is logged in
if (!isAdminLoggedIn()) {
    redirect('admin/login/');
}

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (!verifyCSRFToken($_POST['csrf_token'] ?? '')) {
        showError('Invalid security token. Please try again.');
    } else {
        $username = sanitizeInput($_POST['username'] ?? '');
        $phone = sanitizeInput($_POST['phone'] ?? '');
        $email = sanitizeInput($_POST['email'] ?? '');
        $password = $_POST['password'] ?? '';
        $confirm_password = $_POST['confirm_password'] ?? '';
        $gender = sanitizeInput($_POST['gender'] ?? '');
        $status = sanitizeInput($_POST['status'] ?? 'active');
        $vip_level = (int)($_POST['vip_level'] ?? 1);
        $balance = (float)($_POST['balance'] ?? 0);
        $agent_code = sanitizeInput($_POST['agent_code'] ?? '');
        
        $errors = [];
        if (empty($username)) $errors[] = 'Username is required';
        if (empty($phone)) $errors[] = 'Phone number is required';
        if (empty($email) || !filter_var($email, FILTER_VALIDATE_EMAIL)) $errors[] = 'A valid email is required';
        if (strlen($password) < 6) $errors[] = 'Password must be at least 6 characters';
        if ($password !== $confirm_password) $errors[] = 'Passwords do not match';
        if (empty($gender)) $errors[] = 'Gender is required';

        if (empty($errors)) {
            if (recordExists('users', 'username = ?', [$username])) $errors[] = 'Username already exists';
            if (recordExists('users', 'email = ?', [$email])) $errors[] = 'Email already exists';
        }
        
        if (empty($errors)) {
            try {
                $password_hash = password_hash($password, PASSWORD_DEFAULT);
                $withdrawal_pin = str_pad(rand(0, 999999), 6, '0', STR_PAD_LEFT);
                $withdrawal_pin_hash = password_hash($withdrawal_pin, PASSWORD_DEFAULT);
                $invitation_code = strtoupper(bin2hex(random_bytes(4)));

                $user_data = [
                    'username' => $username,
                    'phone' => $phone,
                    'email' => $email,
                    'password_hash' => $password_hash,
                    'withdrawal_pin_hash' => $withdrawal_pin_hash,
                    'gender' => $gender,
                    'invitation_code' => $invitation_code,
                    'balance' => $balance,
                    'vip_level' => $vip_level,
                    'status' => $status,
                    'last_task_date' => date('Y-m-d'),
                    'created_at' => date('Y-m-d H:i:s'),
                    'updated_at' => date('Y-m-d H:i:s'),
                    'invited_by' => $agent_code
                ];
                
                $user_id = insertRecord('users', $user_data);
                
                if ($user_id) {
                    showSuccess("User created successfully! Withdrawal PIN: $withdrawal_pin, Invite Code: $invitation_code");
                    redirect('admin/member_management/');
                } else {
                    showError('Failed to create user. Please try again.');
                }
            } catch (Exception $e) {
                showError('Error creating user: ' . $e->getMessage());
            }
        } else {
            foreach ($errors as $error) showError($error);
        }
    }
}

$vip_levels = fetchAll("SELECT level, name, commission_multiplier FROM vip_levels ORDER BY level ASC");
// Fetch all agents (superior) for dropdown
$all_agents = fetchAll("SELECT * FROM superiors ORDER BY name ASC");

$page_title = 'Add New User';
$body_class = 'admin-page';
$additional_css = [BASE_URL . 'admin/assets/css/admin.css'];

include '../includes/admin_header.php';
?>

<div class="admin-wrapper">
    <?php include '../includes/admin_sidebar.php'; ?>
    <div class="admin-main">
        <?php include '../includes/admin_topbar.php'; ?>
        <div class="admin-content">
            <div class="container-fluid">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h1 class="h3 mb-0">Add New User</h1>
                    <a href="../member_management/" class="btn btn-secondary"><i class="bi bi-arrow-left me-2"></i>Back to Users</a>
                </div>
                <div class="row">
                    <div class="col-lg-8">
                        <div class="card">
                            <div class="card-header"><h5 class="card-title mb-0"><i class="bi bi-person-plus me-2"></i>User Information</h5></div>
                            <div class="card-body">
                                <form method="POST" action="">
                                    <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                                    <div class="row">
                                        <div class="col-md-6 mb-3"><label for="username" class="form-label">Username <span class="text-danger">*</span></label><input type="text" class="form-control" id="username" name="username" required></div>
                                        <div class="col-md-6 mb-3"><label for="email" class="form-label">Email <span class="text-danger">*</span></label><input type="email" class="form-control" id="email" name="email" required></div>
                                    </div>
                                    <div class="row">
                                        <div class="col-md-6 mb-3"><label for="phone" class="form-label">Phone Number <span class="text-danger">*</span></label><input type="tel" class="form-control" id="phone" name="phone" required></div>
                                        <div class="col-md-6 mb-3"><label for="gender" class="form-label">Gender <span class="text-danger">*</span></label><select class="form-select" id="gender" name="gender" required><option value="">Select</option><option value="male">Male</option><option value="female">Female</option></select></div>
                                    </div>
                                    <div class="row">
                                        <div class="col-md-6 mb-3"><label for="password" class="form-label">Password <span class="text-danger">*</span></label><input type="password" class="form-control" id="password" name="password" required><small class="form-text text-muted">Min. 6 characters</small></div>
                                        <div class="col-md-6 mb-3"><label for="confirm_password" class="form-label">Confirm Password <span class="text-danger">*</span></label><input type="password" class="form-control" id="confirm_password" name="confirm_password" required></div>
                                    </div>
                                    <div class="row">
                                        <div class="col-md-6 mb-3"><label for="status" class="form-label">Status</label><select class="form-select" id="status" name="status"><option value="active" selected>Active</option><option value="pending">Pending</option><option value="suspended">Suspended</option></select></div>
                                        <div class="col-md-6 mb-3"><label for="vip_level" class="form-label">VIP Level</label><select class="form-select" id="vip_level" name="vip_level">
                                            <?php foreach ($vip_levels as $level): ?>
                                                <option value="<?php echo $level['level']; ?>"><?php echo htmlspecialchars($level['name']); ?></option>
                                            <?php endforeach; ?>
                                        </select></div>
                                    </div>
                                    <div class="row">
                                        <div class="col-md-6 mb-3"><label for="balance" class="form-label">Initial Balance (USDT)</label><input type="number" class="form-control" id="balance" name="balance" value="0" step="0.01" min="0"></div>
                                    </div>
                                    <!-- Agent/Superior Dropdown and Auto-fill Fields -->
                                    <div class="row">
                                        <div class="col-md-6 mb-3">
                                            <label for="agent_code" class="form-label">Invited By (Agent/Superior)</label>
                                            <select class="form-select" id="agent_code" name="agent_code">
                                                <option value="">-- Select Agent --</option>
                                                <?php foreach ($all_agents as $agent): ?>
                                                    <option value="<?php echo htmlspecialchars($agent['invitation_code']); ?>" data-name="<?php echo htmlspecialchars($agent['name']); ?>" data-phone="<?php echo htmlspecialchars($agent['phone']); ?>">
                                                        <?php echo htmlspecialchars($agent['name']) . ' (' . $agent['invitation_code'] . ')'; ?>
                                                    </option>
                                                <?php endforeach; ?>
                                            </select>
                                        </div>
                                        <div class="col-md-6 mb-3">
                                            <label class="form-label">Invited By (Agent Name)</label>
                                            <input type="text" class="form-control" id="invited_by_name" value="N/A" readonly>
                                        </div>
                                        <div class="col-md-6 mb-3">
                                            <label class="form-label">Invitation Code (ID)</label>
                                            <input type="text" class="form-control" id="invited_by_code" value="N/A" readonly>
                                        </div>
                                        <div class="col-md-6 mb-3">
                                            <label class="form-label">Invited By (Phone)</label>
                                            <input type="text" class="form-control" id="invited_by_phone" value="N/A" readonly>
                                        </div>
                                    </div>
                                    <div class="d-flex justify-content-end"><button type="button" class="btn btn-secondary me-2" onclick="window.history.back()">Cancel</button><button type="submit" class="btn btn-primary"><i class="bi bi-check-circle me-2"></i>Create User</button></div>
                                </form>
                                <script>
document.addEventListener('DOMContentLoaded', function() {
    const agentSelect = document.getElementById('agent_code');
    if (agentSelect) {
        agentSelect.addEventListener('change', function() {
            const selected = agentSelect.options[agentSelect.selectedIndex];
            document.getElementById('invited_by_name').value = selected.getAttribute('data-name') || 'N/A';
            document.getElementById('invited_by_code').value = selected.value || 'N/A';
            document.getElementById('invited_by_phone').value = selected.getAttribute('data-phone') || 'N/A';
        });
        // Trigger change event on page load if agent is already selected
        if (agentSelect.value) {
            const selected = agentSelect.options[agentSelect.selectedIndex];
            document.getElementById('invited_by_name').value = selected.getAttribute('data-name') || 'N/A';
            document.getElementById('invited_by_code').value = selected.value || 'N/A';
            document.getElementById('invited_by_phone').value = selected.getAttribute('data-phone') || 'N/A';
        }
    }
});
</script>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-4">
                        <div class="card">
                            <div class="card-header"><h5 class="card-title mb-0"><i class="bi bi-info-circle me-2"></i>Information</h5></div>
                            <div class="card-body">
                                <div class="alert alert-info"><h6>Important Notes:</h6><ul class="mb-0"><li>A random Withdrawal PIN and Invitation Code will be generated and displayed upon successful creation.</li><li>Username and email must be unique.</li></ul></div>
                                <div class="alert alert-secondary"><h6>VIP Levels:</h6><ul class="mb-0 list-unstyled">
                                    <?php foreach ($vip_levels as $level): ?>
                                        <li><strong><?php echo htmlspecialchars($level['name']); ?>:</strong> <?php echo htmlspecialchars($level['commission_multiplier']); ?>x commission</li>
                                    <?php endforeach; ?>
                                </ul></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <?php include '../includes/admin_footer.php'; ?>
    </div>
</div>

<?php 
$additional_js = [BASE_URL . 'admin/assets/js/admin.js'];
include '../includes/admin_footer_scripts.php';
?>