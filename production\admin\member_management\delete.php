<?php
/**
 * Bamboo Web Application - Delete Member (Clean Version)
 * Company: Notepadsly
 * Version: 1.0
 */

define('BAMBOO_APP', true);
require_once '../../includes/config.php';
require_once '../../includes/functions.php';
require_once '../../includes/database.php';

// Fallback for error functions
if (!function_exists('showError')) {
    function showError($message) {
        $_SESSION['error_message'] = $message;
    }
}
if (!function_exists('showSuccess')) {
    function showSuccess($message) {
        $_SESSION['success_message'] = $message;
    }
}

// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Ensure admin is logged in
if (!isAdminLoggedIn()) {
    redirect('admin/login/');
}

error_log('Delete script accessed: ' . date('Y-m-d H:i:s'));

// Get user ID from GET parameter
$user_id = (int)($_GET['id'] ?? 0);
if ($user_id === 0) {
    showError('Invalid user ID.');
    redirect('admin/member_management/');
}

// Fetch user info
$user = fetchRow("SELECT * FROM users WHERE id = ?", [$user_id]);
if (!$user) {
    showError('User not found.');
    redirect('admin/member_management/');
}

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $csrf_token = $_POST['csrf_token'] ?? '';
    if (!verifyCSRFToken($csrf_token)) {
        showError('Invalid CSRF token. Please try again.');
        redirect('admin/member_management/');
    }

    try {
        $db = getDB();
        error_log('Starting user deletion for ID: ' . $user_id);
        
        $db->beginTransaction();

        // Simple delete operation
        $stmt = $db->prepare("DELETE FROM users WHERE id = ?");
        $stmt->execute([$user_id]);

        if ($stmt->rowCount() === 0) {
            throw new Exception("No rows affected - user may not exist");
        }

        $db->commit();
        error_log('Successfully deleted user ID: ' . $user_id);
        showSuccess('User deleted successfully.');
        redirect('admin/member_management/');

    } catch (Exception $e) {
        if (isset($db) && $db->inTransaction()) {
            $db->rollBack();
        }
        error_log('Deletion error: ' . $e->getMessage());
        showError('Failed to delete user: ' . $e->getMessage());
        redirect('admin/member_management/');
    }
}

$page_title = 'Delete Member';
include '../includes/admin_header.php';
?>
<div class="admin-wrapper">
    <?php include '../includes/admin_sidebar.php'; ?>
    <div class="admin-main">
        <?php include '../includes/admin_topbar.php'; ?>
        <div class="admin-content">
            <div class="container py-5">
                <div class="card mx-auto" style="max-width: 500px;">
                    <div class="card-header bg-danger text-white">
                        <h4 class="mb-0">Delete Member</h4>
                    </div>
                    <div class="card-body">
                        <p>Are you sure you want to delete the user <strong><?php echo htmlspecialchars($user['username']); ?></strong>? This action cannot be undone.</p>
                        <form method="post">
                            <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                            <div class="d-flex justify-content-between">
                                <a href="index.php" class="btn btn-secondary">Cancel</a>
                                <button type="submit" class="btn btn-danger">Delete User</button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
        <?php include '../includes/admin_footer.php'; ?>
    </div>
</div>
<?php include '../includes/admin_footer_scripts.php'; ?>
