<?php
/**
 * Bamboo Web Application - Edit Member (Complete)
 * Company: Notepadsly
 * Version: 2.0
 */

define('BAMBOO_APP', true);
require_once '../../includes/config.php';
require_once '../../includes/functions.php';

if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

if (!isAdminLoggedIn()) {
    redirect('admin/login/');
}

$user_id = (int)($_GET['id'] ?? 0);
if ($user_id === 0) {
    redirect('admin/member_management/');
}

$user = fetchRow('SELECT u.*, i.username as inviter_username, i.phone as inviter_phone, i.id as inviter_id FROM users u LEFT JOIN users i ON u.invited_by = i.id WHERE u.id = ?', [$user_id]);
if (!$user) {
    redirect('admin/member_management/');
}

// Get user's VIP level information
$vip_level = fetchRow('SELECT * FROM vip_levels WHERE level = ?', [$user['vip_level']]);

// Fetch all agents (superior) for dropdown
$all_agents = fetchAll("SELECT * FROM superiors ORDER BY name ASC");

// Handle POST requests
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['csrf_token'])) {
    if (!verifyCSRFToken($_POST['csrf_token'])) {
        showError('Invalid security token. Please try again.');
    } else {
        $action = $_POST['action'] ?? $_GET['action'] ?? 'edit';
        
        switch ($action) {
            case 'edit': // Main edit action
                $username = sanitizeInput($_POST['username'] ?? '');
                $email = sanitizeInput($_POST['email'] ?? '');
                $phone = sanitizeInput($_POST['phone'] ?? '');
                $gender = sanitizeInput($_POST['gender'] ?? '');
                $vip_level = (int)($_POST['vip_level'] ?? 0);
                $status = sanitizeInput($_POST['status'] ?? '');
                $balance = (float)($_POST['balance'] ?? 0.0);
                $frozen_balance = (float)($_POST['frozen_balance'] ?? 0.0);
                $commission_balance = (float)($_POST['commission_balance'] ?? 0.0);
                $agent_code = sanitizeInput($_POST['agent_code'] ?? '');

                // Validate required fields
                if (empty($username) || empty($phone) || empty($gender)) {
                    showError('Username, phone, and gender are required.');
                    break;
                }

                // Check for duplicates
                $existing_user = fetchRow("SELECT id FROM users WHERE (username = ? OR phone = ? OR (email = ? AND email IS NOT NULL)) AND id != ?", [$username, $phone, $email, $user_id]);
                if ($existing_user) {
                    showError('Username, phone, or email already exists for another user.');
                    break;
                }

                $update_data = [
                    'username' => $username,
                    'email' => $email,
                    'phone' => $phone,
                    'gender' => $gender,
                    'vip_level' => $vip_level,
                    'status' => $status,
                    'balance' => $balance,
                    'frozen_balance' => $frozen_balance,
                    'commission_balance' => $commission_balance,
                    'invited_by' => $agent_code
                ];

                if (updateRecord('users', $update_data, 'id = ?', [$user_id])) {
                    showSuccess('User updated successfully!');
                    // Refresh user data
                    $user = fetchRow('SELECT u.*, i.username as inviter_username, i.phone as inviter_phone, i.id as inviter_id FROM users u LEFT JOIN users i ON u.invited_by = i.id WHERE u.id = ?', [$user_id]);
                } else {
                    showError('Failed to update user.');
                }
                break;
                
            case 'upload_avatar':
                if (isset($_FILES['avatar']) && $_FILES['avatar']['error'] === UPLOAD_ERR_OK) {
                    $upload_result = handleFileUpload($_FILES['avatar'], 'avatars/', ['jpg', 'jpeg', 'png']);
                    if ($upload_result['success']) {
                        // Delete old avatar if exists
                        if (!empty($user['avatar_url'])) {
                            $old_avatar_path = '../../uploads/avatars/' . basename($user['avatar_url']);
                            if (file_exists($old_avatar_path)) {
                                unlink($old_avatar_path);
                            }
                        }

                        if (updateRecord('users', ['avatar_url' => $upload_result['file_url']], 'id = ?', [$user_id])) {
                            showSuccess('Avatar uploaded successfully!');
                            // Refresh user data
                            $user = fetchRow('SELECT u.*, i.username as inviter_username, i.phone as inviter_phone, i.id as inviter_id FROM users u LEFT JOIN users i ON u.invited_by = i.id WHERE u.id = ?', [$user_id]);
                        } else {
                            showError('Failed to update avatar in database.');
                        }
                    } else {
                        showError('Avatar upload failed: ' . $upload_result['message']);
                    }
                } else {
                    showError('No avatar file uploaded or upload error.');
                }
                break;
                
            case 'adjust_balance':
                $amount = (float)($_POST['amount'] ?? 0);
                $type = $_POST['type'] ?? '';
                if ($amount > 0 && in_array($type, ['addition', 'deduction'])) {
                    $admin_id = $_SESSION['admin_id'] ?? null;
                    $admin_username = $_SESSION['admin_username'] ?? 'Unknown';
                    $order_no = generateOrderNo();
                    $payment_channel = 'Admin Panel';
                    $state = 'completed';

                    $success = adjustUserBalance($user_id, $amount, $type, $admin_id, $order_no, $payment_channel, $admin_username, $state);

                    if ($success) {
                        showSuccess('Balance adjusted successfully!');
                        // Refresh user data
                        $user = fetchRow('SELECT u.*, i.username as inviter_username, i.phone as inviter_phone, i.id as inviter_id FROM users u LEFT JOIN users i ON u.invited_by = i.id WHERE u.id = ?', [$user_id]);
                    } else {
                        showError('Failed to adjust balance.');
                    }
                } else {
                    showError('Invalid amount or type.');
                }
                break;
                
            case 'credit_score':
                $score = (int)($_POST['score'] ?? 0);
                if ($score >= 0 && $score <= 100) {
                    if (updateRecord('users', ['credit_score' => $score], 'id = ?', [$user_id])) {
                        showSuccess('Credit score updated successfully!');
                        // Refresh user data
                        $user = fetchRow('SELECT u.*, i.username as inviter_username, i.phone as inviter_phone, i.id as inviter_id FROM users u LEFT JOIN users i ON u.invited_by = i.id WHERE u.id = ?', [$user_id]);
                    } else {
                        showError('Failed to update credit score.');
                    }
                } else {
                    showError('Credit score must be between 0 and 100.');
                }
                break;
                
            case 'change_password':
                $new_password = $_POST['new_password'] ?? '';
                if (!empty($new_password) && strlen($new_password) >= 6) {
                    $hashed_password = password_hash($new_password, PASSWORD_DEFAULT);
                    if (updateRecord('users', ['password_hash' => $hashed_password], 'id = ?', [$user_id])) {
                        showSuccess('Login password updated successfully!');
                    } else {
                        showError('Failed to update login password.');
                    }
                } else {
                    showError('Password must be at least 6 characters long.');
                }
                break;
                
            case 'change_payment_password':
                $new_payment_password = $_POST['new_payment_password'] ?? '';
                if (!empty($new_payment_password) && strlen($new_payment_password) >= 4) {
                    $hashed_payment_password = password_hash($new_payment_password, PASSWORD_DEFAULT);
                    if (updateRecord('users', ['withdrawal_pin_hash' => $hashed_payment_password], 'id = ?', [$user_id])) {
                        showSuccess('Payment password (withdrawal PIN) updated successfully!');
                    } else {
                        showError('Failed to update payment password.');
                    }
                } else {
                    showError('Payment password must be at least 4 characters long.');
                }
                break;
        }
    }
}

$vip_levels = fetchAll('SELECT level, name FROM vip_levels ORDER BY level ASC');
$user_statuses = ['pending', 'active', 'suspended', 'banned'];

$page_title = 'Edit Member';
$additional_css = [BASE_URL . 'admin/assets/css/member-details.css'];
include '../includes/admin_header.php';
?>

<div class="admin-wrapper">
    <?php include '../includes/admin_sidebar.php'; ?>
    <div class="admin-main">
        <?php include '../includes/admin_topbar.php'; ?>
        <div class="admin-content">
            <div class="container-fluid">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <div>
                        <h1 class="h3 mb-0">
                            <i class="bi bi-person-gear text-primary me-2"></i>
                            Edit Member: <?php echo htmlspecialchars($user['username']); ?>
                        </h1>
                        <small class="text-muted">Manage user information and settings</small>
                    </div>
                    <div>
                        <a href="view.php?id=<?php echo $user_id; ?>" class="btn btn-info me-2">
                            <i class="bi bi-eye me-2"></i>View Profile
                        </a>
                        <a href="index.php" class="btn btn-secondary">
                            <i class="bi bi-arrow-left me-2"></i>Back to List
                        </a>
                    </div>
                </div>

                <div class="row">
                    <!-- Main Edit Form -->
                    <div class="col-lg-8">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="card-title mb-0">
                                    <i class="bi bi-person-lines-fill me-2"></i>User Information
                                </h5>
                            </div>
                            <div class="card-body">
                                <form action="edit.php?id=<?php echo $user_id; ?>&action=edit" method="POST">
                                    <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                                    <input type="hidden" name="action" value="edit">

                                    <div class="row">
                                        <div class="col-md-6 mb-3">
                                            <label for="username" class="form-label">Username</label>
                                            <input type="text" class="form-control" id="username" name="username"
                                                   value="<?php echo htmlspecialchars($user['username']); ?>" required>
                                        </div>
                                        <div class="col-md-6 mb-3">
                                            <label for="phone" class="form-label">Phone</label>
                                            <input type="text" class="form-control" id="phone" name="phone"
                                                   value="<?php echo htmlspecialchars($user['phone']); ?>" required>
                                        </div>
                                    </div>

                                    <div class="row">
                                        <div class="col-md-6 mb-3">
                                            <label for="email" class="form-label">Email</label>
                                            <input type="email" class="form-control" id="email" name="email"
                                                   value="<?php echo htmlspecialchars($user['email'] ?? ''); ?>">
                                        </div>
                                        <div class="col-md-6 mb-3">
                                            <label for="gender" class="form-label">Gender</label>
                                            <select class="form-select" id="gender" name="gender" required>
                                                <option value="male" <?php echo $user['gender'] === 'male' ? 'selected' : ''; ?>>Male</option>
                                                <option value="female" <?php echo $user['gender'] === 'female' ? 'selected' : ''; ?>>Female</option>
                                            </select>
                                        </div>
                                    </div>

                                    <div class="row">
                                        <div class="col-md-6 mb-3">
                                            <label for="vip_level" class="form-label">VIP Level</label>
                                            <select class="form-select" id="vip_level" name="vip_level" required>
                                                <?php foreach ($vip_levels as $level): ?>
                                                <option value="<?php echo $level['level']; ?>" <?php echo $user['vip_level'] == $level['level'] ? 'selected' : ''; ?>>
                                                    VIP <?php echo $level['level']; ?> - <?php echo htmlspecialchars($level['name']); ?>
                                                </option>
                                                <?php endforeach; ?>
                                            </select>
                                        </div>
                                        <div class="col-md-6 mb-3">
                                            <label for="status" class="form-label">Status</label>
                                            <select class="form-select" id="status" name="status" required>
                                                <?php foreach ($user_statuses as $status_option): ?>
                                                <option value="<?php echo $status_option; ?>" <?php echo $user['status'] === $status_option ? 'selected' : ''; ?>>
                                                    <?php echo ucfirst($status_option); ?>
                                                </option>
                                                <?php endforeach; ?>
                                            </select>
                                        </div>
                                    </div>

                                    <div class="row">
                                        <div class="col-md-4 mb-3">
                                            <label for="balance" class="form-label">Balance (USDT)</label>
                                            <input type="number" class="form-control" id="balance" name="balance"
                                                   step="0.01" value="<?php echo $user['balance']; ?>">
                                        </div>
                                        <div class="col-md-4 mb-3">
                                            <label for="commission_balance" class="form-label">Commission Balance</label>
                                            <input type="number" class="form-control" id="commission_balance" name="commission_balance"
                                                   step="0.01" value="<?php echo $user['commission_balance']; ?>">
                                        </div>
                                        <div class="col-md-4 mb-3">
                                            <label for="frozen_balance" class="form-label">Frozen Balance</label>
                                            <input type="number" class="form-control" id="frozen_balance" name="frozen_balance"
                                                   step="0.01" value="<?php echo $user['frozen_balance']; ?>">
                                        </div>
                                    </div>

                                    <!-- Superior Assignment Section -->
                                    <div class="row">
                                        <div class="col-md-6 mb-3">
                                            <label for="agent_code" class="form-label">Assign Superior</label>
                                            <select class="form-select" id="agent_code" name="agent_code">
                                                <option value="">No Superior</option>
                                                <?php foreach ($all_agents as $agent): ?>
                                                <option value="<?php echo htmlspecialchars($agent['invitation_code']); ?>"
                                                        data-name="<?php echo htmlspecialchars($agent['name']); ?>"
                                                        data-phone="<?php echo htmlspecialchars($agent['phone']); ?>"
                                                        <?php echo $user['invited_by'] === $agent['invitation_code'] ? 'selected' : ''; ?>>
                                                    <?php echo htmlspecialchars($agent['name']); ?> (<?php echo htmlspecialchars($agent['invitation_code']); ?>)
                                                </option>
                                                <?php endforeach; ?>
                                            </select>
                                        </div>
                                        <div class="col-md-3 mb-3">
                                            <label for="invited_by_name" class="form-label">Superior Name</label>
                                            <input type="text" class="form-control" id="invited_by_name"
                                                   value="<?php echo htmlspecialchars($user['inviter_username'] ?? 'N/A'); ?>" readonly>
                                        </div>
                                        <div class="col-md-3 mb-3">
                                            <label for="invited_by_phone" class="form-label">Superior Phone</label>
                                            <input type="text" class="form-control" id="invited_by_phone"
                                                   value="<?php echo htmlspecialchars($user['inviter_phone'] ?? 'N/A'); ?>" readonly>
                                        </div>
                                    </div>

                                    <div class="d-flex justify-content-end">
                                        <button type="button" class="btn btn-secondary me-2" onclick="window.history.back()">Cancel</button>
                                        <button type="submit" class="btn btn-primary">
                                            <i class="bi bi-check-circle me-2"></i>Update User
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>

                    <!-- Sidebar with Quick Actions -->
                    <div class="col-lg-4">
                        <!-- Avatar Upload -->
                        <div class="card mb-4">
                            <div class="card-header">
                                <h5 class="card-title mb-0">
                                    <i class="bi bi-person-circle me-2"></i>Avatar
                                </h5>
                            </div>
                            <div class="card-body text-center">
                                <div class="avatar-preview mb-3">
                                    <?php if ($user['avatar_url']): ?>
                                        <img src="<?php echo BASE_URL . $user['avatar_url']; ?>" alt="Avatar" class="rounded-circle" style="width: 100px; height: 100px; object-fit: cover;">
                                    <?php else: ?>
                                        <div class="avatar-placeholder rounded-circle d-flex align-items-center justify-content-center" style="width: 100px; height: 100px; background: #f8f9fa; border: 2px dashed #dee2e6; margin: 0 auto;">
                                            <i class="bi bi-person fs-1 text-muted"></i>
                                        </div>
                                    <?php endif; ?>
                                </div>

                                <form action="edit.php?id=<?php echo $user_id; ?>&action=upload_avatar" method="POST" enctype="multipart/form-data">
                                    <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                                    <input type="hidden" name="action" value="upload_avatar">
                                    <div class="mb-3">
                                        <input type="file" class="form-control" name="avatar" accept="image/*" required>
                                    </div>
                                    <button type="submit" class="btn btn-primary btn-sm">
                                        <i class="bi bi-upload me-2"></i>Upload Avatar
                                    </button>
                                </form>
                            </div>
                        </div>

                        <!-- Quick Actions -->
                        <div class="card mb-4">
                            <div class="card-header">
                                <h5 class="card-title mb-0">
                                    <i class="bi bi-lightning me-2"></i>Quick Actions
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="d-grid gap-2">
                                    <button type="button" class="btn btn-outline-primary" data-bs-toggle="modal" data-bs-target="#creditScoreModal">
                                        <i class="bi bi-star me-2"></i>Credit Score
                                    </button>
                                    <button type="button" class="btn btn-outline-warning" data-bs-toggle="modal" data-bs-target="#passwordModal">
                                        <i class="bi bi-key me-2"></i>Change Password
                                    </button>
                                    <button type="button" class="btn btn-outline-info" data-bs-toggle="modal" data-bs-target="#paymentPasswordModal">
                                        <i class="bi bi-shield-lock me-2"></i>Payment Password
                                    </button>
                                    <button type="button" class="btn btn-outline-success" data-bs-toggle="modal" data-bs-target="#balanceModal">
                                        <i class="bi bi-cash-coin me-2"></i>Adjust Balance
                                    </button>
                                    <a href="negative_settings.php?id=<?php echo $user_id; ?>" class="btn btn-outline-danger">
                                        <i class="bi bi-exclamation-triangle me-2"></i>Negative Settings
                                    </a>
                                    <a href="reset_task.php?id=<?php echo $user_id; ?>" class="btn btn-outline-secondary">
                                        <i class="bi bi-arrow-clockwise me-2"></i>Reset Tasks
                                    </a>
                                </div>
                            </div>
                        </div>


                    </div>
                </div>
            </div>
        </div>
        <?php include '../includes/admin_footer.php'; ?>
    </div>
</div>

<!-- Credit Score Modal -->
<div class="modal fade" id="creditScoreModal" tabindex="-1" aria-labelledby="creditScoreModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="creditScoreModalLabel">Update Credit Score</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form action="edit.php?id=<?php echo $user_id; ?>&action=credit_score" method="POST">
                <div class="modal-body">
                    <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                    <input type="hidden" name="action" value="credit_score">
                    <div class="mb-3">
                        <label for="score" class="form-label">Credit Score Percentage</label>
                        <input type="number" class="form-control" id="score" name="score"
                               value="<?php echo htmlspecialchars($user['credit_score'] ?? '100'); ?>"
                               min="0" max="100" placeholder="Enter credit score (0-100)" required>
                        <div class="form-text">Enter a percentage value between 0 and 100</div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Update Score</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Password Modal -->
<div class="modal fade" id="passwordModal" tabindex="-1" aria-labelledby="passwordModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="passwordModalLabel">Change Login Password</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form action="edit.php?id=<?php echo $user_id; ?>&action=change_password" method="POST">
                <div class="modal-body">
                    <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                    <input type="hidden" name="action" value="change_password">
                    <div class="mb-3">
                        <label for="new_password" class="form-label">New Login Password</label>
                        <input type="password" class="form-control" id="new_password" name="new_password"
                               placeholder="Enter new login password" required minlength="6">
                        <div class="form-text">Password must be at least 6 characters long</div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-warning">Update Password</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Payment Password Modal -->
<div class="modal fade" id="paymentPasswordModal" tabindex="-1" aria-labelledby="paymentPasswordModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="paymentPasswordModalLabel">Change Payment Password (Withdrawal PIN)</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form action="edit.php?id=<?php echo $user_id; ?>&action=change_payment_password" method="POST">
                <div class="modal-body">
                    <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                    <input type="hidden" name="action" value="change_payment_password">
                    <div class="mb-3">
                        <label for="new_payment_password" class="form-label">New Payment Password (Withdrawal PIN)</label>
                        <input type="password" class="form-control" id="new_payment_password" name="new_payment_password"
                               placeholder="Enter new payment password" required minlength="4">
                        <div class="form-text">This PIN is required for withdrawal requests. Must be at least 4 characters.</div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-info">Update Payment Password</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Balance Adjustment Modal -->
<div class="modal fade" id="balanceModal" tabindex="-1" aria-labelledby="balanceModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="balanceModalLabel">Adjust User Balance</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form action="edit.php?id=<?php echo $user_id; ?>&action=adjust_balance" method="POST">
                <div class="modal-body">
                    <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                    <input type="hidden" name="action" value="adjust_balance">
                    <p><strong>Username:</strong> <?php echo htmlspecialchars($user['username']); ?></p>
                    <p><strong>Current Balance:</strong> USDT <?php echo number_format($user['balance'], 2); ?></p>
                    <div class="mb-3">
                        <label for="amount" class="form-label">Amount (USDT)</label>
                        <input type="number" class="form-control" id="amount" name="amount" step="0.01" min="0.01" required>
                    </div>
                    <div class="mb-3">
                        <label for="type" class="form-label">Operation Type</label>
                        <select class="form-select" id="type" name="type" required>
                            <option value="">Select operation</option>
                            <option value="addition">Add to Balance</option>
                            <option value="deduction">Deduct from Balance</option>
                        </select>
                    </div>
                    <div class="alert alert-warning">
                        <i class="bi bi-exclamation-triangle me-2"></i>
                        <strong>Warning:</strong> This will directly modify the user's balance and create a transaction record.
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-success">Adjust Balance</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
// Superior dropdown auto-fill functionality
document.addEventListener('DOMContentLoaded', function() {
    const agentSelect = document.getElementById('agent_code');
    const nameField = document.getElementById('invited_by_name');
    const phoneField = document.getElementById('invited_by_phone');

    if (agentSelect && nameField && phoneField) {
        // Function to update fields
        function updateSuperiorFields() {
            const selected = agentSelect.options[agentSelect.selectedIndex];
            if (selected.value === '') {
                nameField.value = 'N/A';
                phoneField.value = 'N/A';
            } else {
                nameField.value = selected.getAttribute('data-name') || 'N/A';
                phoneField.value = selected.getAttribute('data-phone') || 'N/A';
            }
        }

        // Add change event listener
        agentSelect.addEventListener('change', updateSuperiorFields);

        // Initialize fields on page load
        updateSuperiorFields();
    }
});

// Helper function for status badge classes
function getStatusBadgeClass(status) {
    switch(status) {
        case 'active': return 'success';
        case 'pending': return 'warning';
        case 'suspended': return 'danger';
        case 'banned': return 'dark';
        default: return 'secondary';
    }
}
</script>

<?php
// Note: getStatusBadgeClass() function is already defined in includes/functions.php

include '../includes/admin_footer_scripts.php';
?>
