<?php
/**
 * Bamboo Web Application - User's Downline Team
 * Company: Notepadsly
 * Version: 1.0
 */

define('BAMBOO_APP', true);
require_once '../../includes/config.php';
require_once '../../includes/functions.php';

if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

if (!isAdminLoggedIn()) {
    redirect('admin/login/');
}

$user_id = (int)($_GET['id'] ?? 0);
if ($user_id === 0) {
    redirect('admin/member_management/');
}

$user = fetchRow('SELECT username FROM users WHERE id = ?', [$user_id]);
if (!$user) {
    redirect('admin/member_management/');
}

$page_title = 'My Team: ' . htmlspecialchars($user['username']);
include '../includes/admin_header.php';

// Fetch direct referrals (Level 1)
$level1_referrals = fetchAll('SELECT id, username, email, phone, created_at FROM users WHERE referred_by_user_id = ?', [$user_id]) ?: [];

// Function to recursively fetch downline (for demonstration, can be optimized for large datasets)
function getDownline($referrer_id, &$downline_users, $level = 1) {
    $direct_referrals = fetchAll('SELECT id, username, email, phone, created_at FROM users WHERE referred_by_user_id = ?', [$referrer_id]) ?: [];
    foreach ($direct_referrals as $referral) {
        $referral['level'] = $level;
        $downline_users[] = $referral;
        getDownline($referral['id'], $downline_users, $level + 1);
    }
}

$all_downline_users = [];
getDownline($user_id, $all_downline_users);

?>

<div class="admin-wrapper">
    <?php include '../includes/admin_sidebar.php'; ?>
    <div class="admin-main">
        <?php include '../includes/admin_topbar.php'; ?>
        <div class="admin-content">
            <div class="container-fluid">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h1 class="h3 mb-0">My Team for: <?php echo htmlspecialchars($user['username']); ?></h1>
                    <a href="view.php?id=<?php echo $user_id; ?>" class="btn btn-secondary"><i class="bi bi-arrow-left me-2"></i>Back to User Details</a>
                </div>

                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="card-title mb-0">Direct Referrals (Level 1)</h5>
                    </div>
                    <div class="card-body">
                        <?php if (empty($level1_referrals)): ?>
                            <p class="text-muted">No direct referrals found for this user.</p>
                        <?php else: ?>
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>ID</th>
                                            <th>Username</th>
                                            <th>Email</th>
                                            <th>Phone</th>
                                            <th>Joined</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($level1_referrals as $referral): ?>
                                            <tr>
                                                <td><?php echo $referral['id']; ?></td>
                                                <td><?php echo htmlspecialchars($referral['username']); ?></td>
                                                <td><?php echo htmlspecialchars($referral['email']); ?></td>
                                                <td><?php echo htmlspecialchars($referral['phone']); ?></td>
                                                <td><?php echo formatDate($referral['created_at']); ?></td>
                                                <td>
                                                    <a href="view.php?id=<?php echo $referral['id']; ?>" class="btn btn-sm btn-outline-primary" title="View"><i class="bi bi-eye"></i></a>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>

                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">Full Downline (All Levels)</h5>
                    </div>
                    <div class="card-body">
                        <?php if (empty($all_downline_users)): ?>
                            <p class="text-muted">No downline users found for this user.</p>
                        <?php else: ?>
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>ID</th>
                                            <th>Username</th>
                                            <th>Email</th>
                                            <th>Phone</th>
                                            <th>Level</th>
                                            <th>Joined</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($all_downline_users as $referral): ?>
                                            <tr>
                                                <td><?php echo $referral['id']; ?></td>
                                                <td><?php echo htmlspecialchars($referral['username']); ?></td>
                                                <td><?php echo htmlspecialchars($referral['email']); ?></td>
                                                <td><?php echo htmlspecialchars($referral['phone']); ?></td>
                                                <td><?php echo $referral['level']; ?></td>
                                                <td><?php echo formatDate($referral['created_at']); ?></td>
                                                <td>
                                                    <a href="view.php?id=<?php echo $referral['id']; ?>" class="btn btn-sm btn-outline-primary" title="View"><i class="bi bi-eye"></i></a>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>

            </div>
        </div>
        <?php include '../includes/admin_footer.php'; ?>
    </div>
</div>

<?php include '../includes/admin_footer_scripts.php'; ?>
