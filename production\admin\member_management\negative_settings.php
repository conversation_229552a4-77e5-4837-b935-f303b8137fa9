<?php
/**
 * Bamboo Web Application - Negative Settings Management
 * Company: Notepadsly
 * Version: 1.0
 */

define('BAMBOO_APP', true);
require_once '../../includes/config.php';
require_once '../../includes/functions.php';

if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

if (!isAdminLoggedIn()) {
    redirect('admin/login/');
}

$user_id = (int)($_GET['id'] ?? 0);
if ($user_id === 0) {
    redirect('admin/member_management/');
}

$user = fetchRow('SELECT username, balance, avatar, tasks_completed_today FROM users WHERE id = ?', [$user_id]);
if (!$user) {
    redirect('admin/member_management/');
}

// Get user's task progress
$task_progress = fetchRow("SELECT COUNT(*) as completed_tasks FROM task_completions WHERE user_id = ? AND DATE(completed_at) = CURDATE()", [$user_id]);
$completed_tasks = $task_progress ? $task_progress['completed_tasks'] : 0;

// Get user's VIP level to determine max daily tasks
$vip_info = fetchRow("SELECT v.max_daily_tasks FROM users u LEFT JOIN vip_levels v ON u.vip_level = v.level WHERE u.id = ?", [$user_id]);
$max_daily_tasks = $vip_info ? $vip_info['max_daily_tasks'] : 0;

// Get appearance settings for gradient colors
$gradient_start = getAppSetting('appearance_gradient_start', '#ff6900');
$gradient_end = getAppSetting('appearance_gradient_end', '#ff8533');

$page_title = 'Negative Settings for ' . htmlspecialchars($user['username']);
$additional_css = [BASE_URL . 'admin/assets/css/member-details.css'];
include '../includes/admin_header.php';

// Handle form submission for adding negative setting
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['csrf_token'])) {
    if (!verifyCSRFToken($_POST['csrf_token'])) {
        showError('Invalid security token. Please try again.');
    } else {
        $trigger_task_number = (int)$_POST['trigger_task_number'];
        $product_id_override = (int)$_POST['product_id_override'];
        $override_amount = (float)$_POST['override_amount'];

        if ($trigger_task_number > 0 && $product_id_override > 0 && $override_amount > 0) {
            insertRecord('negative_settings', [
                'user_id' => $user_id,
                'trigger_task_number' => $trigger_task_number,
                'product_id_override' => $product_id_override,
                'override_amount' => $override_amount,
                'admin_id_created' => $_SESSION['admin_id']
            ]);
            showSuccess('Negative setting added successfully!');
        } else {
            showError('Invalid data provided.');
        }
        redirect('admin/member_management/negative_settings.php?id=' . $user_id);
        exit();
    }
}

// Handle deletion of negative setting
if (isset($_GET['action']) && $_GET['action'] === 'delete' && isset($_GET['setting_id'])) {
    $setting_id = (int)($_GET['setting_id'] ?? 0);
    if ($setting_id > 0) {
        deleteRecord('negative_settings', 'id = ? AND user_id = ?', [$setting_id, $user_id]);
        showSuccess('Negative setting deleted successfully!');
    } else {
        showError('Invalid setting ID.');
    }
    redirect('admin/member_management/negative_settings.php?id=' . $user_id);
    exit();
}

$products = fetchAll('SELECT id, name FROM products');
$negative_settings = fetchAll('SELECT ns.*, p.name as product_name FROM negative_settings ns JOIN products p ON ns.product_id_override = p.id WHERE ns.user_id = ?', [$user_id]);

?>

<div class="admin-wrapper">
    <?php include '../includes/admin_sidebar.php'; ?>
    <div class="admin-main">
        <?php include '../includes/admin_topbar.php'; ?>
        <div class="admin-content">
            <div class="container-fluid">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <div class="d-flex align-items-center">
                        <div class="user-avatar-header me-3">
                            <?php if (!empty($user['avatar'])): ?>
                                <img src="<?php echo BASE_URL . 'uploads/avatars/' . $user['avatar']; ?>" alt="Avatar" class="header-avatar">
                            <?php else: ?>
                                <div class="header-avatar-initials">
                                    <?php echo strtoupper(substr($user['username'], 0, 2)); ?>
                                </div>
                            <?php endif; ?>
                        </div>
                        <div>
                            <h1 class="h3 mb-0">
                                <i class="bi bi-exclamation-triangle-fill text-warning me-2"></i>
                                Negative Settings: <?php echo htmlspecialchars($user['username']); ?>
                            </h1>
                            <small class="text-muted">Configure negative balance triggers and overrides</small>
                        </div>
                    </div>
                    <a href="view.php?id=<?php echo $user_id; ?>" class="btn btn-secondary"><i class="bi bi-arrow-left me-2"></i>Back to User Details</a>
                </div>

                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="bi bi-person-circle me-2"></i>User Information
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-4">
                                <div class="info-item">
                                    <div class="info-label">Username</div>
                                    <div class="info-value"><?php echo htmlspecialchars($user['username']); ?></div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="info-item">
                                    <div class="info-label">Wallet Balance</div>
                                    <div class="info-value text-success fw-bold"><?php echo formatCurrency($user['balance']); ?></div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="info-item">
                                    <div class="info-label">Daily Task Progress</div>
                                    <div class="info-value">
                                        <span class="task-progress">
                                            <span class="completed-tasks text-primary fw-bold"><?php echo $completed_tasks; ?></span>
                                            <span class="task-separator">/</span>
                                            <span class="total-tasks text-muted"><?php echo $max_daily_tasks; ?></span>
                                        </span>
                                        <div class="progress mt-2" style="height: 6px;">
                                            <div class="progress-bar bg-primary" role="progressbar"
                                                 style="width: <?php echo $max_daily_tasks > 0 ? ($completed_tasks / $max_daily_tasks * 100) : 0; ?>%"></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="card mb-4">
                    <div class="card-header text-white" style="background: linear-gradient(135deg, <?php echo $gradient_start; ?>, <?php echo $gradient_end; ?>);">
                        <h5 class="card-title mb-0">
                            <i class="bi bi-plus-circle me-2"></i>Add Negative Setting
                        </h5>
                        <small class="opacity-75">Configure when negative balance triggers should activate</small>
                    </div>
                    <div class="card-body">
                        <form action="negative_settings.php?id=<?php echo $user_id; ?>" method="POST">
                            <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                            <div class="row">
                                <div class="col-md-4 mb-3">
                                    <label for="trigger_task_number" class="form-label">
                                        <i class="bi bi-hash text-primary me-1"></i>Trigger Task Number
                                    </label>
                                    <input type="number" class="form-control" id="trigger_task_number" name="trigger_task_number"
                                           placeholder="e.g., 5" min="1" required>
                                    <div class="form-text">Task number when negative setting activates</div>
                                </div>
                                <div class="col-md-4 mb-3">
                                    <label for="product_id_override" class="form-label">
                                        <i class="bi bi-box-seam text-primary me-1"></i>Product Override
                                    </label>
                                    <select class="form-select" id="product_id_override" name="product_id_override" required>
                                        <option value="">Select Product</option>
                                        <?php foreach ($products as $product): ?>
                                            <option value="<?php echo $product['id']; ?>"><?php echo htmlspecialchars($product['name']); ?></option>
                                        <?php endforeach; ?>
                                    </select>
                                    <div class="form-text">Product to use when triggered</div>
                                </div>
                                <div class="col-md-4 mb-3">
                                    <label for="override_amount" class="form-label">
                                        <i class="bi bi-currency-dollar text-success me-1"></i>Override Amount
                                    </label>
                                    <div class="input-group">
                                        <span class="input-group-text">$</span>
                                        <input type="number" class="form-control" id="override_amount" name="override_amount"
                                               step="0.01" min="0.01" placeholder="0.00" required>
                                    </div>
                                    <div class="form-text">Amount to charge when triggered</div>
                                </div>
                            </div>
                            <div class="d-flex gap-2">
                                <button type="submit" class="btn btn-warning">
                                    <i class="bi bi-plus-circle me-2"></i>Add Negative Setting
                                </button>
                                <button type="reset" class="btn btn-outline-secondary">
                                    <i class="bi bi-arrow-clockwise me-2"></i>Reset Form
                                </button>
                            </div>
                        </form>
                    </div>
                </div>

                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">Existing Negative Settings</h5>
                    </div>
                    <div class="card-body">
                        <?php if (empty($negative_settings)): ?>
                            <p class="text-muted">No negative settings found for this user.</p>
                        <?php else: ?>
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>S/N</th>
                                            <th>Trigger Task</th>
                                            <th>Product</th>
                                            <th>Amount</th>
                                            <th>Status</th>
                                            <th>Creation Date</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php $sn = 1; foreach ($negative_settings as $setting): ?>
                                            <tr>
                                                <td><?php echo $sn++; ?></td>
                                                <td><?php echo $setting['trigger_task_number']; ?></td>
                                                <td><?php echo htmlspecialchars($setting['product_name']); ?></td>
                                                <td><span class="text-success fw-bold"><?php echo formatCurrency($setting['override_amount']); ?></span></td>
                                                <td><?php echo $setting['is_triggered'] ? '<span class="badge bg-success">Triggered</span>' : '<span class="badge bg-warning">Pending</span>'; ?></td>
                                                <td><?php echo formatDate($setting['created_at']); ?></td>
                                                <td>
                                                    <a href="negative_settings.php?id=<?php echo $user_id; ?>&action=delete&setting_id=<?php echo $setting['id']; ?>" class="btn btn-sm btn-danger" onclick="return confirm('Are you sure you want to delete this setting?')">Delete</a>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>

            </div>
        </div>
        <?php include '../includes/admin_footer.php'; ?>
    </div>
</div>

<?php include '../includes/admin_footer_scripts.php'; ?>
