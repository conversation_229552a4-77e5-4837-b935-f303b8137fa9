<?php
/**
 * Bamboo Web Application - Payment Card Management
 * Company: Notepadsly
 * Version: 1.0
 */

define('BAMBOO_APP', true);
require_once '../../includes/config.php';
require_once '../../includes/functions.php';

if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

if (!isAdminLoggedIn()) {
    redirect('admin/login/');
}

$user_id = (int)($_GET['id'] ?? 0);
if ($user_id === 0) {
    redirect('admin/member_management/');
}

$user = fetchRow('SELECT username, usdt_wallet_address, exchange_name FROM users WHERE id = ?', [$user_id]);
if (!$user) {
    redirect('admin/member_management/');
}

$page_title = 'Payment Card: ' . htmlspecialchars($user['username']);
include '../includes/admin_header.php';

// Handle form submission for updating payment card
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['csrf_token'])) {
    if (!verifyCSRFToken($_POST['csrf_token'])) {
        showError('Invalid security token. Please try again.');
    } else {
        $usdt_wallet_address = sanitizeInput($_POST['usdt_wallet_address']);
        $exchange_name = sanitizeInput($_POST['exchange_name']);

        if (updateRecord('users', ['usdt_wallet_address' => $usdt_wallet_address, 'exchange_name' => $exchange_name], 'id = ?', [$user_id])) {
            showSuccess('Payment card information updated successfully!');
        } else {
            showError('Failed to update payment card information.');
        }
        redirect('admin/member_management/payment_card.php?id=' . $user_id);
        exit();
    }
}

?>

<div class="admin-wrapper">
    <?php include '../includes/admin_sidebar.php'; ?>
    <div class="admin-main">
        <?php include '../includes/admin_topbar.php'; ?>
        <div class="admin-content">
            <div class="container-fluid">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h1 class="h3 mb-0">Payment Card for: <?php echo htmlspecialchars($user['username']); ?></h1>
                    <a href="view.php?id=<?php echo $user_id; ?>" class="btn btn-secondary"><i class="bi bi-arrow-left me-2"></i>Back to User Details</a>
                </div>

                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">Edit Payment Card Information</h5>
                    </div>
                    <div class="card-body">
                        <form action="payment_card.php?id=<?php echo $user_id; ?>" method="POST">
                            <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                            <div class="mb-3">
                                <label for="usdt_wallet_address" class="form-label">USDT Wallet Address</label>
                                <input type="text" class="form-control" id="usdt_wallet_address" name="usdt_wallet_address" value="<?php echo htmlspecialchars($user['usdt_wallet_address'] ?? ''); ?>">
                            </div>
                            <div class="mb-3">
                                <label for="exchange_name" class="form-label">Exchange Name</label>
                                <input type="text" class="form-control" id="exchange_name" name="exchange_name" value="<?php echo htmlspecialchars($user['exchange_name'] ?? ''); ?>">
                            </div>
                            <button type="submit" class="btn btn-primary">Save Changes</button>
                        </form>
                    </div>
                </div>

            </div>
        </div>
        <?php include '../includes/admin_footer.php'; ?>
    </div>
</div>

<?php include '../includes/admin_footer_scripts.php'; ?>
