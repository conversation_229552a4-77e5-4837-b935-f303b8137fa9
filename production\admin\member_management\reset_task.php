<?php
/**
 * Bamboo Web Application - Reset User Tasks
 * Company: Notepadsly
 * Version: 1.0
 */

define('BAMBOO_APP', true);
require_once '../../includes/config.php';
require_once '../../includes/functions.php';

if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

if (!isAdminLoggedIn()) {
    redirect('admin/login/');
}

$user_id = (int)($_GET['id'] ?? 0);
if ($user_id === 0) {
    redirect('admin/member_management/');
}

$user = fetchRow('SELECT username, tasks_completed_today FROM users WHERE id = ?', [$user_id]);
if (!$user) {
    redirect('admin/member_management/');
}

$page_title = 'Reset Tasks for ' . htmlspecialchars($user['username']);
include '../includes/admin_header.php';

// Handle form submission for resetting tasks
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['csrf_token'])) {
    if (!verifyCSRFToken($_POST['csrf_token'])) {
        showError('Invalid security token. Please try again.');
    } else {
        if (updateRecord('users', ['tasks_completed_today' => 0], 'id = ?', [$user_id])) {
            showSuccess('Tasks reset successfully!');
        } else {
            showError('Failed to reset tasks.');
        }
        redirect('admin/member_management/reset_task.php?id=' . $user_id);
        exit();
    }
}

?>

<div class="admin-wrapper">
    <?php include '../includes/admin_sidebar.php'; ?>
    <div class="admin-main">
        <?php include '../includes/admin_topbar.php'; ?>
        <div class="admin-content">
            <div class="container-fluid">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h1 class="h3 mb-0">Reset Tasks for: <?php echo htmlspecialchars($user['username']); ?></h1>
                    <a href="view.php?id=<?php echo $user_id; ?>" class="btn btn-secondary"><i class="bi bi-arrow-left me-2"></i>Back to User Details</a>
                </div>

                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">Reset Daily Tasks</h5>
                    </div>
                    <div class="card-body">
                        <p>This will reset <strong><?php echo htmlspecialchars($user['username']); ?></strong>'s completed tasks for today (currently <strong><?php echo $user['tasks_completed_today']; ?></strong>) to 0.</p>
                        <p class="text-danger">This action cannot be undone.</p>
                        <form action="reset_task.php?id=<?php echo $user_id; ?>" method="POST">
                            <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                            <button type="submit" class="btn btn-warning">Reset Tasks Now</button>
                        </form>
                    </div>
                </div>

            </div>
        </div>
        <?php include '../includes/admin_footer.php'; ?>
    </div>
</div>

<?php include '../includes/admin_footer_scripts.php'; ?>
