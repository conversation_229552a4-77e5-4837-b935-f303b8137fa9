<?php
/**
 * Bamboo Web Application - Admin Payment Card Page
 * Company: Notepadsly
 * Version: 1.0
 */

define('BAMBOO_APP', true);
require_once '../../includes/config.php';
require_once '../../includes/functions.php';

if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

if (!isAdminLoggedIn()) {
    redirect('admin/login/');
}

$page_title = 'Payment Card Management';
$page_section = 'payment_card';
$additional_css = [
    BASE_URL . 'admin/assets/css/admin.css',
    BASE_URL . 'admin/payment_card/payment-card.css'
];

// Pagination setup
$page = (int)($_GET['page'] ?? 1);
$per_page = 20;
$offset = ($page - 1) * $per_page;

// Fetch all users with wallet information
$query = "SELECT u.id, u.username, u.email, u.phone, u.usdt_wallet_address, u.exchange_name, u.updated_at as binding_time
          FROM users u
          WHERE u.usdt_wallet_address IS NOT NULL AND u.usdt_wallet_address != ''
          ORDER BY u.id DESC
          LIMIT $per_page OFFSET $offset";
$users_with_wallets = fetchAll($query);

// Get total count for pagination
$total_count = fetchValue("SELECT COUNT(*) FROM users WHERE usdt_wallet_address IS NOT NULL AND usdt_wallet_address != ''");
$total_pages = ceil($total_count / $per_page);

include '../includes/admin_header.php';
?>

<div class="admin-wrapper">
    <!-- Sidebar -->
    <?php include '../includes/admin_sidebar.php'; ?>

    <!-- Main Content -->
    <div class="admin-main">
        <!-- Top Header -->
        <?php include '../includes/admin_topbar.php'; ?>

        <!-- Content Area -->
        <div class="admin-content">
            <div class="container-fluid">

                <!-- Page Header -->
                <div class="page-header">
                    <h1>Payment Card Management</h1>
                    <p class="text-muted">Manage user wallet addresses and payment card information</p>
                </div>

                <div class="payment-card-management card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="bi bi-credit-card me-2"></i>User Wallet Addresses
                        </h5>
                    </div>
                    <div class="card-body">
                        <?php if (empty($users_with_wallets)): ?>
                            <div class="empty-state">
                                <i class="bi bi-wallet2"></i>
                                <h5>No Wallet Addresses Found</h5>
                                <p>No users have configured their wallet addresses yet.</p>
                            </div>
                        <?php else: ?>
                            <div class="table-responsive">
                                <table class="payment-card-table table">
                                    <thead>
                                        <tr>
                                            <th>S/N</th>
                                            <th>Member Info</th>
                                            <th>Exchange</th>
                                            <th>Wallet Address</th>
                                            <th>Binding Time</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php
                                        // Ensure proper numbering calculation
                                        $current_page = max(1, (int)$page);
                                        $items_per_page = max(1, (int)$per_page);
                                        $sn = ($current_page - 1) * $items_per_page + 1;
                                        ?>
                                        <?php foreach ($users_with_wallets as $user): ?>
                                            <tr>
                                                <td><div class="serial-number"><?php echo $sn++; ?></div></td>
                                                <td>
                                                    <div class="member-info">
                                                        <div class="username"><?php echo htmlspecialchars(ucfirst($user['username'])); ?></div>
                                                        <div class="contact">
                                                            E: <?php echo htmlspecialchars($user['email']); ?><br>
                                                            P: <?php echo htmlspecialchars($user['phone']); ?>
                                                        </div>
                                                    </div>
                                                </td>
                                                <td>
                                                    <div class="exchange-name"><?php echo htmlspecialchars($user['exchange_name'] ?? 'N/A'); ?></div>
                                                </td>
                                                <td>
                                                    <div class="wallet-address" title="<?php echo htmlspecialchars($user['usdt_wallet_address']); ?>">
                                                        <?php echo htmlspecialchars($user['usdt_wallet_address']); ?>
                                                    </div>
                                                </td>
                                                <td>
                                                    <div class="binding-time"><?php echo formatDate($user['binding_time']); ?></div>
                                                </td>
                                                <td>
                                                    <div class="action-buttons">
                                                        <a href="../member_management/view.php?id=<?php echo $user['id']; ?>" class="btn btn-sm btn-outline-primary" title="View User">
                                                            <i class="bi bi-eye me-1"></i>View
                                                        </a>
                                                        <a href="../member_management/edit.php?id=<?php echo $user['id']; ?>&action=wallet_info" class="btn btn-sm btn-outline-secondary" title="Edit Wallet">
                                                            <i class="bi bi-pencil me-1"></i>Edit
                                                        </a>
                                                    </div>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>

                            <!-- Pagination -->
                            <?php if ($total_pages > 1): ?>
                                <div class="d-flex justify-content-center mt-4">
                                    <nav aria-label="Payment Card Pagination">
                                        <ul class="pagination">
                                            <?php if ($page > 1): ?>
                                                <li class="page-item">
                                                    <a class="page-link" href="?page=<?php echo $page - 1; ?>">Previous</a>
                                                </li>
                                            <?php endif; ?>

                                            <?php for ($i = 1; $i <= $total_pages; $i++): ?>
                                                <li class="page-item <?php echo $i == $page ? 'active' : ''; ?>">
                                                    <a class="page-link" href="?page=<?php echo $i; ?>"><?php echo $i; ?></a>
                                                </li>
                                            <?php endfor; ?>

                                            <?php if ($page < $total_pages): ?>
                                                <li class="page-item">
                                                    <a class="page-link" href="?page=<?php echo $page + 1; ?>">Next</a>
                                                </li>
                                            <?php endif; ?>
                                        </ul>
                                    </nav>
                                </div>
                            <?php endif; ?>
                        <?php endif; ?>
                    </div>
                </div>

            </div>
        </div>

        <!-- Footer -->
        <?php include '../includes/admin_footer.php'; ?>
    </div>
</div>

<?php
$additional_js = [
    BASE_URL . 'admin/assets/js/admin.js'
];
include '../includes/admin_footer_scripts.php';
?>
