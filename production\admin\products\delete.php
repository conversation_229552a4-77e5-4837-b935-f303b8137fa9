<?php
/**
 * Bamboo Web Application - Delete Product
 * Company: Notepadsly
 * Version: 1.0
 */

// Define app constant
define('BAMBOO_APP', true);

// Include required files
require_once '../../includes/config.php';
require_once '../../includes/functions.php';

// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Check if admin is logged in
if (!isAdminLoggedIn()) {
    redirect('admin/login/');
}

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Validate CSRF token
    if (!verifyCSRFToken($_POST['csrf_token'] ?? '')) {
        showError('Invalid security token. Please try again.');
        redirect('admin/products/');
    }

    $product_id = (int)($_POST['product_id'] ?? 0);

    if ($product_id > 0) {
        try {
            // First, get the image filename to delete it from the server
            $product = fetchRow("SELECT image_path FROM products WHERE id = ?", [$product_id]);

            if (deleteRecord('products', 'id = ?', [$product_id])) {
                // If the product had an image, delete the file
                if ($product && !empty($product['image_path'])) {
                    $image_file = UPLOAD_PATH . 'products/' . $product['image_path'];
                    if (file_exists($image_file)) {
                        unlink($image_file);
                    }
                }
                showSuccess('Product deleted successfully!');
            } else {
                showError('Failed to delete product.');
            }
        } catch (Exception $e) {
            showError('Error deleting product: ' . $e->getMessage());
        }
    }
}

redirect('admin/products/');
?>