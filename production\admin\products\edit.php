<?php
/**
 * Bamboo Web Application - Edit Product
 * Company: Notepadsly
 * Version: 1.1
 */

// Define app constant
define('BAMBOO_APP', true);

// Include required files
require_once '../../includes/config.php';
require_once '../../includes/functions.php';

// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Check if admin is logged in
if (!isAdminLoggedIn()) {
    redirect('admin/login/');
}

$product_id = (int)($_GET['id'] ?? 0);
if ($product_id === 0) {
    redirect('admin/products/');
}

// Fetch product details
$product = fetchRow("SELECT * FROM products WHERE id = ?", [$product_id]);
if (!$product) {
    showError('Product not found.');
    redirect('admin/products/');
}

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (!verifyCSRFToken($_POST['csrf_token'] ?? '')) {
        showError('Invalid security token. Please try again.');
    } else {
        $name = sanitizeInput($_POST['name'] ?? '');
        $price = (float)($_POST['price'] ?? 0);
        $min_vip_level = (int)($_POST['min_vip_level'] ?? 1);
        $category_id = (int)($_POST['category_id'] ?? 0);
        $status = sanitizeInput($_POST['status'] ?? 'active');

        // Handle file upload
        $image_url = $product['image_url']; // Keep existing image by default
        if (isset($_FILES['image']) && $_FILES['image']['error'] === UPLOAD_ERR_OK) {
            $upload_result = handleFileUpload($_FILES['image'], 'products/');
            if ($upload_result['success']) {
                $image_url = $upload_result['file_url'];
            } else {
                $errors[] = 'Image upload failed: ' . $upload_result['message'];
            }
        }

        $errors = [];
        if (empty($name)) $errors[] = 'Product name is required';
        if ($price <= 0) $errors[] = 'Price must be greater than 0';

        if (empty($errors)) {
            try {
                $product_data = [
                    'name' => $name,
                    'price' => $price,
                    'min_vip_level' => $min_vip_level,
                    'category_id' => $category_id,
                    'status' => $status,
                    'image_url' => $image_url, // Correct column name
                    'updated_at' => date('Y-m-d H:i:s')
                ];
                
                if (updateRecord('products', $product_data, 'id = ?', [$product_id])) {
                    showSuccess('Product updated successfully!');
                    redirect('admin/products/');
                } else {
                    showError('Failed to update product. Please try again.');
                }
            } catch (Exception $e) {
                showError('Error updating product: ' . $e->getMessage());
            }
        }
    }
}

$vip_levels = fetchAll("SELECT level, name FROM vip_levels ORDER BY level ASC");

$page_title = 'Edit Product';
$body_class = 'admin-page';
$additional_css = [BASE_URL . 'admin/assets/css/admin.css'];

include '../includes/admin_header.php';
?>

<div class="admin-wrapper">
    <?php include '../includes/admin_sidebar.php'; ?>
    <div class="admin-main">
        <?php include '../includes/admin_topbar.php'; ?>
        <div class="admin-content">
            <div class="container-fluid">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h1 class="h3 mb-0">Edit Product</h1>
                    <a href="index.php" class="btn btn-secondary"><i class="bi bi-arrow-left me-2"></i>Back to Products</a>
                </div>
                <div class="card">
                    <div class="card-body">
                        <form action="" method="POST" enctype="multipart/form-data">
                            <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                            <div class="mb-3">
                                <label for="name" class="form-label">Product Name</label>
                                <input type="text" class="form-control" id="name" name="name" value="<?php echo htmlspecialchars($product['name']); ?>" required>
                            </div>
                            <div class="mb-3">
                                <label for="price" class="form-label">Product Amount</label>
                                <input type="number" class="form-control" id="price" name="price" value="<?php echo htmlspecialchars($product['price']); ?>" step="0.01" required>
                            </div>
                            <div class="mb-3">
                                <label for="category_id" class="form-label">Category</label>
                                <select class="form-select" id="category_id" name="category_id">
                                    <option value="">Select Category</option>
                                    <?php 
                                    $categories = fetchAll('SELECT id, name FROM product_categories ORDER BY name ASC');
                                    foreach ($categories as $category): ?>
                                        <option value="<?php echo $category['id']; ?>" <?php echo ($product['category_id'] == $category['id']) ? 'selected' : ''; ?>><?php echo htmlspecialchars($category['name']); ?></option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                            <div class="mb-3">
                                <label for="min_vip_level" class="form-label">Minimum VIP Level</label>
                                <select class="form-select" id="min_vip_level" name="min_vip_level">
                                    <?php foreach ($vip_levels as $level): ?>
                                        <option value="<?php echo $level['level']; ?>" <?php echo ($product['min_vip_level'] == $level['level']) ? 'selected' : ''; ?>><?php echo htmlspecialchars($level['name']); ?></option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                            <div class="mb-3">
                                <label for="image" class="form-label">Product Image</label>
                                <input type="file" class="form-control" id="image" name="image">
                                <?php if ($product['image_url']): ?>
                                    <img src="<?php echo BASE_URL . htmlspecialchars($product['image_url']); ?>" alt="Current Image" class="img-thumbnail mt-2" style="max-width: 150px;">
                                <?php endif; ?>
                            </div>
                            <div class="mb-3">
                                <label for="status" class="form-label">Status</label>
                                <select class="form-select" id="status" name="status">
                                    <option value="active" <?php echo ($product['status'] === 'active') ? 'selected' : ''; ?>>Active</option>
                                    <option value="inactive" <?php echo ($product['status'] === 'inactive') ? 'selected' : ''; ?>>Inactive</option>
                                </select>
                            </div>
                            <button type="submit" class="btn btn-primary">Save Changes</button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
        <?php include '../includes/admin_footer.php'; ?>
    </div>
</div>

<?php 
$additional_js = [BASE_URL . 'admin/assets/js/admin.js'];
include '../includes/admin_footer_scripts.php';
?>