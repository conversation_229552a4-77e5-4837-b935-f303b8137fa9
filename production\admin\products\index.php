<?php
/**
 * Bamboo Web Application - Product Management
 * Company: Notepadsly
 * Version: 1.0
 */

// Define app constant
define('BAMBOO_APP', true);

// Include required files
require_once '../../includes/config.php';
require_once '../../includes/functions.php';

// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Check if admin is logged in
if (!isAdminLoggedIn()) {
    redirect('admin/login/');
}

// Page configuration
$page_title = 'Product Management';
$body_class = 'admin-page';
$additional_css = [
    BASE_URL . 'admin/assets/css/admin.css'
];

// Get products with pagination
$page = (int)($_GET['page'] ?? 1);
$limit = 20;
$offset = ($page - 1) * $limit;

try {
    $total_products = getRecordCount('products');
    $products = fetchAll("SELECT p.id, p.name, p.price, p.status, p.created_at, p.image_url, v.name as vip_level_name 
                          FROM products p
                          LEFT JOIN vip_levels v ON p.min_vip_level = v.level
                          ORDER BY p.created_at DESC 
                          LIMIT ? OFFSET ?", [$limit, $offset]);
    $total_pages = ceil($total_products / $limit);
} catch (Exception $e) {
    $products = [];
    $total_products = 0;
    $total_pages = 0;
    showError('Failed to load products: ' . $e->getMessage());
}

// Include admin header
include '../includes/admin_header.php';
?>

<style>
/* Enhanced Table Styling with Primary Color Integration */
.table {
    border-collapse: separate;
    border-spacing: 0;
    border-radius: 0.75rem;
    overflow: hidden;
    box-shadow: 0 0.125rem 0.5rem rgba(0, 0, 0, 0.08);
}

.table tbody tr {
    border-bottom: 2px solid #e9ecef !important;
    transition: all 0.3s ease;
    position: relative;
}

.table tbody tr:nth-child(even) {
    background-color: rgba(248, 249, 250, 0.5);
}

.table tbody tr:hover {
    background: linear-gradient(135deg, rgba(var(--admin-primary-rgb, 255, 105, 0), 0.05) 0%, rgba(var(--admin-primary-rgb, 255, 105, 0), 0.02) 100%);
    transform: scale(1.002);
    box-shadow: 0 0.25rem 0.75rem rgba(0, 0, 0, 0.12);
    border-bottom-color: rgba(var(--admin-primary-rgb, 255, 105, 0), 0.3) !important;
}

.table tbody tr:last-child {
    border-bottom: 2px solid #e9ecef !important;
}

.table thead th {
    background: var(--admin-table-header-bg, var(--admin-primary, #ff6900)) !important;
    border-bottom: 3px solid rgba(var(--dynamic-primary-rgb, 255, 105, 0), 0.3) !important;
    border-top: none !important;
    font-weight: 600;
    color: var(--admin-table-header-text, #ffffff) !important;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    padding: 1.25rem 0.75rem;
}

.table td {
    vertical-align: middle;
    padding: 1.25rem 0.75rem;
    border-left: none;
    border-right: none;
    position: relative;
}

.table th {
    border-left: none;
    border-right: none;
}

/* Row Separator Enhancement */
.table tbody tr::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 1rem;
    right: 1rem;
    height: 1px;
    background: linear-gradient(90deg, transparent 0%, #dee2e6 20%, #dee2e6 80%, transparent 100%);
}

.table tbody tr:last-child::after {
    display: none;
}
</style>

<div class="admin-wrapper">
    <!-- Sidebar -->
    <?php include '../includes/admin_sidebar.php'; ?>
    
    <!-- Main Content -->
    <div class="admin-main">
        <!-- Top Header -->
        <?php include '../includes/admin_topbar.php'; ?>
        
        <!-- Content Area -->
        <div class="admin-content">
            <div class="container-fluid">
                
                <!-- Page Header -->
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <div>
                        <h1 class="h3 mb-0">Product Management</h1>
                        <p class="text-muted">Manage your products and services</p>
                    </div>
                    <div>
                        <a href="add.php" class="btn btn-primary">
                            <i class="bi bi-plus-circle me-2"></i>Add New Product
                        </a>
                    </div>
                </div>
                
                <!-- Products Table -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="bi bi-box-seam me-2"></i>Products (<?php echo number_format($total_products); ?>)
                        </h5>
                    </div>
                    <div class="card-body">
                        <?php if (!empty($products)): ?>
                            <div class="table-responsive">
                                <table class="table table-hover align-middle">
                                    <thead>
                                        <tr>
                                            <th>ID</th>
                                            <th>Picture</th>
                                            <th>Name</th>
                                            <th>Price</th>
                                            <th>VIP Level</th>
                                            <th>Status</th>
                                            <th>Created</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($products as $product): ?>
                                            <tr>
                                                <td><?php echo $product['id']; ?></td>
                                                <td>
                                                    <?php if (!empty($product['image_url'])): ?>
                                                        <img src="<?php echo BASE_URL . 'uploads/products/' . basename($product['image_url']); ?>" alt="<?php echo htmlspecialchars($product['name']); ?>" style="width: 50px; height: 50px; object-fit: cover; border-radius: 5px;">
                                                    <?php else: ?>
                                                        <i class="bi bi-box-seam" style="font-size: 2rem; color: #ccc;"></i>
                                                    <?php endif; ?>
                                                </td>
                                                <td>
                                                    <strong><?php echo htmlspecialchars($product['name']); ?></strong>
                                                </td>
                                                <td><?php echo formatCurrency($product['price']); ?></td>
                                                <td><span class="badge bg-info"><?php echo htmlspecialchars($product['vip_level_name'] ?? 'Any'); ?></span></td>
                                                <td>
                                                    <span class="badge bg-<?php echo $product['status'] === 'active' ? 'success' : 'secondary'; ?>">
                                                        <?php echo ucfirst($product['status']); ?>
                                                    </span>
                                                </td>
                                                <td><?php echo formatDate($product['created_at'], 'M j, Y'); ?></td>
                                                <td>
                                                    <div class="btn-group btn-group-sm">
                                                        <a href="edit.php?id=<?php echo $product['id']; ?>" class="btn btn-outline-secondary" title="Edit">
                                                            <i class="bi bi-pencil"></i>
                                                        </a>
                                                        <button type="button" class="btn btn-outline-danger" title="Delete" onclick="deleteProduct(<?php echo $product['id']; ?>, '<?php echo htmlspecialchars(addslashes($product['name'])); ?>')">
                                                            <i class="bi bi-trash"></i>
                                                        </button>
                                                    </div>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                            
                            <!-- Pagination -->
                            <?php if ($total_pages > 1): ?>
                                <nav aria-label="Products pagination">
                                    <ul class="pagination justify-content-center">
                                        <?php if ($page > 1): ?>
                                            <li class="page-item">
                                                <a class="page-link" href="?page=<?php echo $page - 1; ?>">Previous</a>
                                            </li>
                                        <?php endif; ?>
                                        
                                        <?php for ($i = 1; $i <= $total_pages; $i++): ?>
                                            <li class="page-item <?php echo $i === $page ? 'active' : ''; ?>">
                                                <a class="page-link" href="?page=<?php echo $i; ?>"><?php echo $i; ?></a>
                                            </li>
                                        <?php endfor; ?>
                                        
                                        <?php if ($page < $total_pages): ?>
                                            <li class="page-item">
                                                <a class="page-link" href="?page=<?php echo $page + 1; ?>">Next</a>
                                            </li>
                                        <?php endif; ?>
                                    </ul>
                                </nav>
                            <?php endif; ?>
                            
                        <?php else: ?>
                            <div class="text-center text-muted py-5">
                                <i class="bi bi-box-seam" style="font-size: 3rem;"></i>
                                <h4 class="mt-3">No Products Found</h4>
                                <p>No products have been added yet.</p>
                                <a href="add.php" class="btn btn-primary">Add First Product</a>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
                
            </div>
        </div>
        
        <!-- Footer -->
        <?php include '../includes/admin_footer.php'; ?>
    </div>
</div>

<!-- Delete Form (Hidden) -->
<form id="deleteForm" method="POST" action="delete.php" style="display: none;">
    <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
    <input type="hidden" name="product_id" id="deleteProductId">
</form>

<script>
function deleteProduct(productId, productName) {
    if (confirm('Are you sure you want to delete the product "' + productName + '"? This action cannot be undone.')) {
        document.getElementById('deleteProductId').value = productId;
        document.getElementById('deleteForm').submit();
    }
}
</script>

<?php 
$additional_js = [
    BASE_URL . 'admin/assets/js/admin.js'
];
include '../includes/admin_footer_scripts.php';
?>