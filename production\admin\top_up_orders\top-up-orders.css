/**
 * Bamboo Web Application - Top Up Orders Styles
 * Company: Notepadsly
 * Version: 1.0
 */

/* ===== TOP UP ORDERS STYLES ===== */

/* Page Header */
.page-header {
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid rgba(0, 0, 0, 0.04);
}

.page-header h1 {
    font-size: 2.25rem;
    font-weight: 700;
    color: var(--admin-text-dark);
    margin-bottom: 0.5rem;
    background: linear-gradient(135deg, var(--admin-text-dark), rgba(44, 62, 80, 0.8));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* Orders Card */
.orders-card {
    border: none;
    border-radius: 1rem;
    box-shadow: 0 0.125rem 0.5rem rgba(0, 0, 0, 0.08);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    background: #ffffff;
    overflow: hidden;
}

.orders-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 0.75rem 2rem rgba(0, 0, 0, 0.12);
}

.orders-card .card-header {
    background: linear-gradient(135deg, #fafbfc 0%, #f1f3f4 100%);
    border-bottom: 1px solid rgba(0, 0, 0, 0.06);
    padding: 1.25rem 1.5rem;
    position: relative;
}

.orders-card .card-header::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 1.5rem;
    right: 1.5rem;
    height: 1px;
    background: linear-gradient(90deg, transparent, rgba(0, 0, 0, 0.1), transparent);
}

.orders-card .card-body {
    padding: 0;
}

/* Search Form */
.search-form {
    display: flex;
    gap: 0.5rem;
    align-items: center;
}

.search-form .form-control {
    border: 2px solid rgba(0, 0, 0, 0.06);
    border-radius: 0.5rem;
    padding: 0.5rem 0.75rem;
    font-size: 0.875rem;
    transition: all 0.3s ease;
}

.search-form .form-control:focus {
    border-color: var(--admin-primary);
    box-shadow: 0 0 0 0.2rem rgba(var(--admin-primary-rgb), 0.25);
}

.search-form .btn {
    border-radius: 0.5rem;
    padding: 0.5rem 1rem;
    font-weight: 500;
    transition: all 0.3s ease;
}

/* Modern Table Styling */
.modern-table {
    margin: 0;
    border-collapse: separate;
    border-spacing: 0;
    width: 100%;
    font-size: 0.875rem;
}

.modern-table thead th {
    background: var(--admin-table-header-bg, var(--admin-primary, #ff6900)) !important;
    border: none;
    border-bottom: 3px solid rgba(var(--dynamic-primary-rgb, 255, 105, 0), 0.3);
    padding: 1rem 0.75rem;
    font-weight: 600;
    color: var(--admin-table-header-text, #ffffff) !important;
    text-transform: uppercase;
    letter-spacing: 0.025em;
    font-size: 0.75rem;
    position: sticky;
    top: 0;
    z-index: 10;
}

.modern-table thead th:first-child {
    border-top-left-radius: 0.75rem;
}

.modern-table thead th:last-child {
    border-top-right-radius: 0.75rem;
}

.modern-table tbody tr {
    transition: all 0.3s ease;
    border-bottom: 1px solid rgba(0, 0, 0, 0.04);
}

.modern-table tbody tr:hover {
    background: linear-gradient(135deg, #fafbfc 0%, #f8f9fa 100%);
    transform: scale(1.01);
    box-shadow: 0 0.25rem 0.75rem rgba(0, 0, 0, 0.08);
}

.modern-table tbody tr:last-child {
    border-bottom: none;
}

.modern-table tbody td {
    padding: 1rem 0.75rem;
    border: none;
    vertical-align: middle;
    line-height: 1.4;
}

/* Cell Content Styling */
.member-info {
    font-size: 0.8rem;
    line-height: 1.3;
}

.member-info .username {
    font-weight: 600;
    color: var(--admin-text-dark);
    margin-bottom: 0.25rem;
}

.member-info .contact {
    color: var(--admin-text-muted);
    font-size: 0.75rem;
}

.order-amount {
    font-weight: 700;
    color: var(--admin-primary);
    font-size: 0.9rem;
}

.order-id {
    font-family: 'Courier New', monospace;
    font-size: 0.75rem;
    background: rgba(0, 0, 0, 0.05);
    padding: 0.25rem 0.5rem;
    border-radius: 0.375rem;
    word-break: break-all;
}

.wallet-address {
    font-family: 'Courier New', monospace;
    font-size: 0.7rem;
    background: rgba(0, 0, 0, 0.05);
    padding: 0.25rem 0.5rem;
    border-radius: 0.375rem;
    word-break: break-all;
    max-width: 140px;
    overflow: hidden;
    text-overflow: ellipsis;
}

.payment-method {
    font-size: 0.8rem;
    font-weight: 500;
    color: var(--admin-text-dark);
}

.creation-time {
    font-size: 0.75rem;
    color: var(--admin-text-muted);
    white-space: nowrap;
}

.order-description {
    font-size: 0.75rem;
    color: var(--admin-text-muted);
    max-width: 160px;
    overflow: hidden;
    text-overflow: ellipsis;
}

/* Status Badges */
.status-badge {
    font-size: 0.7rem;
    font-weight: 600;
    padding: 0.375rem 0.75rem;
    border-radius: 0.5rem;
    text-transform: uppercase;
    letter-spacing: 0.025em;
    border: none;
}

.status-badge.bg-success {
    background: linear-gradient(135deg, #10b981, #059669) !important;
    box-shadow: 0 0.25rem 0.75rem rgba(16, 185, 129, 0.3);
}

.status-badge.bg-warning {
    background: linear-gradient(135deg, #f59e0b, #d97706) !important;
    box-shadow: 0 0.25rem 0.75rem rgba(245, 158, 11, 0.3);
    color: white !important;
}

.status-badge.bg-danger {
    background: linear-gradient(135deg, #ef4444, #dc2626) !important;
    box-shadow: 0 0.25rem 0.75rem rgba(239, 68, 68, 0.3);
}

.status-badge.bg-info {
    background: linear-gradient(135deg, #06b6d4, #0891b2) !important;
    box-shadow: 0 0.25rem 0.75rem rgba(6, 182, 212, 0.3);
}

/* Pagination Enhancement */
.pagination-enhanced {
    margin-top: 1.5rem;
    padding-top: 1rem;
    border-top: 1px solid rgba(0, 0, 0, 0.04);
}

.pagination-enhanced .pagination {
    margin: 0;
    justify-content: center;
}

.pagination-enhanced .page-link {
    border: 2px solid rgba(0, 0, 0, 0.06);
    border-radius: 0.5rem;
    margin: 0 0.125rem;
    padding: 0.5rem 0.75rem;
    color: var(--admin-text-dark);
    font-weight: 500;
    transition: all 0.3s ease;
}

.pagination-enhanced .page-link:hover {
    background: var(--admin-primary);
    border-color: var(--admin-primary);
    color: white;
    transform: translateY(-1px);
}

.pagination-enhanced .page-item.active .page-link {
    background: linear-gradient(135deg, var(--admin-primary), rgba(var(--admin-primary-rgb), 0.8));
    border-color: var(--admin-primary);
    box-shadow: 0 0.25rem 0.75rem rgba(var(--admin-primary-rgb), 0.3);
}

/* Empty State */
.empty-state {
    text-align: center;
    padding: 3rem 1rem;
    color: var(--admin-text-muted);
}

.empty-state i {
    font-size: 3rem;
    margin-bottom: 1rem;
    opacity: 0.5;
}

.empty-state h5 {
    color: var(--admin-text-dark);
    margin-bottom: 0.5rem;
}

/* Responsive Design */
@media (max-width: 1200px) {
    .modern-table {
        font-size: 0.8rem;
    }
    
    .modern-table thead th,
    .modern-table tbody td {
        padding: 0.75rem 0.5rem;
    }
    
    .wallet-address {
        max-width: 100px;
    }
    
    .order-description {
        max-width: 120px;
    }
}

@media (max-width: 768px) {
    .search-form {
        flex-direction: column;
        gap: 0.75rem;
        width: 100%;
    }
    
    .search-form .form-control {
        width: 100%;
    }
    
    .modern-table {
        font-size: 0.75rem;
    }
    
    .modern-table thead th,
    .modern-table tbody td {
        padding: 0.5rem 0.25rem;
    }
    
    .member-info,
    .order-id,
    .wallet-address,
    .order-description {
        font-size: 0.7rem;
    }
    
    .wallet-address {
        max-width: 80px;
    }
    
    .order-description {
        max-width: 100px;
    }
}

/* Loading States */
.table-loading {
    position: relative;
    opacity: 0.6;
    pointer-events: none;
}

.table-loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 2rem;
    height: 2rem;
    margin: -1rem 0 0 -1rem;
    border: 0.25rem solid #f3f3f3;
    border-top: 0.25rem solid var(--admin-primary);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    z-index: 10;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}
