/**
 * Bamboo User Dashboard CSS
 * Professional PC-optimized styling
 */

/* CSS Variables for dynamic theming */
:root {
    --primary-color: #007bff;
    --secondary-color: #6c757d;
    --primary-rgb: 0, 123, 255;
    --secondary-rgb: 108, 117, 125;
    --success-color: #28a745;
    --warning-color: #ffc107;
    --danger-color: #dc3545;
    --info-color: #17a2b8;
    --light-color: #f8f9fa;
    --dark-color: #343a40;
    --border-radius: 12px;
    --box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    --transition: all 0.3s ease;
}

/* Global Styles */
body {
    background-color: #f5f6fa;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
}

/* Header Styles */
.header {
    background: linear-gradient(135deg, var(--primary-color), rgba(var(--primary-rgb), 0.8));
    color: white;
    padding: 1rem 0;
    box-shadow: var(--box-shadow);
    position: sticky;
    top: 0;
    z-index: 1000;
}

.header-logo {
    height: 40px;
    width: auto;
}

.header-title {
    color: white;
    font-weight: 700;
    font-size: 1.5rem;
}

.user-info {
    color: white;
}

.user-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    overflow: hidden;
    border: 2px solid rgba(255, 255, 255, 0.3);
}

.user-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.avatar-initials {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(255, 255, 255, 0.2);
    color: white;
    font-weight: bold;
    font-size: 1.2rem;
}

.user-name {
    font-weight: 600;
    font-size: 0.9rem;
}

.user-balance {
    font-size: 0.8rem;
    opacity: 0.9;
}

.vip-badge .badge {
    background: linear-gradient(135deg, #ffd700, #ffed4e) !important;
    color: #333 !important;
    font-weight: 600;
    padding: 0.5rem 1rem;
    border-radius: 20px;
}

/* Main Content */
.main-content {
    padding: 2rem 0;
    min-height: calc(100vh - 80px);
}

/* Welcome Card */
.welcome-card {
    background: linear-gradient(135deg, rgba(var(--primary-rgb), 0.1), rgba(var(--primary-rgb), 0.05));
    border: 1px solid rgba(var(--primary-rgb), 0.2);
    border-radius: var(--border-radius);
    padding: 2rem;
    margin-bottom: 1.5rem;
}

.welcome-title {
    color: var(--primary-color);
    font-weight: 700;
    font-size: 1.8rem;
    margin-bottom: 0.5rem;
}

.welcome-subtitle {
    color: var(--secondary-color);
    font-size: 1.1rem;
    margin-bottom: 0;
}

.welcome-stats .stat-item {
    text-align: center;
}

.welcome-stats .stat-value {
    font-size: 2rem;
    font-weight: 700;
    color: var(--primary-color);
}

.welcome-stats .stat-label {
    font-size: 0.9rem;
    color: var(--secondary-color);
}

/* Stat Cards */
.stat-card {
    background: white;
    border-radius: var(--border-radius);
    padding: 1.5rem;
    box-shadow: var(--box-shadow);
    border: 1px solid rgba(0, 0, 0, 0.05);
    transition: var(--transition);
    height: 100%;
    display: flex;
    align-items: center;
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 15px rgba(0, 0, 0, 0.15);
}

.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
    margin-right: 1rem;
    flex-shrink: 0;
}

.stat-content {
    flex: 1;
}

.stat-title {
    font-size: 0.9rem;
    color: var(--secondary-color);
    margin-bottom: 0.5rem;
    font-weight: 500;
}

.stat-value {
    font-size: 1.4rem;
    font-weight: 700;
    color: var(--dark-color);
}

/* Navigation Card */
.navigation-card {
    background: white;
    border-radius: var(--border-radius);
    padding: 2rem;
    box-shadow: var(--box-shadow);
    border: 1px solid rgba(0, 0, 0, 0.05);
}

.card-title {
    color: var(--dark-color);
    font-weight: 700;
    font-size: 1.3rem;
}

.nav-item {
    display: block;
    text-decoration: none;
    color: inherit;
    background: white;
    border: 1px solid rgba(0, 0, 0, 0.1);
    border-radius: var(--border-radius);
    padding: 1.5rem;
    text-align: center;
    transition: var(--transition);
    height: 100%;
}

.nav-item:hover {
    text-decoration: none;
    color: inherit;
    transform: translateY(-3px);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
    border-color: var(--primary-color);
}

.nav-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
    margin: 0 auto 1rem;
    transition: var(--transition);
}

.nav-icon.scale-110 {
    transform: scale(1.1);
}

.nav-title {
    font-weight: 600;
    font-size: 1rem;
    color: var(--dark-color);
    margin-bottom: 0.5rem;
}

.nav-subtitle {
    font-size: 0.85rem;
    color: var(--secondary-color);
    margin-bottom: 0;
}

/* Info Cards */
.info-card {
    background: white;
    border-radius: var(--border-radius);
    padding: 1.5rem;
    box-shadow: var(--box-shadow);
    border: 1px solid rgba(0, 0, 0, 0.05);
    height: 100%;
}

.info-card .card-title {
    color: var(--dark-color);
    font-weight: 600;
    font-size: 1.1rem;
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

/* VIP Details */
.vip-details {
    text-align: center;
}

.vip-level-badge .badge {
    background: linear-gradient(135deg, var(--primary-color), rgba(var(--primary-rgb), 0.8)) !important;
    color: white !important;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 1rem;
}

.vip-benefits {
    text-align: left;
}

.vip-benefits h6 {
    color: var(--dark-color);
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.vip-benefits ul li {
    margin-bottom: 0.5rem;
    font-size: 0.9rem;
}

/* Activity List */
.activity-list {
    max-height: 300px;
    overflow-y: auto;
    scroll-behavior: smooth;
    /* Hide scrollbars */
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none; /* Internet Explorer 10+ */
}

.activity-list::-webkit-scrollbar {
    display: none; /* WebKit */
}

.activity-item {
    display: flex;
    align-items: center;
    padding: 0.75rem 0;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.activity-item:last-child {
    border-bottom: none;
}

.activity-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(var(--primary-rgb), 0.1);
    margin-right: 1rem;
    flex-shrink: 0;
}

.activity-content {
    flex: 1;
}

.activity-title {
    font-weight: 600;
    font-size: 0.9rem;
    color: var(--dark-color);
    margin-bottom: 0.25rem;
}

.activity-time {
    font-size: 0.8rem;
    color: var(--secondary-color);
}

.activity-amount {
    font-weight: 600;
    font-size: 0.9rem;
}

/* Gradient Backgrounds */
.bg-gradient-primary {
    background: linear-gradient(135deg, var(--primary-color), rgba(var(--primary-rgb), 0.8)) !important;
}

/* Responsive Design */
@media (max-width: 768px) {
    .header {
        padding: 0.75rem 0;
    }
    
    .header-title {
        font-size: 1.2rem;
    }
    
    .main-content {
        padding: 1rem 0;
    }
    
    .welcome-card {
        padding: 1.5rem;
    }
    
    .welcome-title {
        font-size: 1.5rem;
    }
    
    .navigation-card {
        padding: 1.5rem;
    }
    
    .nav-item {
        padding: 1rem;
    }
    
    .nav-icon {
        width: 50px;
        height: 50px;
        font-size: 1.3rem;
    }
    
    .stat-card {
        padding: 1rem;
    }
    
    .stat-icon {
        width: 50px;
        height: 50px;
        font-size: 1.3rem;
    }
}

/* Utility Classes */
.text-success {
    color: var(--success-color) !important;
}

.text-warning {
    color: var(--warning-color) !important;
}

.text-danger {
    color: var(--danger-color) !important;
}

.text-info {
    color: var(--info-color) !important;
}

.bg-primary {
    background-color: var(--primary-color) !important;
}

.bg-success {
    background-color: var(--success-color) !important;
}

.bg-warning {
    background-color: var(--warning-color) !important;
}

.bg-danger {
    background-color: var(--danger-color) !important;
}

.bg-info {
    background-color: var(--info-color) !important;
}

.bg-secondary {
    background-color: var(--secondary-color) !important;
}
