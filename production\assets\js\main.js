/**
 * Bamboo Web Application - Main JavaScript
 * Company: Notepadsly
 * Version: 1.0
 */

// Global BambooApp object (initialized in footer.php)
window.BambooApp = window.BambooApp || {};

// Document ready
$(document).ready(function() {
    // Initialize the application
    BambooApp.init();
});

// Main application object
BambooApp = {
    // Configuration
    config: {
        ajaxTimeout: 30000,
        loadingDelay: 300,
        animationDuration: 300
    },
    
    // Initialize application
    init: function() {
        this.setupAjax();
        this.setupEventListeners();
        this.setupFormValidation();
        this.setupMobileFeatures();
        this.hideLoadingSpinner();
        this.autoHideAlerts();
        this.setupCSRFToken();
        this.registerServiceWorker();
    },
    
    // Setup AJAX defaults
    setupAjax: function() {
        $.ajaxSetup({
            timeout: this.config.ajaxTimeout,
            beforeSend: function(xhr) {
                // Add CSRF token to all AJAX requests
                xhr.setRequestHeader('X-CSRF-Token', BambooApp.csrfToken);
            },
            error: function(xhr, status, error) {
                BambooApp.hideLoadingSpinner();
                
                if (status === 'timeout') {
                    BambooApp.showAlert('Request timeout. Please try again.', 'danger');
                } else if (xhr.status === 401) {
                    BambooApp.showAlert('Session expired. Please login again.', 'warning');
                    setTimeout(function() {
                        window.location.href = BambooApp.baseUrl + 'user/login/';
                    }, 2000);
                } else if (xhr.status === 403) {
                    BambooApp.showAlert('Access denied.', 'danger');
                } else if (xhr.status === 500) {
                    BambooApp.showAlert('Server error. Please try again later.', 'danger');
                } else if (status !== 'abort') {
                    BambooApp.showAlert('An error occurred. Please try again.', 'danger');
                }
            }
        });
    },
    
    // Setup event listeners
    setupEventListeners: function() {
        // Handle form submissions with loading states
        $(document).on('submit', 'form[data-ajax="true"]', this.handleAjaxForm);
        
        // Handle confirmation dialogs
        $(document).on('click', '[data-confirm]', this.handleConfirmation);
        
        // Handle loading buttons
        $(document).on('click', '[data-loading-text]', this.handleLoadingButton);
        
        // Handle mobile menu toggle
        $(document).on('click', '.mobile-menu-toggle', this.toggleMobileMenu);
        
        // Handle popup close
        $(document).on('click', '.popup-close, .popup-overlay', this.closePopup);
        
        // Handle back button
        $(document).on('click', '[data-back]', function(e) {
            e.preventDefault();
            history.back();
        });
        
        // Handle external links
        $(document).on('click', 'a[target="_blank"]', function() {
            // Add rel="noopener noreferrer" for security
            $(this).attr('rel', 'noopener noreferrer');
        });
    },
    
    // Setup form validation
    setupFormValidation: function() {
        // Real-time validation
        $(document).on('blur', '.form-control[required]', function() {
            BambooApp.validateField($(this));
        });
        
        // Password confirmation validation
        $(document).on('blur', 'input[name="confirm_password"]', function() {
            BambooApp.validatePasswordConfirmation($(this));
        });
        
        // Phone number formatting
        $(document).on('input', 'input[type="tel"]', function() {
            BambooApp.formatPhoneNumber($(this));
        });
    },
    
    // Setup mobile-specific features
    setupMobileFeatures: function() {
        // Add mobile class if on mobile device
        if (this.isMobile()) {
            $('body').addClass('mobile-device');

            // Add page transition class
            $('main, .main-content').addClass('page-transition');

            // Setup mobile-specific features
            this.setupPullToRefresh();
            this.setupSwipeGestures();
            this.setupHapticFeedback();
            this.setupSmoothScrolling();
        }

        // Handle mobile navigation
        this.setupMobileNavigation();

        // Handle touch events for better mobile experience
        this.setupTouchEvents();

        // Prevent zoom on input focus (iOS)
        if (this.isIOS()) {
            $('input, select, textarea').attr('autocomplete', 'off');

            // Add safe area classes for iOS
            $('body').addClass('ios-device');
            $('.header, .mobile-header').addClass('safe-area-top');
            $('.mobile-nav, .user-footer').addClass('safe-area-bottom');
        }

        // Setup viewport height fix for mobile browsers
        this.setupViewportFix();
    },
    
    // Setup mobile navigation
    setupMobileNavigation: function() {
        // Highlight active navigation item
        const currentPath = window.location.pathname;
        $('.mobile-nav-item').each(function() {
            const href = $(this).attr('href');
            if (currentPath.includes(href)) {
                $(this).addClass('active');
            }
        });
    },
    
    // Setup touch events
    setupTouchEvents: function() {
        // Add touch feedback to buttons
        $(document).on('touchstart', '.btn', function() {
            $(this).addClass('btn-pressed');
        });

        $(document).on('touchend touchcancel', '.btn', function() {
            $(this).removeClass('btn-pressed');
        });

        // Enhanced touch feedback for interactive elements
        $(document).on('touchstart', '.interactive-element, .list-item, .card', function() {
            $(this).addClass('touching');
        });

        $(document).on('touchend touchcancel', '.interactive-element, .list-item, .card', function() {
            $(this).removeClass('touching');
        });

        // Prevent 300ms click delay on mobile
        $(document).on('touchend', 'a, button, .btn, [role="button"]', function(e) {
            if (!this.disabled) {
                e.preventDefault();
                this.click();
            }
        });
    },
    
    // Setup CSRF token refresh
    setupCSRFToken: function() {
        // Refresh CSRF token every 15 minutes
        setInterval(function() {
            BambooApp.refreshCSRFToken();
        }, 15 * 60 * 1000);
    },
    
    // Handle AJAX form submission
    handleAjaxForm: function(e) {
        e.preventDefault();
        
        const $form = $(this);
        const url = $form.attr('action') || window.location.href;
        const method = $form.attr('method') || 'POST';
        const data = $form.serialize();
        
        // Show loading state
        BambooApp.showLoadingSpinner();
        $form.find('button[type="submit"]').prop('disabled', true);
        
        $.ajax({
            url: url,
            method: method,
            data: data,
            dataType: 'json',
            success: function(response) {
                BambooApp.hideLoadingSpinner();
                $form.find('button[type="submit"]').prop('disabled', false);
                
                if (response.success) {
                    if (response.message) {
                        BambooApp.showAlert(response.message, 'success');
                    }
                    
                    if (response.redirect) {
                        setTimeout(function() {
                            window.location.href = response.redirect;
                        }, 1000);
                    }
                    
                    if (response.reload) {
                        setTimeout(function() {
                            window.location.reload();
                        }, 1000);
                    }
                } else {
                    BambooApp.showAlert(response.message || 'An error occurred', 'danger');
                    
                    if (response.errors) {
                        BambooApp.showFormErrors($form, response.errors);
                    }
                }
            },
            error: function() {
                BambooApp.hideLoadingSpinner();
                $form.find('button[type="submit"]').prop('disabled', false);
            }
        });
    },
    
    // Handle confirmation dialogs
    handleConfirmation: function(e) {
        const message = $(this).data('confirm');
        if (!confirm(message)) {
            e.preventDefault();
            return false;
        }
    },
    
    // Handle loading buttons
    handleLoadingButton: function() {
        const $btn = $(this);
        const loadingText = $btn.data('loading-text');
        const originalText = $btn.text();
        
        $btn.text(loadingText).prop('disabled', true);
        
        // Reset after 5 seconds (fallback)
        setTimeout(function() {
            $btn.text(originalText).prop('disabled', false);
        }, 5000);
    },
    
    // Show loading spinner
    showLoadingSpinner: function() {
        $('#loading-spinner').addClass('show');
    },
    
    // Hide loading spinner
    hideLoadingSpinner: function() {
        setTimeout(function() {
            $('#loading-spinner').removeClass('show');
        }, this.config.loadingDelay);
    },
    
    // Show alert message
    showAlert: function(message, type = 'info', duration = 5000) {
        const alertHtml = `
            <div class="alert alert-${type} alert-dismissible fade show" role="alert">
                <i class="bi bi-${this.getAlertIcon(type)} me-2"></i>
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        `;
        
        $('.flash-messages').append(alertHtml);
        
        // Auto-hide after duration
        if (duration > 0) {
            setTimeout(function() {
                $('.flash-messages .alert').last().fadeOut(function() {
                    $(this).remove();
                });
            }, duration);
        }
    },
    
    // Get alert icon based on type
    getAlertIcon: function(type) {
        const icons = {
            'success': 'check-circle-fill',
            'danger': 'exclamation-triangle-fill',
            'warning': 'exclamation-triangle-fill',
            'info': 'info-circle-fill'
        };
        return icons[type] || 'info-circle-fill';
    },
    
    // Auto-hide alerts
    autoHideAlerts: function() {
        setTimeout(function() {
            $('.flash-messages .alert').fadeOut(function() {
                $(this).remove();
            });
        }, 5000);
    },
    
    // Show form errors
    showFormErrors: function($form, errors) {
        // Clear previous errors
        $form.find('.is-invalid').removeClass('is-invalid');
        $form.find('.invalid-feedback').remove();
        
        // Show new errors
        $.each(errors, function(field, message) {
            const $field = $form.find(`[name="${field}"]`);
            if ($field.length) {
                $field.addClass('is-invalid');
                $field.after(`<div class="invalid-feedback">${message}</div>`);
            }
        });
    },
    
    // Validate field
    validateField: function($field) {
        const value = $field.val().trim();
        const fieldName = $field.attr('name');
        let isValid = true;
        let message = '';
        
        // Required validation
        if ($field.prop('required') && !value) {
            isValid = false;
            message = 'This field is required';
        }
        
        // Email validation
        if (isValid && $field.attr('type') === 'email' && value) {
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (!emailRegex.test(value)) {
                isValid = false;
                message = 'Please enter a valid email address';
            }
        }
        
        // Phone validation
        if (isValid && $field.attr('type') === 'tel' && value) {
            const phoneRegex = /^[\+]?[1-9][\d]{0,15}$/;
            if (!phoneRegex.test(value.replace(/\s/g, ''))) {
                isValid = false;
                message = 'Please enter a valid phone number';
            }
        }
        
        // Password validation
        if (isValid && $field.attr('type') === 'password' && fieldName === 'password' && value) {
            if (value.length < 6) {
                isValid = false;
                message = 'Password must be at least 6 characters long';
            }
        }
        
        // Update field state
        if (isValid) {
            $field.removeClass('is-invalid').addClass('is-valid');
            $field.siblings('.invalid-feedback').remove();
        } else {
            $field.removeClass('is-valid').addClass('is-invalid');
            $field.siblings('.invalid-feedback').remove();
            $field.after(`<div class="invalid-feedback">${message}</div>`);
        }
        
        return isValid;
    },
    
    // Validate password confirmation
    validatePasswordConfirmation: function($field) {
        const password = $('input[name="password"]').val();
        const confirmPassword = $field.val();
        
        if (confirmPassword && password !== confirmPassword) {
            $field.removeClass('is-valid').addClass('is-invalid');
            $field.siblings('.invalid-feedback').remove();
            $field.after('<div class="invalid-feedback">Passwords do not match</div>');
            return false;
        } else if (confirmPassword) {
            $field.removeClass('is-invalid').addClass('is-valid');
            $field.siblings('.invalid-feedback').remove();
            return true;
        }
    },
    
    // Format phone number
    formatPhoneNumber: function($field) {
        let value = $field.val().replace(/\D/g, '');
        
        // Basic formatting for display
        if (value.length >= 10) {
            value = value.replace(/(\d{3})(\d{3})(\d{4})/, '$1-$2-$3');
        }
        
        $field.val(value);
    },
    
    // Close popup
    closePopup: function(e) {
        if ($(e.target).hasClass('popup-close') || $(e.target).hasClass('popup-overlay')) {
            $('.welcome-popup, .popup-overlay').fadeOut();
        }
    },
    
    // Toggle mobile menu
    toggleMobileMenu: function() {
        $('.mobile-nav').toggleClass('show');
    },
    
    // Refresh CSRF token
    refreshCSRFToken: function() {
        $.get(BambooApp.baseUrl + 'api/csrf-token.php', function(response) {
            if (response.success) {
                BambooApp.csrfToken = response.token;
                $('meta[name="csrf-token"]').attr('content', response.token);
            }
        });
    },
    
    // Utility functions
    isMobile: function() {
        return window.innerWidth <= 768 || /Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
    },

    // Setup Pull-to-Refresh functionality
    setupPullToRefresh: function() {
        let startY = 0;
        let currentY = 0;
        let pulling = false;
        let refreshing = false;

        const pullToRefreshContainer = $('.main-content, main');
        if (pullToRefreshContainer.length === 0) return;

        pullToRefreshContainer.addClass('pull-to-refresh');
        pullToRefreshContainer.prepend('<div class="pull-to-refresh-indicator"><i class="bi bi-arrow-clockwise"></i></div>');

        const indicator = $('.pull-to-refresh-indicator');

        pullToRefreshContainer.on('touchstart', function(e) {
            if (window.scrollY === 0 && !refreshing) {
                startY = e.touches[0].clientY;
                pulling = false;
            }
        });

        pullToRefreshContainer.on('touchmove', function(e) {
            if (window.scrollY === 0 && !refreshing) {
                currentY = e.touches[0].clientY;
                const pullDistance = currentY - startY;

                if (pullDistance > 0) {
                    e.preventDefault();

                    if (pullDistance > 60) {
                        if (!pulling) {
                            pulling = true;
                            pullToRefreshContainer.addClass('pulling');
                            BambooApp.triggerHaptic('light');
                        }
                    } else {
                        pulling = false;
                        pullToRefreshContainer.removeClass('pulling');
                    }
                }
            }
        });

        pullToRefreshContainer.on('touchend', function() {
            if (pulling && !refreshing) {
                refreshing = true;
                pullToRefreshContainer.addClass('refreshing');
                BambooApp.triggerHaptic('medium');

                // Simulate refresh (reload page after 1 second)
                setTimeout(function() {
                    window.location.reload();
                }, 1000);
            } else {
                pullToRefreshContainer.removeClass('pulling');
            }
        });
    },

    // Setup Swipe Gestures
    setupSwipeGestures: function() {
        let startX = 0;
        let startY = 0;
        let currentX = 0;
        let currentY = 0;

        $(document).on('touchstart', '.swipeable', function(e) {
            startX = e.touches[0].clientX;
            startY = e.touches[0].clientY;
        });

        $(document).on('touchmove', '.swipeable', function(e) {
            if (!startX || !startY) return;

            currentX = e.touches[0].clientX;
            currentY = e.touches[0].clientY;

            const diffX = startX - currentX;
            const diffY = startY - currentY;

            // Only handle horizontal swipes
            if (Math.abs(diffX) > Math.abs(diffY)) {
                e.preventDefault();

                if (diffX > 50) {
                    // Swipe left - show actions
                    $(this).addClass('swiped');
                } else if (diffX < -50) {
                    // Swipe right - hide actions
                    $(this).removeClass('swiped');
                }
            }
        });

        $(document).on('touchend', '.swipeable', function() {
            startX = 0;
            startY = 0;
        });
    },

    // Setup Haptic Feedback Simulation
    setupHapticFeedback: function() {
        // Add haptic classes to interactive elements
        $('.btn-primary, .btn-success').addClass('haptic-medium');
        $('.btn-secondary, .btn-outline-primary').addClass('haptic-light');
        $('.btn-danger, .btn-warning').addClass('haptic-heavy');
        $('.footer-nav-item, .nav-item').addClass('haptic-light');
    },

    // Trigger Haptic Feedback
    triggerHaptic: function(intensity = 'light') {
        // Use Web Vibration API if available
        if ('vibrate' in navigator) {
            switch (intensity) {
                case 'light':
                    navigator.vibrate(10);
                    break;
                case 'medium':
                    navigator.vibrate(20);
                    break;
                case 'heavy':
                    navigator.vibrate([30, 10, 30]);
                    break;
            }
        }
    },

    // Setup Smooth Scrolling
    setupSmoothScrolling: function() {
        // Add smooth scroll class to scrollable containers
        $('.main-content, main, .modal-body').addClass('smooth-scroll hide-scrollbar');

        // Smooth scroll to top functionality
        $(document).on('click', '[data-scroll-top]', function(e) {
            e.preventDefault();
            window.scrollTo({
                top: 0,
                behavior: 'smooth'
            });
        });
    },

    // Setup Viewport Height Fix for Mobile Browsers
    setupViewportFix: function() {
        // Fix for mobile browsers that change viewport height when address bar shows/hides
        const setViewportHeight = () => {
            const vh = window.innerHeight * 0.01;
            document.documentElement.style.setProperty('--vh', `${vh}px`);
        };

        setViewportHeight();
        window.addEventListener('resize', setViewportHeight);
        window.addEventListener('orientationchange', setViewportHeight);
    },

    isIOS: function() {
        return /iPad|iPhone|iPod/.test(navigator.userAgent);
    },
    
    // Format currency
    formatCurrency: function(amount, currency = null) {
        if (currency === null) {
            currency = BambooApp.defaultCurrency || 'USDT';
        }
        const decimalPlaces = BambooApp.decimalPlaces || 2;
        return currency + ' ' + parseFloat(amount).toFixed(decimalPlaces);
    },
    
    // Format date
    formatDate: function(date, format = 'YYYY-MM-DD HH:mm:ss') {
        // Simple date formatting - you might want to use a library like moment.js for more complex formatting
        const d = new Date(date);
        return d.toLocaleString();
    },
    
    // Copy to clipboard
    copyToClipboard: function(text) {
        if (navigator.clipboard) {
            navigator.clipboard.writeText(text).then(function() {
                BambooApp.showAlert('Copied to clipboard!', 'success', 2000);
            });
        } else {
            // Fallback for older browsers
            const textArea = document.createElement('textarea');
            textArea.value = text;
            document.body.appendChild(textArea);
            textArea.select();
            document.execCommand('copy');
            document.body.removeChild(textArea);
            BambooApp.showAlert('Copied to clipboard!', 'success', 2000);
        }
    },

    // Register Service Worker for PWA functionality
    registerServiceWorker: function() {
        if ('serviceWorker' in navigator) {
            window.addEventListener('load', function() {
                navigator.serviceWorker.register('/Bamboo/sw.js')
                    .then(function(registration) {
                        console.log('Service Worker: Registered successfully', registration.scope);

                        // Check for updates
                        registration.addEventListener('updatefound', function() {
                            const newWorker = registration.installing;
                            newWorker.addEventListener('statechange', function() {
                                if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {
                                    // New content is available, prompt user to refresh
                                    if (confirm('New version available! Refresh to update?')) {
                                        window.location.reload();
                                    }
                                }
                            });
                        });
                    })
                    .catch(function(error) {
                        console.log('Service Worker: Registration failed', error);
                    });
            });
        } else {
            console.log('Service Worker: Not supported');
        }
    }
};

// Add CSS for button pressed state
const mainStyle = document.createElement('style');
mainStyle.textContent = `
    .btn-pressed {
        transform: scale(0.95);
        opacity: 0.8;
    }
`;
document.head.appendChild(mainStyle);