<?php
/**
 * Bamboo Web Application - Universal Error Handler
 * Company: Notepadsly
 * Version: 1.0
 */

// Prevent direct access
if (!defined('BAMBOO_APP')) {
    die('Direct access not permitted');
}

class ErrorHandler {
    private static $instance = null;
    private $errors = [];
    private $logFile;
    
    private function __construct() {
        $this->logFile = dirname(__DIR__) . '/logs/error.log';
        $this->setupErrorHandling();
    }
    
    public static function getInstance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    private function setupErrorHandling() {
        // Set error reporting based on environment
        $debug_mode = defined('DEBUG_MODE') ? DEBUG_MODE : true;
        
        if ($debug_mode) {
            error_reporting(E_ALL);
            ini_set('display_errors', 1);
            ini_set('display_startup_errors', 1);
        } else {
            error_reporting(E_ALL);
            ini_set('display_errors', 0);
            ini_set('log_errors', 1);
            ini_set('error_log', $this->logFile);
        }
        
        // Set custom error handlers
        set_error_handler([$this, 'handleError']);
        set_exception_handler([$this, 'handleException']);
        register_shutdown_function([$this, 'handleFatalError']);
    }
    
    public function handleError($severity, $message, $file, $line) {
        $error = [
            'type' => 'PHP Error',
            'severity' => $this->getSeverityName($severity),
            'message' => $message,
            'file' => $file,
            'line' => $line,
            'timestamp' => date('Y-m-d H:i:s'),
            'url' => $_SERVER['REQUEST_URI'] ?? 'CLI',
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'Unknown'
        ];
        
        $this->logError($error);
        
        $debug_mode = defined('DEBUG_MODE') ? DEBUG_MODE : true;
        if ($debug_mode) {
            $this->displayError($error);
        }
        
        return true;
    }
    
    public function handleException($exception) {
        $error = [
            'type' => 'Exception',
            'severity' => 'Fatal',
            'message' => $exception->getMessage(),
            'file' => $exception->getFile(),
            'line' => $exception->getLine(),
            'trace' => $exception->getTraceAsString(),
            'timestamp' => date('Y-m-d H:i:s'),
            'url' => $_SERVER['REQUEST_URI'] ?? 'CLI',
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'Unknown'
        ];
        
        $this->logError($error);
        
        $debug_mode = defined('DEBUG_MODE') ? DEBUG_MODE : true;
        if ($debug_mode) {
            $this->displayException($error);
        } else {
            $this->displayFriendlyError();
        }
    }
    
    public function handleFatalError() {
        $error = error_get_last();
        
        if ($error !== null && in_array($error['type'], [E_ERROR, E_PARSE, E_CORE_ERROR, E_COMPILE_ERROR])) {
            $errorData = [
                'type' => 'Fatal Error',
                'severity' => 'Fatal',
                'message' => $error['message'],
                'file' => $error['file'],
                'line' => $error['line'],
                'timestamp' => date('Y-m-d H:i:s'),
                'url' => $_SERVER['REQUEST_URI'] ?? 'CLI',
                'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'Unknown'
            ];
            
            $this->logError($errorData);
            
            $debug_mode = defined('DEBUG_MODE') ? DEBUG_MODE : true;
            if ($debug_mode) {
                $this->displayError($errorData);
            } else {
                $this->displayFriendlyError();
            }
        }
    }
    
    private function getSeverityName($severity) {
        $severities = [
            E_ERROR => 'Fatal Error',
            E_WARNING => 'Warning',
            E_PARSE => 'Parse Error',
            E_NOTICE => 'Notice',
            E_CORE_ERROR => 'Core Error',
            E_CORE_WARNING => 'Core Warning',
            E_COMPILE_ERROR => 'Compile Error',
            E_COMPILE_WARNING => 'Compile Warning',
            E_USER_ERROR => 'User Error',
            E_USER_WARNING => 'User Warning',
            E_USER_NOTICE => 'User Notice',
            E_STRICT => 'Strict Standards',
            E_RECOVERABLE_ERROR => 'Recoverable Error',
            E_DEPRECATED => 'Deprecated',
            E_USER_DEPRECATED => 'User Deprecated'
        ];
        
        return $severities[$severity] ?? 'Unknown Error';
    }
    
    private function logError($error) {
        $logMessage = sprintf(
            "[%s] %s: %s in %s on line %d\n",
            $error['timestamp'],
            $error['type'],
            $error['message'],
            $error['file'],
            $error['line']
        );
        
        if (isset($error['trace'])) {
            $logMessage .= "Stack trace:\n" . $error['trace'] . "\n";
        }
        
        $logMessage .= "URL: " . $error['url'] . "\n";
        $logMessage .= "User Agent: " . $error['user_agent'] . "\n";
        $logMessage .= str_repeat('-', 80) . "\n";
        
        error_log($logMessage, 3, $this->logFile);
    }
    
    private function displayError($error) {
        if (headers_sent()) {
            echo $this->getErrorHtml($error);
        } else {
            http_response_code(500);
            header('Content-Type: text/html; charset=UTF-8');
            echo $this->getErrorPage($error);
        }
    }
    
    private function displayException($error) {
        if (headers_sent()) {
            echo $this->getExceptionHtml($error);
        } else {
            http_response_code(500);
            header('Content-Type: text/html; charset=UTF-8');
            echo $this->getExceptionPage($error);
        }
    }
    
    private function displayFriendlyError() {
        if (!headers_sent()) {
            http_response_code(500);
            header('Content-Type: text/html; charset=UTF-8');
        }
        
        echo $this->getFriendlyErrorPage();
    }
    
    private function getErrorHtml($error) {
        return sprintf(
            '<div style="background: #ff6b6b; color: white; padding: 15px; margin: 10px 0; border-radius: 5px; font-family: monospace;">
                <strong>%s (%s)</strong><br>
                <strong>Message:</strong> %s<br>
                <strong>File:</strong> %s<br>
                <strong>Line:</strong> %d<br>
                <strong>Time:</strong> %s
            </div>',
            htmlspecialchars($error['type']),
            htmlspecialchars($error['severity']),
            htmlspecialchars($error['message']),
            htmlspecialchars($error['file']),
            $error['line'],
            $error['timestamp']
        );
    }
    
    private function getExceptionHtml($error) {
        return sprintf(
            '<div style="background: #ff4757; color: white; padding: 15px; margin: 10px 0; border-radius: 5px; font-family: monospace;">
                <strong>%s (%s)</strong><br>
                <strong>Message:</strong> %s<br>
                <strong>File:</strong> %s<br>
                <strong>Line:</strong> %d<br>
                <strong>Time:</strong> %s<br>
                <details style="margin-top: 10px;">
                    <summary>Stack Trace</summary>
                    <pre style="background: rgba(0,0,0,0.2); padding: 10px; margin-top: 5px; border-radius: 3px; overflow: auto;">%s</pre>
                </details>
            </div>',
            htmlspecialchars($error['type']),
            htmlspecialchars($error['severity']),
            htmlspecialchars($error['message']),
            htmlspecialchars($error['file']),
            $error['line'],
            $error['timestamp'],
            htmlspecialchars($error['trace'])
        );
    }
    
    private function getErrorPage($error) {
        return '<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Error - Bamboo</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body { background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%); min-height: 100vh; font-family: monospace; }
        .error-container { max-width: 800px; margin: 2rem auto; padding: 2rem; }
        .error-card { background: rgba(255,255,255,0.95); border-radius: 1rem; padding: 2rem; box-shadow: 0 20px 40px rgba(0,0,0,0.1); }
        .error-icon { font-size: 4rem; color: #ff6b6b; }
        .code-block { background: #f8f9fa; padding: 1rem; border-radius: 0.5rem; border-left: 4px solid #ff6b6b; }
    </style>
</head>
<body>
    <div class="container error-container">
        <div class="error-card">
            <div class="text-center mb-4">
                <div class="error-icon">⚠️</div>
                <h1 class="text-danger">Application Error</h1>
                <p class="text-muted">An error occurred while processing your request</p>
            </div>
            
            <div class="code-block">
                <h5>' . htmlspecialchars($error['type']) . ' (' . htmlspecialchars($error['severity']) . ')</h5>
                <p><strong>Message:</strong> ' . htmlspecialchars($error['message']) . '</p>
                <p><strong>File:</strong> ' . htmlspecialchars($error['file']) . '</p>
                <p><strong>Line:</strong> ' . $error['line'] . '</p>
                <p><strong>Time:</strong> ' . $error['timestamp'] . '</p>
            </div>
            
            <div class="mt-4 text-center">
                <a href="javascript:history.back()" class="btn btn-outline-primary">Go Back</a>
                <a href="' . (defined('BASE_URL') ? BASE_URL : '/') . '" class="btn btn-primary">Home</a>
            </div>
        </div>
    </div>
</body>
</html>';
    }
    
    private function getExceptionPage($error) {
        return '<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Exception - Bamboo</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body { background: linear-gradient(135deg, #ff4757 0%, #c44569 100%); min-height: 100vh; font-family: monospace; }
        .error-container { max-width: 900px; margin: 2rem auto; padding: 2rem; }
        .error-card { background: rgba(255,255,255,0.95); border-radius: 1rem; padding: 2rem; box-shadow: 0 20px 40px rgba(0,0,0,0.1); }
        .error-icon { font-size: 4rem; color: #ff4757; }
        .code-block { background: #f8f9fa; padding: 1rem; border-radius: 0.5rem; border-left: 4px solid #ff4757; }
        .trace-block { background: #2f3542; color: #f1f2f6; padding: 1rem; border-radius: 0.5rem; max-height: 400px; overflow: auto; }
    </style>
</head>
<body>
    <div class="container error-container">
        <div class="error-card">
            <div class="text-center mb-4">
                <div class="error-icon">💥</div>
                <h1 class="text-danger">Unhandled Exception</h1>
                <p class="text-muted">An exception was thrown and not caught</p>
            </div>
            
            <div class="code-block">
                <h5>' . htmlspecialchars($error['type']) . ' (' . htmlspecialchars($error['severity']) . ')</h5>
                <p><strong>Message:</strong> ' . htmlspecialchars($error['message']) . '</p>
                <p><strong>File:</strong> ' . htmlspecialchars($error['file']) . '</p>
                <p><strong>Line:</strong> ' . $error['line'] . '</p>
                <p><strong>Time:</strong> ' . $error['timestamp'] . '</p>
            </div>
            
            <div class="mt-3">
                <h6>Stack Trace:</h6>
                <div class="trace-block">
                    <pre>' . htmlspecialchars($error['trace']) . '</pre>
                </div>
            </div>
            
            <div class="mt-4 text-center">
                <a href="javascript:history.back()" class="btn btn-outline-primary">Go Back</a>
                <a href="' . (defined('BASE_URL') ? BASE_URL : '/') . '" class="btn btn-primary">Home</a>
            </div>
        </div>
    </div>
</body>
</html>';
    }
    
    private function getFriendlyErrorPage() {
        return '<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Error - Bamboo</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body { background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 50%, #ff6900 100%); min-height: 100vh; }
        .error-container { max-width: 600px; margin: 2rem auto; padding: 2rem; }
        .error-card { background: rgba(255,255,255,0.95); border-radius: 1rem; padding: 2rem; box-shadow: 0 20px 40px rgba(0,0,0,0.1); }
    </style>
</head>
<body>
    <div class="container error-container">
        <div class="error-card text-center">
            <div style="font-size: 4rem; color: #ff6900; margin-bottom: 1rem;">😔</div>
            <h1 class="text-primary">Oops! Something went wrong</h1>
            <p class="text-muted">We encountered an unexpected error. Our team has been notified and is working to fix it.</p>
            
            <div class="mt-4">
                <a href="javascript:history.back()" class="btn btn-outline-primary me-2">Go Back</a>
                <a href="' . (defined('BASE_URL') ? BASE_URL : '/') . '" class="btn btn-primary">Home</a>
            </div>
            
            <div class="mt-4">
                <small class="text-muted">
                    Error ID: ' . uniqid() . '<br>
                    Time: ' . date('Y-m-d H:i:s') . '
                </small>
            </div>
        </div>
    </div>
</body>
</html>';
    }
    
    public function checkFileExists($file, $description = 'File') {
        if (!file_exists($file)) {
            throw new Exception("$description not found: $file");
        }
        return true;
    }
    
    public function checkDatabaseConnection() {
        try {
            $db = getDB();
            return true;
        } catch (Exception $e) {
            throw new Exception("Database connection failed: " . $e->getMessage());
        }
    }
    
    public function checkTableExists($table) {
        try {
            $db = getDB();
            $stmt = $db->query("SHOW TABLES LIKE '$table'");
            if ($stmt->rowCount() === 0) {
                throw new Exception("Database table '$table' does not exist");
            }
            return true;
        } catch (Exception $e) {
            throw new Exception("Table check failed: " . $e->getMessage());
        }
    }
}

// Initialize error handler
if (!isset($GLOBALS['error_handler'])) {
    $GLOBALS['error_handler'] = ErrorHandler::getInstance();
}

// Helper functions
function checkFile($file, $description = 'File') {
    return $GLOBALS['error_handler']->checkFileExists($file, $description);
}

function checkDatabase() {
    return $GLOBALS['error_handler']->checkDatabaseConnection();
}

function checkTable($table) {
    return $GLOBALS['error_handler']->checkTableExists($table);
}
?>