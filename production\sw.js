/**
 * Bamboo Service Worker
 * Provides offline functionality and performance optimizations
 */

const CACHE_NAME = 'bamboo-v1.0.0';
const STATIC_CACHE = 'bamboo-static-v1.0.0';
const DYNAMIC_CACHE = 'bamboo-dynamic-v1.0.0';

// Files to cache for offline functionality
const STATIC_FILES = [
  '/Bamboo/',
  '/Bamboo/user/dashboard/dashboard.php',
  '/Bamboo/assets/css/main.css',
  '/Bamboo/assets/css/mobile-optimizations.css',
  '/Bamboo/assets/css/user-dashboard.css',
  '/Bamboo/assets/js/main.js',
  '/Bamboo/assets/js/jquery-3.6.0.min.js',
  '/Bamboo/assets/images/favicon.ico',
  'https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css',
  'https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js',
  'https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css'
];

// Install event - cache static files
self.addEventListener('install', event => {
  console.log('Service Worker: Installing...');
  
  event.waitUntil(
    caches.open(STATIC_CACHE)
      .then(cache => {
        console.log('Service Worker: Caching static files');
        return cache.addAll(STATIC_FILES);
      })
      .then(() => {
        console.log('Service Worker: Static files cached');
        return self.skipWaiting();
      })
      .catch(error => {
        console.error('Service Worker: Error caching static files', error);
      })
  );
});

// Activate event - clean up old caches
self.addEventListener('activate', event => {
  console.log('Service Worker: Activating...');
  
  event.waitUntil(
    caches.keys()
      .then(cacheNames => {
        return Promise.all(
          cacheNames.map(cacheName => {
            if (cacheName !== STATIC_CACHE && cacheName !== DYNAMIC_CACHE) {
              console.log('Service Worker: Deleting old cache', cacheName);
              return caches.delete(cacheName);
            }
          })
        );
      })
      .then(() => {
        console.log('Service Worker: Activated');
        return self.clients.claim();
      })
  );
});

// Fetch event - serve cached files or fetch from network
self.addEventListener('fetch', event => {
  const { request } = event;
  const url = new URL(request.url);
  
  // Skip non-GET requests
  if (request.method !== 'GET') {
    return;
  }
  
  // Skip chrome-extension and other non-http requests
  if (!url.protocol.startsWith('http')) {
    return;
  }
  
  event.respondWith(
    caches.match(request)
      .then(cachedResponse => {
        // Return cached version if available
        if (cachedResponse) {
          console.log('Service Worker: Serving from cache', request.url);
          return cachedResponse;
        }
        
        // Fetch from network and cache dynamic content
        return fetch(request)
          .then(networkResponse => {
            // Don't cache if not a valid response
            if (!networkResponse || networkResponse.status !== 200 || networkResponse.type !== 'basic') {
              return networkResponse;
            }
            
            // Clone the response
            const responseToCache = networkResponse.clone();
            
            // Cache dynamic content (excluding API calls and admin pages)
            if (!request.url.includes('/api/') && !request.url.includes('/admin/')) {
              caches.open(DYNAMIC_CACHE)
                .then(cache => {
                  console.log('Service Worker: Caching dynamic content', request.url);
                  cache.put(request, responseToCache);
                });
            }
            
            return networkResponse;
          })
          .catch(error => {
            console.log('Service Worker: Network fetch failed', error);
            
            // Return offline page for navigation requests
            if (request.destination === 'document') {
              return caches.match('/Bamboo/offline.html');
            }
            
            // Return cached fallback for other requests
            return caches.match(request);
          });
      })
  );
});

// Background sync for offline form submissions
self.addEventListener('sync', event => {
  console.log('Service Worker: Background sync triggered', event.tag);
  
  if (event.tag === 'background-sync') {
    event.waitUntil(
      // Handle offline form submissions
      handleOfflineSubmissions()
    );
  }
});

// Push notification handling
self.addEventListener('push', event => {
  console.log('Service Worker: Push notification received');
  
  const options = {
    body: event.data ? event.data.text() : 'New notification from Bamboo',
    icon: '/Bamboo/assets/images/icon-192.png',
    badge: '/Bamboo/assets/images/favicon.ico',
    vibrate: [200, 100, 200],
    data: {
      dateOfArrival: Date.now(),
      primaryKey: 1
    },
    actions: [
      {
        action: 'explore',
        title: 'Open App',
        icon: '/Bamboo/assets/images/icon-192.png'
      },
      {
        action: 'close',
        title: 'Close',
        icon: '/Bamboo/assets/images/icon-192.png'
      }
    ]
  };
  
  event.waitUntil(
    self.registration.showNotification('Bamboo', options)
  );
});

// Notification click handling
self.addEventListener('notificationclick', event => {
  console.log('Service Worker: Notification clicked');
  
  event.notification.close();
  
  if (event.action === 'explore') {
    event.waitUntil(
      clients.openWindow('/Bamboo/user/dashboard/dashboard.php')
    );
  }
});

// Handle offline form submissions
async function handleOfflineSubmissions() {
  try {
    const cache = await caches.open('offline-submissions');
    const requests = await cache.keys();
    
    for (const request of requests) {
      try {
        const response = await fetch(request);
        if (response.ok) {
          await cache.delete(request);
          console.log('Service Worker: Offline submission synced', request.url);
        }
      } catch (error) {
        console.log('Service Worker: Failed to sync offline submission', error);
      }
    }
  } catch (error) {
    console.error('Service Worker: Error handling offline submissions', error);
  }
}

// Message handling for communication with main thread
self.addEventListener('message', event => {
  console.log('Service Worker: Message received', event.data);
  
  if (event.data && event.data.type === 'SKIP_WAITING') {
    self.skipWaiting();
  }
  
  if (event.data && event.data.type === 'CACHE_URLS') {
    event.waitUntil(
      caches.open(DYNAMIC_CACHE)
        .then(cache => cache.addAll(event.data.payload))
    );
  }
});

console.log('Service Worker: Loaded successfully');
