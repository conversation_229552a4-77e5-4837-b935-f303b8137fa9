<?php
/**
 * Bamboo Web Application - User Dashboard
 * Company: Notepadsly
 * Version: 1.0
 */

// Define app constant
define('BAMBOO_APP', true);

// Include required files
require_once '../../includes/config.php';
require_once '../../includes/functions.php';

// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Check if user is logged in
if (!isLoggedIn()) {
    redirect('user/login/login.php');
}

// Get user information
$user_id = $_SESSION['user_id'];
$user = fetchRow("SELECT * FROM users WHERE id = ?", [$user_id]);

if (!$user) {
    session_destroy();
    redirect('user/login/login.php');
}

// Get VIP level information
$vip_level = fetchRow("SELECT * FROM vip_levels WHERE level = ?", [$user['vip_level']]);

// Get user statistics
$today = date('Y-m-d');
$tasks_completed_today = getRecordCount('tasks', 'user_id = ? AND DATE(completed_at) = ? AND status = "completed"', [$user_id, $today]);
$total_tasks_completed = getRecordCount('tasks', 'user_id = ? AND status = "completed"', [$user_id]);

// Calculate today's profit
$today_profit = fetchValue("SELECT COALESCE(SUM(commission_earned), 0) FROM tasks WHERE user_id = ? AND DATE(completed_at) = ? AND status = 'completed'", [$user_id, $today]);

// Get recent transactions
$recent_transactions = fetchAll("SELECT * FROM transactions WHERE user_id = ? ORDER BY created_at DESC LIMIT 5", [$user_id]);

// Get app settings
$app_name = getAppSetting('app_name', 'Bamboo');
$app_logo = getAppSetting('app_logo', '');
$usdt_multiplier = getAppSetting('usdt_multiplier', '1');
$primary_color = getAppSetting('primary_color', '#007bff');
$secondary_color = getAppSetting('secondary_color', '#6c757d');

// Check for welcome popup (first login today)
$show_welcome_popup = false;
$last_login_date = date('Y-m-d', strtotime($user['last_login'] ?? ''));
if ($last_login_date !== $today) {
    $show_welcome_popup = true;
    // Update last login date
    updateRecord('users', ['last_login' => date('Y-m-d H:i:s')], 'id = ?', [$user_id]);
}

// Get notifications
$notifications = fetchAll("SELECT * FROM notifications WHERE (target_user_id IS NULL OR target_user_id = ?) AND (target_vip_level IS NULL OR target_vip_level = ?) AND status = 'active' AND (start_date IS NULL OR start_date <= NOW()) AND (end_date IS NULL OR end_date >= NOW()) ORDER BY created_at DESC", [$user_id, $user['vip_level']]);

// Get banner notification
$banner_notification = fetchRow("SELECT * FROM notifications WHERE is_banner = 1 AND status = 'active' AND (start_date IS NULL OR start_date <= NOW()) AND (end_date IS NULL OR end_date >= NOW()) ORDER BY created_at DESC LIMIT 1");
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <title>Dashboard - <?php echo htmlspecialchars($app_name); ?></title>

    <!-- Favicon -->
    <?php
    $favicon_path = ASSETS_URL . 'images/favicon.ico';
    $favicon_exists = file_exists(__DIR__ . '/../../assets/images/favicon.ico') && filesize(__DIR__ . '/../../assets/images/favicon.ico') > 100;
    ?>
    <?php if ($favicon_exists): ?>
        <link rel="icon" type="image/x-icon" href="<?php echo $favicon_path; ?>?v=<?php echo filemtime(__DIR__ . '/../../assets/images/favicon.ico'); ?>">
    <?php else: ?>
        <link rel="icon" type="image/svg+xml" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32'><rect width='32' height='32' fill='<?php echo urlencode($primary_color); ?>'/><text x='16' y='20' font-family='Arial' font-size='18' fill='white' text-anchor='middle'>B</text></svg>">
    <?php endif; ?>

    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    
    <!-- Custom CSS -->
    <link href="<?php echo BASE_URL; ?>assets/css/user-dashboard.css" rel="stylesheet">
    
    <style>
        :root {
            --primary-color: <?php echo $primary_color; ?>;
            --secondary-color: <?php echo $secondary_color; ?>;
            --primary-rgb: <?php echo implode(',', sscanf($primary_color, "#%02x%02x%02x")); ?>;
            --secondary-rgb: <?php echo implode(',', sscanf($secondary_color, "#%02x%02x%02x")); ?>;
        }
    </style>
</head>
<body>
    <!-- Welcome Popup -->
    <?php if ($show_welcome_popup): ?>
    <div class="modal fade" id="welcomeModal" tabindex="-1" aria-labelledby="welcomeModalLabel" aria-hidden="true" data-bs-backdrop="static" data-bs-keyboard="false">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header border-0 text-center">
                    <h5 class="modal-title w-100" id="welcomeModalLabel">
                        <i class="bi bi-gift text-warning fs-1"></i>
                    </h5>
                </div>
                <div class="modal-body text-center">
                    <h4 class="text-primary mb-3">Welcome to <?php echo htmlspecialchars($app_name); ?>!</h4>
                    <div class="mb-4">
                        <div class="display-4 text-success fw-bold">USDT <?php echo $usdt_multiplier; ?>X</div>
                        <p class="text-muted">Anniversary celebration multiplier</p>
                    </div>
                    <p class="mb-4">Thank you for being part of our community. Enjoy enhanced rewards during our anniversary celebration!</p>
                </div>
                <div class="modal-footer border-0 justify-content-center">
                    <button type="button" class="btn btn-primary btn-lg px-5" data-bs-dismiss="modal">
                        <i class="bi bi-check-circle me-2"></i>Get Started
                    </button>
                </div>
            </div>
        </div>
    </div>
    <?php endif; ?>

    <!-- Header -->
    <header class="header">
        <div class="container-fluid">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <div class="d-flex align-items-center">
                        <?php
                        // Logo path construction (same as user header)
                        $logo_path = '';
                        if ($app_logo) {
                            if (strpos($app_logo, 'http') === 0) {
                                $logo_path = $app_logo;
                            } else {
                                $logo_path = BASE_URL . 'uploads/logos/' . $app_logo;
                            }

                            // Verify file exists
                            $full_logo_path = __DIR__ . '/../../uploads/logos/' . $app_logo;
                            if (!file_exists($full_logo_path)) {
                                $logo_path = '';
                            }
                        }
                        $file_extension = $app_logo ? strtolower(pathinfo($app_logo, PATHINFO_EXTENSION)) : '';
                        ?>

                        <?php if ($logo_path): ?>
                            <!-- Show logo only (no text) -->
                            <?php if ($file_extension === 'svg'): ?>
                                <div class="header-logo-svg">
                                    <object data="<?php echo $logo_path; ?>" type="image/svg+xml" class="header-logo" style="height: 40px; width: auto;">
                                        <img src="<?php echo $logo_path; ?>" alt="<?php echo htmlspecialchars($app_name); ?>" class="header-logo" style="height: 40px; width: auto;">
                                    </object>
                                </div>
                            <?php else: ?>
                                <img src="<?php echo $logo_path; ?>" alt="<?php echo htmlspecialchars($app_name); ?>" class="header-logo" style="height: 40px; width: auto;">
                            <?php endif; ?>
                        <?php else: ?>
                            <!-- Show text name only (no logo) -->
                            <h1 class="header-title mb-0"><?php echo htmlspecialchars($app_name); ?></h1>
                        <?php endif; ?>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="d-flex align-items-center justify-content-end">
                        <!-- User Info -->
                        <div class="user-info me-3">
                            <div class="d-flex align-items-center">
                                <div class="user-avatar me-2">
                                    <?php if ($user['avatar_url']): ?>
                                        <img src="<?php echo BASE_URL . $user['avatar_url']; ?>" alt="Avatar" class="rounded-circle">
                                    <?php else: ?>
                                        <div class="avatar-initials">
                                            <?php echo strtoupper(substr($user['username'], 0, 1)); ?>
                                        </div>
                                    <?php endif; ?>
                                </div>
                                <div>
                                    <div class="user-name"><?php echo htmlspecialchars($user['username']); ?></div>
                                    <div class="user-balance">USDT <?php echo number_format($user['balance'], 2); ?></div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- VIP Badge -->
                        <div class="vip-badge me-3">
                            <span class="badge bg-gradient-primary">
                                <i class="bi bi-gem me-1"></i>VIP <?php echo $user['vip_level']; ?>
                            </span>
                        </div>
                        
                        <!-- User Menu -->
                        <div class="dropdown">
                            <button class="btn btn-outline-primary dropdown-toggle" type="button" id="userMenuDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                                <i class="bi bi-person-circle"></i>
                            </button>
                            <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="userMenuDropdown">
                                <li><a class="dropdown-item" href="<?php echo BASE_URL; ?>user/profile/profile.php"><i class="bi bi-person me-2"></i>Profile</a></li>
                                <li><a class="dropdown-item" href="<?php echo BASE_URL; ?>user/settings/settings.php"><i class="bi bi-gear me-2"></i>Settings</a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="<?php echo BASE_URL; ?>user/logout/logout.php"><i class="bi bi-box-arrow-right me-2"></i>Logout</a></li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </header>

    <!-- Banner Notification -->
    <?php if ($banner_notification): ?>
    <div class="alert alert-info alert-dismissible fade show mb-0" role="alert" style="border-radius: 0;">
        <div class="container-fluid">
            <div class="d-flex align-items-center">
                <i class="bi bi-megaphone me-2"></i>
                <strong><?php echo htmlspecialchars($banner_notification['title']); ?>:</strong>
                <span class="ms-2"><?php echo htmlspecialchars($banner_notification['message']); ?></span>
                <button type="button" class="btn-close ms-auto" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        </div>
    </div>
    <?php endif; ?>

    <!-- Main Content -->
    <main class="main-content">
        <div class="container-fluid">
            <!-- Welcome Section -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="welcome-card">
                        <div class="row align-items-center">
                            <div class="col-md-8">
                                <h2 class="welcome-title">Welcome back, <?php echo htmlspecialchars($user['username']); ?>! 👋</h2>
                                <p class="welcome-subtitle">Ready to start your tasks and earn rewards?</p>
                            </div>
                            <div class="col-md-4 text-end">
                                <div class="welcome-stats">
                                    <div class="stat-item">
                                        <div class="stat-value"><?php echo $tasks_completed_today; ?>/<?php echo $vip_level['max_daily_tasks'] ?? 0; ?></div>
                                        <div class="stat-label">Today's Tasks</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Stats Cards -->
            <div class="row mb-4">
                <div class="col-md-3 col-sm-6 mb-3">
                    <div class="stat-card">
                        <div class="stat-icon bg-primary">
                            <i class="bi bi-wallet2"></i>
                        </div>
                        <div class="stat-content">
                            <div class="stat-title">Current Balance</div>
                            <div class="stat-value text-success">USDT <?php echo number_format($user['balance'], 2); ?></div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3 col-sm-6 mb-3">
                    <div class="stat-card">
                        <div class="stat-icon bg-success">
                            <i class="bi bi-graph-up-arrow"></i>
                        </div>
                        <div class="stat-content">
                            <div class="stat-title">Today's Profit</div>
                            <div class="stat-value text-success">USDT <?php echo number_format($today_profit, 2); ?></div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3 col-sm-6 mb-3">
                    <div class="stat-card">
                        <div class="stat-icon bg-info">
                            <i class="bi bi-list-task"></i>
                        </div>
                        <div class="stat-content">
                            <div class="stat-title">Tasks Completed</div>
                            <div class="stat-value"><?php echo $tasks_completed_today; ?>/<?php echo $vip_level['max_daily_tasks'] ?? 0; ?></div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3 col-sm-6 mb-3">
                    <div class="stat-card">
                        <div class="stat-icon bg-warning">
                            <i class="bi bi-star-fill"></i>
                        </div>
                        <div class="stat-content">
                            <div class="stat-title">Credit Score</div>
                            <div class="stat-value"><?php echo $user['credit_score'] ?? 100; ?></div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Main Navigation Grid -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="navigation-card">
                        <h4 class="card-title mb-4">
                            <i class="bi bi-grid-3x3-gap me-2"></i>Quick Actions
                        </h4>
                        <div class="row g-3">
                            <div class="col-md-3 col-sm-6">
                                <a href="<?php echo BASE_URL; ?>user/tasks/tasks.php" class="nav-item">
                                    <div class="nav-icon bg-primary">
                                        <i class="bi bi-list-task"></i>
                                    </div>
                                    <div class="nav-title">Start Tasks</div>
                                    <div class="nav-subtitle">Begin matching products</div>
                                </a>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <a href="<?php echo BASE_URL; ?>user/team/team.php" class="nav-item">
                                    <div class="nav-icon bg-success">
                                        <i class="bi bi-people"></i>
                                    </div>
                                    <div class="nav-title">Downline Team</div>
                                    <div class="nav-subtitle">View your referrals</div>
                                </a>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <a href="<?php echo BASE_URL; ?>user/certificate/certificate.php" class="nav-item">
                                    <div class="nav-icon bg-info">
                                        <i class="bi bi-award"></i>
                                    </div>
                                    <div class="nav-title">Certificate</div>
                                    <div class="nav-subtitle">View certificates</div>
                                </a>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <a href="<?php echo BASE_URL; ?>user/withdraw/withdraw.php" class="nav-item">
                                    <div class="nav-icon bg-warning">
                                        <i class="bi bi-cash-coin"></i>
                                    </div>
                                    <div class="nav-title">Withdraw</div>
                                    <div class="nav-subtitle">Request withdrawal</div>
                                </a>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <a href="<?php echo BASE_URL; ?>user/deposit/deposit.php" class="nav-item">
                                    <div class="nav-icon bg-success">
                                        <i class="bi bi-plus-circle"></i>
                                    </div>
                                    <div class="nav-title">Deposit</div>
                                    <div class="nav-subtitle">Add funds</div>
                                </a>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <a href="<?php echo BASE_URL; ?>user/terms/terms.php" class="nav-item">
                                    <div class="nav-icon bg-secondary">
                                        <i class="bi bi-file-text"></i>
                                    </div>
                                    <div class="nav-title">Terms & Conditions</div>
                                    <div class="nav-subtitle">Read our terms</div>
                                </a>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <a href="<?php echo BASE_URL; ?>user/campaigns/campaigns.php" class="nav-item">
                                    <div class="nav-icon bg-danger">
                                        <i class="bi bi-megaphone"></i>
                                    </div>
                                    <div class="nav-title">Latest Campaign</div>
                                    <div class="nav-subtitle">Current promotions</div>
                                </a>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <a href="<?php echo BASE_URL; ?>user/faq/faq.php" class="nav-item">
                                    <div class="nav-icon bg-info">
                                        <i class="bi bi-question-circle"></i>
                                    </div>
                                    <div class="nav-title">FAQ</div>
                                    <div class="nav-subtitle">Get answers</div>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- VIP Level Details & Recent Activity -->
            <div class="row">
                <div class="col-md-6 mb-4">
                    <div class="info-card">
                        <h5 class="card-title">
                            <i class="bi bi-gem me-2"></i>VIP Level Details
                        </h5>
                        <div class="vip-details">
                            <div class="vip-level-badge">
                                <span class="badge bg-gradient-primary fs-6">VIP <?php echo $user['vip_level']; ?></span>
                            </div>
                            <div class="vip-benefits mt-3">
                                <h6>Current Benefits:</h6>
                                <ul class="list-unstyled">
                                    <li><i class="bi bi-check-circle text-success me-2"></i><?php echo number_format(($vip_level['commission_multiplier'] ?? 1) * 100, 1); ?>% profit per task</li>
                                    <li><i class="bi bi-check-circle text-success me-2"></i>Up to <?php echo $vip_level['max_daily_tasks'] ?? 0; ?> tasks daily</li>
                                    <li><i class="bi bi-check-circle text-success me-2"></i>Daily withdrawal limit: USDT <?php echo number_format($vip_level['withdrawal_limit_daily'] ?? 0, 2); ?></li>
                                    <?php if ($vip_level['benefits']): ?>
                                        <li><i class="bi bi-check-circle text-success me-2"></i><?php echo htmlspecialchars($vip_level['benefits']); ?></li>
                                    <?php endif; ?>
                                </ul>
                            </div>
                            <div class="mt-3">
                                <a href="<?php echo BASE_URL; ?>user/vip/vip.php" class="btn btn-outline-primary btn-sm">
                                    <i class="bi bi-arrow-up-circle me-1"></i>View All Levels
                                </a>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-md-6 mb-4">
                    <div class="info-card">
                        <h5 class="card-title">
                            <i class="bi bi-clock-history me-2"></i>Recent Activity
                        </h5>
                        <div class="activity-list">
                            <?php if (!empty($recent_transactions)): ?>
                                <?php foreach ($recent_transactions as $transaction): ?>
                                <div class="activity-item">
                                    <div class="activity-icon">
                                        <?php
                                        $icon = 'bi-arrow-right';
                                        $color = 'text-primary';
                                        switch ($transaction['type']) {
                                            case 'deposit':
                                                $icon = 'bi-plus-circle';
                                                $color = 'text-success';
                                                break;
                                            case 'withdrawal':
                                                $icon = 'bi-dash-circle';
                                                $color = 'text-danger';
                                                break;
                                            case 'commission':
                                                $icon = 'bi-star';
                                                $color = 'text-warning';
                                                break;
                                        }
                                        ?>
                                        <i class="bi <?php echo $icon; ?> <?php echo $color; ?>"></i>
                                    </div>
                                    <div class="activity-content">
                                        <div class="activity-title"><?php echo ucfirst($transaction['type']); ?></div>
                                        <div class="activity-time"><?php echo date('M j, Y H:i', strtotime($transaction['created_at'])); ?></div>
                                    </div>
                                    <div class="activity-amount">
                                        <span class="<?php echo $transaction['type'] === 'withdrawal' ? 'text-danger' : 'text-success'; ?>">
                                            <?php echo $transaction['type'] === 'withdrawal' ? '-' : '+'; ?>USDT <?php echo number_format($transaction['amount'], 2); ?>
                                        </span>
                                    </div>
                                </div>
                                <?php endforeach; ?>
                            <?php else: ?>
                                <div class="text-center text-muted py-4">
                                    <i class="bi bi-inbox fs-1"></i>
                                    <p class="mt-2">No recent activity</p>
                                </div>
                            <?php endif; ?>
                        </div>
                        <div class="mt-3">
                            <a href="<?php echo BASE_URL; ?>user/transactions/transactions.php" class="btn btn-outline-primary btn-sm">
                                <i class="bi bi-list me-1"></i>View All Transactions
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

        </div>
    </main>

    <script>
        // Dashboard specific JavaScript
        $(document).ready(function() {
            // Show welcome modal if needed
            <?php if ($show_welcome_popup): ?>
            $('#welcomeModal').modal('show');
            <?php endif; ?>

            // Auto-hide alerts after 5 seconds
            setTimeout(function() {
                $('.alert').fadeOut();
            }, 5000);

            // Add hover effects to navigation items
            $('.nav-item').hover(
                function() {
                    $(this).find('.nav-icon').addClass('scale-110');
                },
                function() {
                    $(this).find('.nav-icon').removeClass('scale-110');
                }
            );

            // Real-time balance updates (optional)
            function updateBalance() {
                $.ajax({
                    url: '<?php echo BASE_URL; ?>user/api/get-balance.php',
                    method: 'GET',
                    dataType: 'json',
                    success: function(response) {
                        if (response.success) {
                            $('.user-balance').text('USDT ' + parseFloat(response.balance).toFixed(2));
                            $('.stat-value.text-success').first().text('USDT ' + parseFloat(response.balance).toFixed(2));
                        }
                    },
                    error: function() {
                        // Silently fail
                    }
                });
            }

            // Update balance every 30 seconds
            setInterval(updateBalance, 30000);
        });
    </script>

<?php
// Set additional JS for footer
$additional_js = '
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.7.0.min.js"></script>
';
include '../includes/user_footer.php';
?>
