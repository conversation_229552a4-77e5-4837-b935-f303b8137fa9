<?php
ob_start(); // Start output buffering at the very beginning of the page

/**
 * Bamboo Web Application - User Login Page
 * Company: Notepadsly
 * Version: 1.0
 */

// Define app constant
define('BAMBOO_APP', true);

// Include required files
require_once '../../includes/config.php';
require_once '../../includes/functions.php';

// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Redirect if already logged in
if (isLoggedIn()) {
    redirect('user/dashboard/');
}

// Handle login form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $username = sanitizeInput($_POST['username'] ?? '');
    $password = $_POST['password'] ?? '';
    $remember_me = isset($_POST['remember_me']);
    
    // Validate CSRF token
    if (!verifyCSRFToken($_POST['csrf_token'] ?? '')) {
        showError('Invalid security token. Please try again.');
    } else {
        // Validate input
        if (empty($username) || empty($password)) {
            showError('Please enter both username and password.');
        } else {
            // Check login attempts
            $ip_address = $_SERVER['REMOTE_ADDR'];
            $login_attempts = getRecordCount('user_sessions', 
                'ip_address = ? AND login_attempts >= ? AND last_attempt > DATE_SUB(NOW(), INTERVAL ? MINUTE)',
                [$ip_address, MAX_LOGIN_ATTEMPTS, LOGIN_LOCKOUT_TIME / 60]
            );
            
            if ($login_attempts > 0) {
                showError('Too many failed login attempts. Please try again later.');
            } else {
                // Attempt login
                $user = fetchRow(
                    "SELECT id, username, password_hash, status, vip_level, balance FROM users WHERE username = ? OR phone = ?",
                    [$username, $username]
                );

                if ($user && verifyPassword($password, $user['password_hash'])) {
                    if ($user['status'] === 'active') {
                        // Successful login
                        $_SESSION['user_id'] = $user['id'];
                        $_SESSION['username'] = $user['username'];
                        $_SESSION['user_type'] = 'user';
                        $_SESSION['vip_level'] = $user['vip_level'];
                        $_SESSION['last_activity'] = time();
                        
                        // Clear login attempts
                        executeQuery(
                            "DELETE FROM user_sessions WHERE ip_address = ? AND login_attempts > 0",
                            [$ip_address]
                        );
                        
                        // Create session record
                        $session_data = [
                            'user_id' => $user['id'],
                            'session_id' => session_id(),
                            'ip_address' => $ip_address,
                            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? '',
                            'login_time' => date('Y-m-d H:i:s'),
                            'last_activity' => date('Y-m-d H:i:s')
                        ];
                        insertRecord('user_sessions', $session_data);
                        
                        // Set remember me cookie if requested
                        if ($remember_me) {
                            $remember_token = generateRandomString(32);
                            setcookie('remember_token', $remember_token, time() + (30 * 24 * 60 * 60), '/');
                            updateRecord('users', 
                                ['remember_token' => $remember_token], 
                                'id = ?', 
                                [$user['id']]
                            );
                        }
                        
                        showSuccess('Login successful! Welcome back.');
                        redirect('user/dashboard/dashboard.php');
                    } else {
                        showError('Your account is not active. Please contact support.');
                    }
                } else {
                    // Failed login - record attempt
                    $session_data = [
                        'ip_address' => $ip_address,
                        'login_attempts' => 1,
                        'last_attempt' => date('Y-m-d H:i:s')
                    ];
                    
                    $existing_session = fetchRow(
                        "SELECT id, login_attempts FROM user_sessions WHERE ip_address = ? AND login_attempts > 0",
                        [$ip_address]
                    );
                    
                    if ($existing_session) {
                        updateRecord('user_sessions',
                            ['login_attempts' => $existing_session['login_attempts'] + 1, 'last_attempt' => date('Y-m-d H:i:s')],
                            'id = ?',
                            [$existing_session['id']]
                        );
                    } else {
                        insertRecord('user_sessions', $session_data);
                    }
                    
                    showError('Invalid username or password.');
                }
            }
        }
    }
}

// Get app settings safely
$app_name = APP_NAME;
$app_logo = '';
try {
    $app_logo = getAppSetting('app_logo', '');
    $app_name = getAppSetting('app_name', APP_NAME);
} catch (Exception $e) {
    // Fallback if settings table doesn't exist yet
    $app_logo = '';
    $app_name = APP_NAME;
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <title>Login - <?php echo htmlspecialchars($app_name); ?></title>

    <!-- Favicon -->
    <?php
    $favicon_path = ASSETS_URL . 'images/favicon.ico';
    $favicon_exists = file_exists(__DIR__ . '/../../assets/images/favicon.ico') && filesize(__DIR__ . '/../../assets/images/favicon.ico') > 100; // Check if it's a real file, not placeholder
    ?>
    <?php if ($favicon_exists): ?>
        <link rel="icon" type="image/x-icon" href="<?php echo $favicon_path; ?>?v=<?php echo filemtime(__DIR__ . '/../../assets/images/favicon.ico'); ?>">
        <link rel="shortcut icon" type="image/x-icon" href="<?php echo $favicon_path; ?>?v=<?php echo filemtime(__DIR__ . '/../../assets/images/favicon.ico'); ?>">
    <?php else: ?>
        <!-- Fallback: Use a data URI for a simple favicon -->
        <link rel="icon" type="image/svg+xml" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32'><rect width='32' height='32' fill='%23ff6900'/><text x='16' y='20' font-family='Arial' font-size='18' fill='white' text-anchor='middle'>B</text></svg>">
    <?php endif; ?>

    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    
    <!-- Meta tags for mobile -->
    <meta name="theme-color" content="#ff6900">
    
    <style>
        body {
            background: linear-gradient(135deg, #ff6900 0%, #ff8533 25%, #ffb366 50%, #ff9f40 75%, #ff6900 100%);
            background-attachment: fixed;
            background-size: 400% 400%;
            animation: gradientShift 15s ease infinite;
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 0;
        }

        @keyframes gradientShift {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }

        .login-container {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 2rem 1rem;
        }

        .login-card {
            background: rgba(255, 255, 255, 0.98);
            backdrop-filter: blur(20px);
            border-radius: 30px;
            box-shadow: 0 40px 80px rgba(0, 0, 0, 0.12);
            padding: 2.5rem 2rem;
            width: 100%;
            max-width: 420px;
            border: 1px solid rgba(255, 255, 255, 0.4);
            animation: slideInUp 0.8s ease-out;
        }

        @keyframes slideInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .brand-section {
            text-align: center;
            margin-bottom: 2.5rem;
        }

        .brand-icon {
            width: 100px;
            height: 100px;
            background: linear-gradient(135deg, #ff6900, #ff8533);
            border-radius: 25px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1.5rem;
            font-size: 3rem;
            color: white;
            box-shadow: 0 15px 40px rgba(255, 105, 0, 0.3);
            animation: float 3s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
        }

        .brand-title {
            color: #ff6900;
            font-weight: 800;
            font-size: 2.5rem;
            margin-bottom: 0.5rem;
            background: linear-gradient(135deg, #ff6900 0%, #ff8533 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .welcome-text {
            color: #6c757d;
            font-size: 1.1rem;
            margin-bottom: 0;
        }

        .form-group {
            margin-bottom: 1.5rem;
            position: relative;
        }

        .form-control {
            border: 2px solid #e9ecef;
            border-radius: 15px;
            padding: 1.2rem 1rem 0.8rem 1rem;
            font-size: 1.1rem;
            transition: all 0.3s ease;
            background-color: rgba(248, 249, 250, 0.9);
            height: 60px;
            width: 100%;
        }

        .form-control:focus {
            border-color: #ff6900;
            box-shadow: 0 0 0 0.25rem rgba(255, 105, 0, 0.15);
            background-color: white;
            transform: translateY(-2px);
            outline: none;
        }

        .form-control:focus + .form-label,
        .form-control.has-value + .form-label {
            transform: translateY(-1.2rem) scale(0.85);
            color: #ff6900;
            background: rgba(255, 255, 255, 0.9);
            padding: 0 0.5rem;
            border-radius: 4px;
        }

        .form-label {
            position: absolute;
            top: 1.2rem;
            left: 1rem;
            color: #6c757d;
            font-weight: 500;
            transition: all 0.3s ease;
            pointer-events: none;
            background: transparent;
            z-index: 2;
        }

        .form-control::placeholder {
            color: transparent;
            transition: color 0.3s ease;
        }

        .form-control:focus::placeholder {
            color: #adb5bd;
        }

        .password-group {
            position: relative;
        }

        .password-group .form-control {
            padding-right: 3.5rem;
        }

        .password-toggle {
            position: absolute;
            right: 1rem;
            top: 50%;
            transform: translateY(-50%);
            background: none;
            border: none;
            color: #6c757d;
            font-size: 1.2rem;
            cursor: pointer;
            z-index: 3;
            transition: all 0.3s ease;
        }

        .password-toggle:hover {
            color: #ff6900;
        }

        .btn-primary {
            background: linear-gradient(135deg, #ff6900 0%, #ff8533 100%);
            border: none;
            border-radius: 15px;
            padding: 1rem 2rem;
            font-weight: 700;
            font-size: 1.1rem;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .btn-primary:hover {
            transform: translateY(-3px);
            box-shadow: 0 15px 35px rgba(255, 105, 0, 0.4);
        }

        .btn-primary:active {
            transform: translateY(-1px);
        }

        .btn-primary::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s;
        }

        .btn-primary:hover::before {
            left: 100%;
        }

        .btn-outline-primary {
            border: 2px solid #ff6900;
            color: #ff6900;
            border-radius: 15px;
            padding: 0.75rem 1.5rem;
            font-weight: 600;
            transition: all 0.3s ease;
            background: transparent;
        }

        .btn-outline-primary:hover {
            background: linear-gradient(135deg, #ff6900 0%, #ff8533 100%);
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(255, 105, 0, 0.3);
        }

        .form-check-input {
            width: 1.3rem;
            height: 1.3rem;
            border: 2px solid #dee2e6;
            border-radius: 6px;
        }

        .form-check-input:checked {
            background-color: #ff6900;
            border-color: #ff6900;
        }

        .form-check-label {
            font-size: 0.95rem;
            color: #6c757d;
            margin-left: 0.5rem;
        }

        .alert {
            border-radius: 15px;
            border: none;
            margin-bottom: 1.5rem;
        }

        .alert-success {
            background: linear-gradient(135deg, #d4edda, #c3e6cb);
            color: #155724;
        }

        .alert-danger {
            background: linear-gradient(135deg, #f8d7da, #f5c6cb);
            color: #721c24;
        }

        .text-muted {
            color: #6c757d !important;
        }

        .link-primary {
            color: #ff6900 !important;
            text-decoration: none;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .link-primary:hover {
            color: #ff8533 !important;
            text-decoration: underline;
        }

        .footer-info {
            text-align: center;
            margin-top: 2rem;
            padding-top: 1.5rem;
            border-top: 1px solid rgba(108, 117, 125, 0.2);
        }

        .divider {
            text-align: center;
            margin: 2rem 0;
            position: relative;
        }

        .divider::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 0;
            right: 0;
            height: 1px;
            background: linear-gradient(to right, transparent, #dee2e6, transparent);
        }

        .divider span {
            background: rgba(255, 255, 255, 0.95);
            padding: 0 1rem;
            color: #6c757d;
            font-size: 0.9rem;
        }

        /* Responsive design */
        @media (max-width: 576px) {
            .login-card {
                padding: 2rem 1.5rem;
                margin: 1rem;
                border-radius: 20px;
            }
            
            .brand-title {
                font-size: 2rem;
            }
            
            .brand-icon {
                width: 80px;
                height: 80px;
                font-size: 2.5rem;
            }
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="login-card">
            <!-- Flash Messages -->
            <?php $flash_messages = getFlashMessages(); ?>
            <?php if (!empty($flash_messages)): ?>
                <?php if (isset($flash_messages['success'])): ?>
                    <div class="alert alert-success alert-dismissible fade show" role="alert">
                        <i class="bi bi-check-circle-fill me-2"></i>
                        <?php echo htmlspecialchars($flash_messages['success']); ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>
                
                <?php if (isset($flash_messages['error'])): ?>
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        <i class="bi bi-exclamation-triangle-fill me-2"></i>
                        <?php echo htmlspecialchars($flash_messages['error']); ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>
            <?php endif; ?>

            <!-- Brand Section -->
            <div class="brand-section">
                <?php if ($favicon_exists): ?>
                    <img src="<?php echo $favicon_path; ?>?v=<?php echo filemtime(__DIR__ . '/../../assets/images/favicon.ico'); ?>"
                         alt="<?php echo htmlspecialchars($app_name); ?>"
                         class="brand-icon img-fluid">
                <?php else: ?>
                    <div class="brand-icon">
                        <i class="bi bi-grid-3x3-gap-fill"></i>
                    </div>
                <?php endif; ?>
                
                <h1 class="brand-title"><?php echo htmlspecialchars($app_name); ?></h1>
                <p class="welcome-text">Welcome back! Please sign in to your account.</p>
            </div>
            
            <!-- Login Form -->
            <form method="POST" action="" id="loginForm" novalidate>
                <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                
                <!-- Username Field -->
                <div class="form-group">
                    <input type="text" 
                           class="form-control" 
                           id="username" 
                           name="username" 
                           placeholder="Enter your username or phone"
                           value="<?php echo htmlspecialchars($_POST['username'] ?? ''); ?>"
                           autocomplete="off"
                           autocapitalize="off"
                           spellcheck="false"
                           required>
                    <label for="username" class="form-label">
                        <i class="bi bi-person me-2" style="color: #ff6900;"></i>Username or Phone
                    </label>
                </div>
                
                <!-- Password Field -->
                <div class="form-group">
                    <div class="password-group">
                        <input type="password" 
                               class="form-control" 
                               id="password" 
                               name="password" 
                               placeholder="Enter your password"
                               autocomplete="new-password"
                               required>
                        <button type="button" 
                                class="password-toggle" 
                                id="togglePassword"
                                title="Toggle password visibility">
                            <i class="bi bi-eye"></i>
                        </button>
                        <label for="password" class="form-label">
                            <i class="bi bi-lock me-2" style="color: #ff6900;"></i>Password
                        </label>
                    </div>
                </div>
                
                <!-- Remember Me -->
                <div class="form-check mb-4">
                    <input type="checkbox" 
                           class="form-check-input" 
                           id="remember_me" 
                           name="remember_me">
                    <label class="form-check-label" for="remember_me">
                        Remember me for 30 days
                    </label>
                </div>
                
                <!-- Login Button -->
                <div class="d-grid mb-3">
                    <button type="submit" 
                            class="btn btn-primary btn-lg"
                            id="loginBtn">
                        <i class="bi bi-box-arrow-in-right me-2"></i>
                        <span class="btn-text">Sign In</span>
                        <span class="btn-loading d-none">
                            <span class="spinner-border spinner-border-sm me-2" role="status"></span>
                            Signing in...
                        </span>
                    </button>
                </div>
                
                <!-- Forgot Password Link -->
                <div class="text-center mb-3">
                    <a href="<?php echo BASE_URL; ?>user/forgot-password/" class="link-primary">
                        <i class="bi bi-question-circle me-1"></i>Forgot your password?
                    </a>
                </div>
                
                <!-- Divider -->
                <div class="divider">
                    <span>New to <?php echo htmlspecialchars($app_name); ?>?</span>
                </div>
                
                <!-- Register Link -->
                <div class="text-center">
                    <a href="<?php echo BASE_URL; ?>user/register/" class="btn btn-outline-primary">
                        <i class="bi bi-person-plus me-2"></i>Create Account
                    </a>
                </div>
            </form>
            
        </div>
    </div>

    <!-- Bootstrap 5 JS Bundle -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.7.0.min.js"></script>
    
    <script>
        $(document).ready(function() {
            // Password toggle functionality
            $('#togglePassword').on('click', function() {
                const passwordField = $('#password');
                const icon = $(this).find('i');
                
                if (passwordField.attr('type') === 'password') {
                    passwordField.attr('type', 'text');
                    icon.removeClass('bi-eye').addClass('bi-eye-slash');
                    $(this).attr('title', 'Hide password');
                } else {
                    passwordField.attr('type', 'password');
                    icon.removeClass('bi-eye-slash').addClass('bi-eye');
                    $(this).attr('title', 'Show password');
                }
            });
            
            // Form submission with loading state
            $('#loginForm').on('submit', function(e) {
                const username = $('#username').val().trim();
                const password = $('#password').val();
                
                if (!username || !password) {
                    e.preventDefault();
                    alert('Please enter both username and password.');
                    return false;
                }
                
                // Show loading state
                const btn = $('#loginBtn');
                btn.find('.btn-text').addClass('d-none');
                btn.find('.btn-loading').removeClass('d-none');
                btn.prop('disabled', true);
            });
            
            // Auto-hide alerts after 5 seconds
            setTimeout(function() {
                $('.alert').fadeOut();
            }, 5000);
            
            // Focus on username field
            $('#username').focus();
            
            // Clear any browser autofill
            setTimeout(function() {
                $('#username').val('');
                $('#password').val('');
            }, 100);
            
            // Disable autofill
            $('input').attr('autocomplete', 'off');
            
            // Handle floating labels
            function updateFloatingLabels() {
                $('input.form-control').each(function() {
                    if ($(this).val() !== '') {
                        $(this).addClass('has-value');
                    } else {
                        $(this).removeClass('has-value');
                    }
                });
            }
            
            // Update labels on input
            $('input.form-control').on('input blur', function() {
                updateFloatingLabels();
            });
            
            // Update labels on focus
            $('input.form-control').on('focus', function() {
                $(this).addClass('has-value');
            });
            
            // Update labels on blur
            $('input.form-control').on('blur', function() {
                if ($(this).val() === '') {
                    $(this).removeClass('has-value');
                }
            });
            
            // Initial check for pre-filled values
            updateFloatingLabels();
        });
    </script>
</body>
</html>