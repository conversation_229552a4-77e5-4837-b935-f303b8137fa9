<?php
/**
 * Bamboo Web Application - User Login Test Handler
 * Company: Notepadsly
 * Version: 1.0
 * Description: This file contains the core login logic extracted from login.php for testing purposes.
 */

// Define app constant
define('BAMBOO_APP', true);

// Include required files
require_once '../../includes/config.php';
require_once '../../includes/functions.php';

// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Ensure this script is only accessed via POST
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $username = sanitizeInput($_POST['username'] ?? '');
    $password = $_POST['password'] ?? '';
    $remember_me = isset($_POST['remember_me']);
    
    // Validate CSRF token
    if (!verifyCSRFToken($_POST['csrf_token'] ?? '')) {
        showError('Invalid security token. Please try again.');
    } else {
        // Validate input
        if (empty($username) || empty($password)) {
            showError('Please enter both username and password.');
        } else {
            // Check login attempts
            $ip_address = $_SERVER['REMOTE_ADDR'];
            $login_attempts = getRecordCount('user_sessions', 
                'ip_address = ? AND login_attempts >= ? AND last_attempt > DATE_SUB(NOW(), INTERVAL ? MINUTE)',
                [$ip_address, MAX_LOGIN_ATTEMPTS, LOGIN_LOCKOUT_TIME / 60]
            );
            
            if ($login_attempts > 0) {
                showError('Too many failed login attempts. Please try again later.');
            } else {
                // Attempt login
                $user = fetchRow(
                    "SELECT id, username, password_hash, status, vip_level, balance FROM users WHERE username = ? OR phone = ?",
                    [$username, $username]
                );

                if ($user && verifyPassword($password, $user['password_hash'])) {
                    if ($user['status'] === 'active') {
                        // Successful login
                        $_SESSION['user_id'] = $user['id'];
                        $_SESSION['username'] = $user['username'];
                        $_SESSION['user_type'] = 'user';
                        $_SESSION['vip_level'] = $user['vip_level'];
                        $_SESSION['last_activity'] = time();
                        
                        // Clear login attempts
                        executeQuery(
                            "DELETE FROM user_sessions WHERE ip_address = ? AND login_attempts > 0",
                            [$ip_address]
                        );
                        
                        // Create session record
                        $session_data = [
                            'user_id' => $user['id'],
                            'session_id' => session_id(),
                            'ip_address' => $ip_address,
                            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? '',
                            'login_time' => date('Y-m-d H:i:s'),
                            'last_activity' => date('Y-m-d H:i:s')
                        ];
                        insertRecord('user_sessions', $session_data);
                        
                        // Set remember me cookie if requested
                        if ($remember_me) {
                            $remember_token = generateRandomString(32);
                            setcookie('remember_token', $remember_token, time() + (30 * 24 * 60 * 60), '/');
                            updateRecord('users', 
                                ['remember_token' => $remember_token], 
                                'id = ?', 
                                [$user['id']]
                            );
                        }
                        
                        showSuccess('Login successful! Welcome back.');
                        redirect('user/dashboard/dashboard.php');
                    } else {
                        showError('Your account is not active. Please contact support.');
                    }
                } else {
                    // Failed login - record attempt
                    $session_data = [
                        'ip_address' => $ip_address,
                        'login_attempts' => 1,
                        'last_attempt' => date('Y-m-d H:i:s')
                    ];
                    
                    $existing_session = fetchRow(
                        "SELECT id, login_attempts FROM user_sessions WHERE ip_address = ? AND login_attempts > 0",
                        [$ip_address]
                    );
                    
                    if ($existing_session) {
                        updateRecord('user_sessions',
                            ['login_attempts' => $existing_session['login_attempts'] + 1, 'last_attempt' => date('Y-m-d H:i:s')],
                            'id = ?',
                            [$existing_session['id']]
                        );
                    } else {
                        insertRecord('user_sessions', $session_data);
                    }
                    
                    showError('Invalid username or password.');
                }
            }
        }
    }
}
?>