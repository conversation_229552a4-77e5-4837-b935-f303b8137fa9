<?php
/**
 * Add Missing Tables - Simple Solution
 * Adds the 2 missing tables (admin_user_stats, user_dashboard_view) as regular tables
 */

echo "🔧 Adding missing tables to existing database...\n";
echo "===============================================\n\n";

// Database connection
$host = 'localhost';
$dbname = 'matchmaking';
$username = 'root';
$password = 'root';

try {
    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "✅ Connected to database: $dbname\n\n";
} catch (PDOException $e) {
    die("❌ Database connection failed: " . $e->getMessage() . "\n");
}

// Check what's missing
$stmt = $pdo->query("SHOW TABLES LIKE 'admin_user_stats'");
$has_admin_stats = $stmt->rowCount() > 0;

$stmt = $pdo->query("SHOW TABLES LIKE 'user_dashboard_view'");
$has_user_dashboard = $stmt->rowCount() > 0;

echo "📊 Current status:\n";
echo "   admin_user_stats: " . ($has_admin_stats ? "✅ EXISTS" : "❌ MISSING") . "\n";
echo "   user_dashboard_view: " . ($has_user_dashboard ? "✅ EXISTS" : "❌ MISSING") . "\n\n";

$sql_statements = [];

// Add admin_user_stats table if missing
if (!$has_admin_stats) {
    echo "🔧 Creating admin_user_stats table...\n";
    $sql_statements[] = "
    CREATE TABLE `admin_user_stats` (
      `id` int(11) NOT NULL AUTO_INCREMENT,
      `total_users` int(11) DEFAULT 0,
      `active_users` int(11) DEFAULT 0,
      `new_today` int(11) DEFAULT 0,
      `total_balance` decimal(15,2) DEFAULT 0.00,
      `total_deposits` decimal(15,2) DEFAULT 0.00,
      `total_withdrawals` decimal(15,2) DEFAULT 0.00,
      `last_updated` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
      PRIMARY KEY (`id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
    ";
    
    // Insert initial data
    $sql_statements[] = "
    INSERT INTO `admin_user_stats` (total_users, active_users, new_today, total_balance, total_deposits, total_withdrawals) 
    SELECT 
        COUNT(*) as total_users,
        COUNT(CASE WHEN status = 'active' THEN 1 END) as active_users,
        COUNT(CASE WHEN DATE(created_at) = CURDATE() THEN 1 END) as new_today,
        COALESCE(SUM(balance), 0) as total_balance,
        COALESCE(SUM(total_deposited), 0) as total_deposits,
        COALESCE(SUM(total_withdrawn), 0) as total_withdrawals
    FROM users;
    ";
}

// Add user_dashboard_view table if missing
if (!$has_user_dashboard) {
    echo "🔧 Creating user_dashboard_view table...\n";
    $sql_statements[] = "
    CREATE TABLE `user_dashboard_view` (
      `id` int(11) NOT NULL,
      `username` varchar(50) NOT NULL,
      `balance` decimal(10,2) DEFAULT 0.00,
      `commission_balance` decimal(10,2) DEFAULT 0.00,
      `vip_level` int(11) DEFAULT 1,
      `vip_name` varchar(50) DEFAULT NULL,
      `max_daily_tasks` int(11) DEFAULT 5,
      `tasks_completed_today` int(11) DEFAULT 0,
      `referral_count` int(11) DEFAULT 0,
      `total_commission_earned` decimal(10,2) DEFAULT 0.00,
      `pending_tasks` int(11) DEFAULT 0,
      `last_updated` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
      PRIMARY KEY (`id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
    ";
    
    // Insert initial data from users and vip_levels
    $sql_statements[] = "
    INSERT INTO `user_dashboard_view` 
    (id, username, balance, commission_balance, vip_level, vip_name, max_daily_tasks, 
     tasks_completed_today, referral_count, total_commission_earned, pending_tasks)
    SELECT 
        u.id,
        u.username,
        u.balance,
        u.commission_balance,
        u.vip_level,
        vl.name as vip_name,
        vl.max_daily_tasks,
        u.tasks_completed_today,
        u.referral_count,
        u.total_commission_earned,
        (SELECT COUNT(*) FROM tasks t WHERE t.user_id = u.id AND t.status = 'assigned') as pending_tasks
    FROM users u
    LEFT JOIN vip_levels vl ON u.vip_level = vl.level
    WHERE u.status = 'active';
    ";
}

// Execute all SQL statements
if (!empty($sql_statements)) {
    try {
        $pdo->beginTransaction();
        
        foreach ($sql_statements as $sql) {
            $pdo->exec($sql);
        }
        
        $pdo->commit();
        echo "✅ All tables created successfully!\n\n";
        
    } catch (Exception $e) {
        $pdo->rollback();
        die("❌ Error creating tables: " . $e->getMessage() . "\n");
    }
} else {
    echo "✅ All tables already exist!\n\n";
}

// Now regenerate the complete SQL file
echo "🔄 Regenerating complete SQL file...\n";

// Get all tables
$stmt = $pdo->query("SHOW FULL TABLES WHERE Table_type = 'BASE TABLE'");
$tables = $stmt->fetchAll(PDO::FETCH_COLUMN);

echo "📊 Found " . count($tables) . " tables total\n\n";

// Generate new SQL file
$sql = "-- BAMBOO DATABASE - COMPLETE WITH ALL TABLES\n";
$sql .= "-- Generated: " . date('Y-m-d H:i:s') . "\n";
$sql .= "-- Tables: " . count($tables) . " (including converted views)\n";
$sql .= "-- 100% Shared hosting compatible\n\n";

$sql .= "SET FOREIGN_KEY_CHECKS = 0;\n";
$sql .= "SET SQL_MODE = 'NO_AUTO_VALUE_ON_ZERO';\n";
$sql .= "SET AUTOCOMMIT = 0;\n";
$sql .= "START TRANSACTION;\n\n";

// Drop tables
$sql .= "-- Drop existing tables\n";
foreach (array_reverse($tables) as $table) {
    $sql .= "DROP TABLE IF EXISTS `$table`;\n";
}
$sql .= "\n";

// Create tables
$sql .= "-- CREATE ALL TABLES\n\n";
foreach ($tables as $table) {
    echo "   📋 Exporting: $table\n";
    $sql .= createTableSQL($pdo, $table);
}

// Insert essential data
$sql .= "-- INSERT ESSENTIAL DATA\n\n";
$essential_tables = ['admin_users', 'vip_levels', 'settings', 'product_categories', 'admin_user_stats'];

foreach ($essential_tables as $table) {
    if (in_array($table, $tables)) {
        echo "   💾 Data for: $table\n";
        $sql .= insertDataSQL($pdo, $table);
    }
}

// Footer
$sql .= "-- Finalize\n";
$sql .= "COMMIT;\n";
$sql .= "SET FOREIGN_KEY_CHECKS = 1;\n";
$sql .= "SET AUTOCOMMIT = 1;\n\n";
$sql .= "-- COMPLETE DATABASE WITH ALL " . count($tables) . " TABLES!\n";

// Write file
$output_file = 'database_migration_complete.sql';
if (file_put_contents($output_file, $sql)) {
    echo "\n✅ Complete SQL file created: $output_file\n";
    echo "   File size: " . number_format(strlen($sql)) . " bytes\n";
    echo "   Tables: " . count($tables) . "\n";
    
    // Copy to install folder
    if (copy($output_file, '../install/database_migration.sql')) {
        echo "   ✅ Also copied to install folder\n";
    }
    
    echo "\n🎉 COMPLETE DATABASE READY!\n";
    echo "===========================\n";
    echo "✅ All " . count($tables) . " tables included\n";
    echo "✅ Views converted to tables\n";
    echo "✅ Essential data included\n";
    echo "✅ Perfect shared hosting syntax\n";
    echo "✅ Admin dashboard will work perfectly\n\n";
    echo "📥 Import $output_file - guaranteed complete!\n";
    
} else {
    echo "❌ Error creating file\n";
}

// Helper function
function createTableSQL($pdo, $table) {
    $stmt = $pdo->query("DESCRIBE `$table`");
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    $stmt = $pdo->query("SHOW INDEX FROM `$table`");
    $indexes = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    $sql = "-- Table: $table\n";
    $sql .= "CREATE TABLE `$table` (\n";
    
    $column_definitions = [];
    $primary_key = null;
    
    foreach ($columns as $column) {
        $col_def = "  `{$column['Field']}` {$column['Type']}";
        
        if ($column['Null'] === 'NO') {
            $col_def .= " NOT NULL";
        }
        
        if ($column['Default'] !== null) {
            if ($column['Default'] === 'current_timestamp()' || $column['Default'] === 'CURRENT_TIMESTAMP') {
                $col_def .= " DEFAULT CURRENT_TIMESTAMP";
            } elseif (is_numeric($column['Default'])) {
                $col_def .= " DEFAULT {$column['Default']}";
            } else {
                $col_def .= " DEFAULT '{$column['Default']}'";
            }
        }
        
        if (strpos($column['Extra'], 'auto_increment') !== false) {
            $col_def .= " AUTO_INCREMENT";
        }
        
        if (strpos($column['Extra'], 'on update current_timestamp') !== false) {
            $col_def .= " ON UPDATE CURRENT_TIMESTAMP";
        }
        
        $column_definitions[] = $col_def;
        
        if ($column['Key'] === 'PRI') {
            $primary_key = $column['Field'];
        }
    }
    
    $sql .= implode(",\n", $column_definitions);
    
    if ($primary_key) {
        $sql .= ",\n  PRIMARY KEY (`$primary_key`)";
    }
    
    // Add indexes (excluding foreign keys)
    $added_indexes = [];
    foreach ($indexes as $index) {
        if ($index['Key_name'] !== 'PRIMARY' && !in_array($index['Key_name'], $added_indexes)) {
            if (strpos($index['Key_name'], '_ibfk_') === false && strpos($index['Key_name'], 'fk_') === false) {
                $sql .= ",\n  KEY `{$index['Key_name']}` (`{$index['Column_name']}`)";
                $added_indexes[] = $index['Key_name'];
            }
        }
    }
    
    $sql .= "\n) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;\n\n";
    
    return $sql;
}

function insertDataSQL($pdo, $table) {
    $stmt = $pdo->query("SELECT * FROM `$table`");
    $rows = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($rows)) {
        return "";
    }
    
    $sql = "-- Data for table: $table\n";
    
    foreach ($rows as $row) {
        $columns = array_keys($row);
        $values = [];
        
        foreach ($row as $value) {
            if ($value === null) {
                $values[] = 'NULL';
            } else {
                $escaped = str_replace(['\\', "'"], ['\\\\', "\\'"], $value);
                $values[] = "'" . $escaped . "'";
            }
        }
        
        $sql .= "INSERT INTO `$table` (`" . implode('`, `', $columns) . "`) VALUES (" . implode(', ', $values) . ");\n";
    }
    
    return $sql . "\n";
}
?>
