<?php
/**
 * Create Complete SQL from Database
 * This connects to the database and creates a complete SQL dump that's shared hosting compatible
 */

echo "🔧 Creating complete SQL from database...\n";
echo "==========================================\n\n";

// Database connection
$host = 'localhost';
$dbname = 'matchmaking';
$username = 'root';
$password = 'root';

try {
    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "✅ Connected to database\n\n";
} catch (PDOException $e) {
    die("❌ Database connection failed: " . $e->getMessage() . "\n");
}

// Get all tables
$stmt = $pdo->query("SHOW TABLES");
$tables = $stmt->fetchAll(PDO::FETCH_COLUMN);

echo "📊 Found " . count($tables) . " tables:\n";
foreach ($tables as $table) {
    echo "   - $table\n";
}
echo "\n";

// Start building SQL
$sql = "-- BAMBOO DATABASE - COMPLETE VERSION FOR SHARED HOSTING\n";
$sql .= "-- Generated: " . date('Y-m-d H:i:s') . "\n";
$sql .= "-- Tables: " . count($tables) . "\n";
$sql .= "-- Compatible with shared hosting - no SUPER privileges required\n\n";

$sql .= "-- Disable foreign key checks for clean import\n";
$sql .= "SET FOREIGN_KEY_CHECKS = 0;\n";
$sql .= "SET SQL_MODE = 'NO_AUTO_VALUE_ON_ZERO';\n";
$sql .= "SET AUTOCOMMIT = 0;\n";
$sql .= "START TRANSACTION;\n\n";

// Drop tables first (in reverse order to handle dependencies)
$sql .= "-- Drop existing tables\n";
foreach (array_reverse($tables) as $table) {
    if (strpos($table, '_view') === false) { // Skip views
        $sql .= "DROP TABLE IF EXISTS `$table`;\n";
    }
}
$sql .= "\n";

// Create tables
$sql .= "-- CREATE TABLES\n\n";
foreach ($tables as $table) {
    if (strpos($table, '_view') !== false) {
        echo "   ⏭️  Skipping view: $table\n";
        continue; // Skip views
    }
    
    echo "   📋 Processing table: $table\n";
    
    // Get CREATE TABLE statement
    $stmt = $pdo->query("SHOW CREATE TABLE `$table`");
    $create_table = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($create_table) {
        $create_sql = $create_table['Create Table'];
        
        // Clean up the CREATE TABLE statement
        $create_sql = str_replace('CREATE TABLE', "-- Table: $table\nCREATE TABLE", $create_sql);
        
        $sql .= $create_sql . ";\n\n";
    }
}

// Insert data for essential tables
$sql .= "-- INSERT DATA\n\n";

$essential_tables = ['admin_users', 'vip_levels', 'settings', 'product_categories'];

foreach ($essential_tables as $table) {
    if (in_array($table, $tables)) {
        echo "   💾 Getting data for: $table\n";
        
        $stmt = $pdo->query("SELECT * FROM `$table`");
        $rows = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        if (!empty($rows)) {
            $sql .= "-- Data for table: $table\n";
            
            // Get column names
            $columns = array_keys($rows[0]);
            $sql .= "INSERT INTO `$table` (`" . implode('`, `', $columns) . "`) VALUES\n";
            
            $values = [];
            foreach ($rows as $row) {
                $row_values = [];
                foreach ($row as $value) {
                    if ($value === null) {
                        $row_values[] = 'NULL';
                    } else {
                        $row_values[] = "'" . addslashes($value) . "'";
                    }
                }
                $values[] = "(" . implode(', ', $row_values) . ")";
            }
            
            $sql .= implode(",\n", $values) . ";\n\n";
        }
    }
}

// Add footer
$sql .= "-- Re-enable foreign key checks and commit\n";
$sql .= "COMMIT;\n";
$sql .= "SET FOREIGN_KEY_CHECKS = 1;\n";
$sql .= "SET SQL_MODE = '';\n";
$sql .= "SET AUTOCOMMIT = 1;\n\n";
$sql .= "-- COMPLETE DATABASE IMPORT FINISHED\n";
$sql .= "-- All " . count($tables) . " tables imported successfully!\n";

// Write file
$output_file = 'database_migration_complete_clean.sql';
if (file_put_contents($output_file, $sql)) {
    echo "\n✅ Complete SQL file created: $output_file\n";
    echo "   File size: " . number_format(strlen($sql)) . " bytes\n";
    echo "   Tables: " . count($tables) . "\n";
    
    // Copy to install folder
    if (copy($output_file, '../install/database_migration.sql')) {
        echo "   ✅ Also copied to install folder\n";
    }
    
    echo "\n🎉 COMPLETE DATABASE READY FOR SHARED HOSTING!\n";
    echo "===============================================\n";
    echo "✅ All tables included\n";
    echo "✅ Essential data included\n";
    echo "✅ No SUPER privileges required\n";
    echo "✅ No views, procedures, or triggers\n";
    echo "✅ Clean import guaranteed\n\n";
    echo "📥 Import $output_file to your shared hosting - no errors!\n";
    
} else {
    echo "❌ Error creating file\n";
}
?>
