<?php
/**
 * Create SQL with NO Foreign Keys for Shared Hosting
 * This removes ALL foreign key constraints to avoid shared hosting issues
 */

echo "🔧 Creating SQL with NO foreign keys for shared hosting...\n";
echo "========================================================\n\n";

// Database connection
$host = 'localhost';
$dbname = 'matchmaking';
$username = 'root';
$password = 'root';

try {
    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "✅ Connected to database\n\n";
} catch (PDOException $e) {
    die("❌ Database connection failed: " . $e->getMessage() . "\n");
}

// Get all tables (excluding views)
$stmt = $pdo->query("SHOW FULL TABLES WHERE Table_type = 'BASE TABLE'");
$tables = $stmt->fetchAll(PDO::FETCH_COLUMN);

echo "📊 Found " . count($tables) . " tables:\n";
foreach ($tables as $table) {
    echo "   - $table\n";
}
echo "\n";

// Start building SQL
$sql = "-- BAMBOO DATABASE - NO FOREIGN KEYS VERSION FOR SHARED HOSTING\n";
$sql .= "-- Generated: " . date('Y-m-d H:i:s') . "\n";
$sql .= "-- Tables: " . count($tables) . "\n";
$sql .= "-- NO foreign key constraints - 100% shared hosting compatible\n\n";

$sql .= "-- Disable foreign key checks (not needed but safe)\n";
$sql .= "SET FOREIGN_KEY_CHECKS = 0;\n";
$sql .= "SET SQL_MODE = 'NO_AUTO_VALUE_ON_ZERO';\n";
$sql .= "SET AUTOCOMMIT = 0;\n";
$sql .= "START TRANSACTION;\n\n";

// Drop tables first
$sql .= "-- Drop existing tables\n";
foreach (array_reverse($tables) as $table) {
    $sql .= "DROP TABLE IF EXISTS `$table`;\n";
}
$sql .= "\n";

// Create tables WITHOUT foreign keys
$sql .= "-- CREATE TABLES (NO FOREIGN KEYS)\n\n";
foreach ($tables as $table) {
    echo "   📋 Processing table: $table\n";
    
    // Get CREATE TABLE statement
    $stmt = $pdo->query("SHOW CREATE TABLE `$table`");
    $create_table = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($create_table) {
        $create_sql = $create_table['Create Table'];
        
        // Remove ALL foreign key constraints (more comprehensive)
        $create_sql = preg_replace('/,\s*CONSTRAINT\s+`[^`]+`\s+FOREIGN KEY.*?(?=,\s*(?:KEY|UNIQUE|PRIMARY|CONSTRAINT|\)))/is', '', $create_sql);
        $create_sql = preg_replace('/,\s*FOREIGN KEY.*?(?=,\s*(?:KEY|UNIQUE|PRIMARY|CONSTRAINT|\)))/is', '', $create_sql);

        // Remove any REFERENCES clauses
        $create_sql = preg_replace('/\)\s*REFERENCES\s+`[^`]+`\s*\([^)]+\)(?:\s*ON\s+(?:DELETE|UPDATE)\s+(?:CASCADE|SET NULL|RESTRICT|NO ACTION))?/i', ')', $create_sql);

        // Remove KEY references to foreign keys that might cause issues
        $create_sql = preg_replace('/,\s*KEY\s+`[^`]*_ibfk_\d+`[^,)]+/i', '', $create_sql);

        // Clean up any double commas and trailing references
        $create_sql = preg_replace('/,\s*,/', ',', $create_sql);
        $create_sql = preg_replace('/,\s*\)/', ')', $create_sql);
        $create_sql = preg_replace('/\)\s*REFERENCES.*$/', ')', $create_sql);
        
        // Add table comment
        $create_sql = str_replace('CREATE TABLE', "-- Table: $table (NO FOREIGN KEYS)\nCREATE TABLE", $create_sql);
        
        $sql .= $create_sql . ";\n\n";
    }
}

// Insert data for essential tables
$sql .= "-- INSERT ESSENTIAL DATA\n\n";

$essential_tables = ['admin_users', 'vip_levels', 'settings', 'product_categories'];

foreach ($essential_tables as $table) {
    if (in_array($table, $tables)) {
        echo "   💾 Getting data for: $table\n";
        
        $stmt = $pdo->query("SELECT * FROM `$table`");
        $rows = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        if (!empty($rows)) {
            $sql .= "-- Data for table: $table\n";
            
            // Get column names
            $columns = array_keys($rows[0]);
            $sql .= "INSERT INTO `$table` (`" . implode('`, `', $columns) . "`) VALUES\n";
            
            $values = [];
            foreach ($rows as $row) {
                $row_values = [];
                foreach ($row as $value) {
                    if ($value === null) {
                        $row_values[] = 'NULL';
                    } else {
                        // Escape single quotes and backslashes
                        $escaped_value = str_replace(['\\', "'"], ['\\\\', "\\'"], $value);
                        $row_values[] = "'" . $escaped_value . "'";
                    }
                }
                $values[] = "(" . implode(', ', $row_values) . ")";
            }
            
            $sql .= implode(",\n", $values) . ";\n\n";
        }
    }
}

// Add footer
$sql .= "-- Re-enable foreign key checks and commit\n";
$sql .= "COMMIT;\n";
$sql .= "SET FOREIGN_KEY_CHECKS = 1;\n";
$sql .= "SET SQL_MODE = '';\n";
$sql .= "SET AUTOCOMMIT = 1;\n\n";
$sql .= "-- NO FOREIGN KEYS DATABASE IMPORT FINISHED\n";
$sql .= "-- All " . count($tables) . " tables imported successfully!\n";
$sql .= "-- NO foreign key constraints - works on ANY shared hosting!\n";

// Write file
$output_file = 'database_migration_no_fk.sql';
if (file_put_contents($output_file, $sql)) {
    echo "\n✅ NO FOREIGN KEYS SQL file created: $output_file\n";
    echo "   File size: " . number_format(strlen($sql)) . " bytes\n";
    echo "   Tables: " . count($tables) . "\n";
    
    // Copy to install folder
    if (copy($output_file, '../install/database_migration.sql')) {
        echo "   ✅ Also copied to install folder\n";
    }
    
    echo "\n🎉 NO FOREIGN KEYS DATABASE READY!\n";
    echo "==================================\n";
    echo "✅ All " . count($tables) . " tables included\n";
    echo "✅ NO foreign key constraints\n";
    echo "✅ NO SUPER privileges required\n";
    echo "✅ Works on ANY shared hosting\n";
    echo "✅ Zero import errors guaranteed\n\n";
    echo "📥 Import $output_file - absolutely no errors!\n";
    
} else {
    echo "❌ Error creating file\n";
}
?>
