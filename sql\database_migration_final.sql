-- BAMBOO DATABASE - FINAL PERFECT VERSION
-- Generated: 2025-07-08 10:03:10
-- Perfect syntax, no SUPER privileges, guaranteed to work

SET FOREIGN_KEY_CHECKS = 0;
SET SQL_MODE = 'NO_AUTO_VALUE_ON_ZERO';
SET AUTOCOMMIT = 0;
START TRANSACTION;

-- Drop existing tables
DROP TABLE IF EXISTS `withdrawal_quotes`;
DROP TABLE IF EXISTS `vip_levels`;
DROP TABLE IF EXISTS `users`;
DROP TABLE IF EXISTS `user_sessions`;
DROP TABLE IF EXISTS `user_salaries`;
DROP TABLE IF EXISTS `transactions`;
DROP TABLE IF EXISTS `tasks`;
DROP TABLE IF EXISTS `superiors`;
DROP TABLE IF EXISTS `settings`;
DROP TABLE IF EXISTS `products`;
DROP TABLE IF EXISTS `product_categories`;
DROP TABLE IF EXISTS `notifications`;
DROP TABLE IF EXISTS `negative_settings`;
DROP TABLE IF EXISTS `customer_service_contacts`;
DROP TABLE IF EXISTS `admin_users`;

-- CREATE TABLES WITH PERFECT SYNTAX

-- Table: admin_users
CREATE TABLE `admin_users` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `username` varchar(50) NOT NULL,
  `email` varchar(100) NOT NULL,
  `full_name` varchar(100),
  `password_hash` varchar(255) NOT NULL,
  `role` enum('super_admin','admin','moderator') NOT NULL DEFAULT 'admin',
  `permissions` json,
  `last_login` timestamp,
  `status` enum('active','inactive') NOT NULL DEFAULT 'active',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `username` (`username`),
  KEY `email` (`email`),
  KEY `idx_username` (`username`),
  KEY `idx_email` (`email`),
  KEY `idx_role` (`role`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Table: customer_service_contacts
CREATE TABLE `customer_service_contacts` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `link` varchar(500) NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Table: negative_settings
CREATE TABLE `negative_settings` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `trigger_task_number` int(11) NOT NULL,
  `product_id_override` int(11) NOT NULL,
  `override_amount` decimal(15,2) NOT NULL,
  `is_active` tinyint(1) NOT NULL DEFAULT 1,
  `is_triggered` tinyint(1) NOT NULL DEFAULT 0,
  `admin_id_created` int(11) NOT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  KEY `product_id_override` (`product_id_override`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Table: notifications
CREATE TABLE `notifications` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `type` enum('system','user','admin','banner') NOT NULL,
  `title` varchar(255) NOT NULL,
  `message` text NOT NULL,
  `target_user_id` int(11),
  `target_vip_level` int(11),
  `is_global` tinyint(1) NOT NULL DEFAULT 0,
  `is_popup` tinyint(1) NOT NULL DEFAULT 0,
  `is_banner` tinyint(1) NOT NULL DEFAULT 0,
  `banner_color` varchar(7) DEFAULT '#007bff',
  `status` enum('active','inactive','scheduled') NOT NULL DEFAULT 'active',
  `start_date` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `end_date` timestamp,
  `read_count` int(11) NOT NULL DEFAULT 0,
  `created_by` int(11),
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `created_by` (`created_by`),
  KEY `idx_type` (`type`),
  KEY `idx_target_user_id` (`target_user_id`),
  KEY `idx_is_global` (`is_global`),
  KEY `idx_status` (`status`),
  KEY `idx_start_date` (`start_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Table: product_categories
CREATE TABLE `product_categories` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL,
  `description` text,
  `status` enum('active','inactive') NOT NULL DEFAULT 'active',
  `sort_order` int(11) NOT NULL DEFAULT 0,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Table: products
CREATE TABLE `products` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `description` text,
  `image_url` varchar(500),
  `price` decimal(10,2) NOT NULL,
  `commission_rate` decimal(5,2) NOT NULL DEFAULT 5.00,
  `category_id` int(11) NOT NULL,
  `min_vip_level` int(11) NOT NULL DEFAULT 1,
  `max_daily_assignments` int(11) NOT NULL DEFAULT 100,
  `weight` int(11) NOT NULL DEFAULT 1,
  `stock` int(11) NOT NULL DEFAULT 0,
  `status` enum('active','inactive','out_of_stock') NOT NULL DEFAULT 'active',
  `total_assignments` int(11) NOT NULL DEFAULT 0,
  `total_completions` int(11) NOT NULL DEFAULT 0,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_category` (`category_id`),
  KEY `idx_status` (`status`),
  KEY `idx_min_vip_level` (`min_vip_level`),
  KEY `idx_price` (`price`),
  KEY `idx_product_category_status` (`category_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Table: settings
CREATE TABLE `settings` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `key` varchar(100) NOT NULL,
  `value` text NOT NULL,
  `type` enum('string','integer','float','boolean','json') NOT NULL DEFAULT 'string',
  `description` text,
  `category` varchar(50) NOT NULL DEFAULT 'general',
  `is_public` tinyint(1) NOT NULL DEFAULT 0,
  `updated_by` int(11),
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `key` (`key`),
  KEY `updated_by` (`updated_by`),
  KEY `idx_key` (`key`),
  KEY `idx_category` (`category`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Table: superiors
CREATE TABLE `superiors` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL,
  `phone` varchar(32) NOT NULL,
  `email` varchar(100) NOT NULL,
  `invitation_code` varchar(32) NOT NULL,
  `invited_count` int(11) NOT NULL DEFAULT 0,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `invitation_code` (`invitation_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Table: tasks
CREATE TABLE `tasks` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `product_id` int(11) NOT NULL,
  `amount` decimal(10,2) NOT NULL DEFAULT 0.00,
  `commission_earned` decimal(10,2) NOT NULL DEFAULT 0.00,
  `base_commission` decimal(10,2) NOT NULL DEFAULT 0.00,
  `vip_bonus` decimal(10,2) NOT NULL DEFAULT 0.00,
  `status` enum('assigned','in_progress','completed','failed','expired') NOT NULL DEFAULT 'assigned',
  `assigned_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `started_at` timestamp,
  `completed_at` timestamp,
  `expires_at` timestamp,
  `submission_data` json,
  `admin_notes` text,
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_product_id` (`product_id`),
  KEY `idx_status` (`status`),
  KEY `idx_assigned_at` (`assigned_at`),
  KEY `idx_completed_at` (`completed_at`),
  KEY `idx_task_user_status` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Table: transactions
CREATE TABLE `transactions` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `order_no` varchar(32),
  `payment_channel` varchar(64),
  `credited_by` varchar(64),
  `state` varchar(32),
  `user_id` int(11) NOT NULL,
  `type` enum('deposit','withdrawal','commission','bonus','referral_bonus','penalty','adjustment','admin_credit','admin_deduction') NOT NULL,
  `amount` decimal(10,2) NOT NULL,
  `balance_before` decimal(10,2) NOT NULL DEFAULT 0.00,
  `balance_after` decimal(10,2) NOT NULL DEFAULT 0.00,
  `status` enum('pending','processing','completed','failed','cancelled') NOT NULL DEFAULT 'pending',
  `payment_method` varchar(50),
  `transaction_id` varchar(100),
  `external_transaction_id` varchar(255),
  `fee_amount` decimal(10,2) NOT NULL DEFAULT 0.00,
  `description` text,
  `admin_notes` text,
  `processed_by` int(11),
  `processed_at` timestamp,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `transaction_id` (`transaction_id`),
  KEY `idx_order_no` (`order_no`),
  KEY `processed_by` (`processed_by`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_type` (`type`),
  KEY `idx_status` (`status`),
  KEY `idx_transaction_id` (`transaction_id`),
  KEY `idx_created_at` (`created_at`),
  KEY `idx_transaction_user_type` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Table: user_salaries
CREATE TABLE `user_salaries` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `amount` decimal(15,2) NOT NULL,
  `status` enum('paid','pending_approval') NOT NULL,
  `admin_id_processed` int(11) NOT NULL,
  `paid_at` datetime,
  `notes` text,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Table: user_sessions
CREATE TABLE `user_sessions` (
  `id` varchar(128) NOT NULL,
  `user_id` int(11) NOT NULL,
  `ip_address` varchar(45) NOT NULL,
  `user_agent` text NOT NULL,
  `payload` longtext NOT NULL,
  `last_activity` int(11) NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_last_activity` (`last_activity`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Table: users
CREATE TABLE `users` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `username` varchar(50) NOT NULL,
  `phone` varchar(20) NOT NULL,
  `usdt_wallet_address` varchar(255),
  `exchange_name` varchar(100),
  `email` varchar(100),
  `avatar` varchar(255),
  `password_hash` varchar(255),
  `withdrawal_pin_hash` varchar(255),
  `gender` enum('male','female') NOT NULL,
  `invitation_code` varchar(20) NOT NULL,
  `invited_by` varchar(32),
  `balance` decimal(10,2) NOT NULL DEFAULT 0.00,
  `commission_balance` decimal(10,2) NOT NULL DEFAULT 0.00,
  `frozen_balance` decimal(10,2) NOT NULL DEFAULT 0.00,
  `total_deposited` decimal(10,2) NOT NULL DEFAULT 0.00,
  `total_withdrawn` decimal(10,2) NOT NULL DEFAULT 0.00,
  `total_commission_earned` decimal(10,2) NOT NULL DEFAULT 0.00,
  `vip_level` int(11) NOT NULL DEFAULT 1,
  `tasks_completed_today` int(11) NOT NULL DEFAULT 0,
  `last_task_date` date,
  `status` enum('pending','active','suspended','banned') NOT NULL DEFAULT 'pending',
  `email_verified` tinyint(1) NOT NULL DEFAULT 0,
  `phone_verified` tinyint(1) NOT NULL DEFAULT 0,
  `avatar_url` varchar(500),
  `referral_count` int(11) NOT NULL DEFAULT 0,
  `last_login` timestamp,
  `login_count` int(11) NOT NULL DEFAULT 0,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `username` (`username`),
  KEY `phone` (`phone`),
  KEY `invitation_code` (`invitation_code`),
  KEY `email` (`email`),
  KEY `idx_username` (`username`),
  KEY `idx_phone` (`phone`),
  KEY `idx_email` (`email`),
  KEY `idx_invitation_code` (`invitation_code`),
  KEY `idx_invited_by` (`invited_by`),
  KEY `idx_vip_level` (`vip_level`),
  KEY `idx_status` (`status`),
  KEY `idx_created_at` (`created_at`),
  KEY `idx_user_vip_status` (`vip_level`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Table: vip_levels
CREATE TABLE `vip_levels` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `level` int(11) NOT NULL,
  `name` varchar(50) NOT NULL,
  `min_balance` decimal(10,2) NOT NULL DEFAULT 0.00,
  `max_daily_tasks` int(11) NOT NULL DEFAULT 10,
  `commission_multiplier` decimal(3,2) NOT NULL DEFAULT 1.00,
  `withdrawal_limit_daily` decimal(10,2) NOT NULL DEFAULT 1000.00,
  `withdrawal_fee_percentage` decimal(5,2) NOT NULL DEFAULT 2.00,
  `benefits` text,
  `icon_path` varchar(255),
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `level` (`level`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Table: withdrawal_quotes
CREATE TABLE `withdrawal_quotes` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `message` text NOT NULL,
  `status` enum('active','resolved') NOT NULL DEFAULT 'active',
  `admin_id_created` int(11) NOT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- INSERT ESSENTIAL DATA

-- Data for table: admin_users
INSERT INTO `admin_users` (`id`, `username`, `email`, `full_name`, `password_hash`, `role`, `permissions`, `last_login`, `status`, `created_at`, `updated_at`) VALUES ('1', 'admin', '<EMAIL>', NULL, '$2y$10$Qu/guvBOUODqL6g1aQ/zYeZ26DmHqWj2rmsTHv107d6QNk21/eJ1u', 'super_admin', NULL, '2025-07-07 16:26:54', 'active', '2025-06-27 10:23:08', '2025-07-07 16:26:54');
INSERT INTO `admin_users` (`id`, `username`, `email`, `full_name`, `password_hash`, `role`, `permissions`, `last_login`, `status`, `created_at`, `updated_at`) VALUES ('2', 'thedemohomexx', '<EMAIL>', NULL, '$2y$10$1x.DXRM.oEJZBoGRwaaFPeUjNw2kxqjKyB5T5udDo.Z0EJb5sKTDq', 'super_admin', NULL, NULL, 'active', '2025-07-02 11:58:07', '2025-07-02 13:58:07');

-- Data for table: vip_levels
INSERT INTO `vip_levels` (`id`, `level`, `name`, `min_balance`, `max_daily_tasks`, `commission_multiplier`, `withdrawal_limit_daily`, `withdrawal_fee_percentage`, `benefits`, `icon_path`, `created_at`, `updated_at`) VALUES ('1', '1', 'VIP 1', '0.00', '5', '1.00', '100.00', '5.00', 'Basic access to platform', '685e60c79e5a0.png', '2025-06-27 10:23:08', '2025-06-27 11:13:43');
INSERT INTO `vip_levels` (`id`, `level`, `name`, `min_balance`, `max_daily_tasks`, `commission_multiplier`, `withdrawal_limit_daily`, `withdrawal_fee_percentage`, `benefits`, `icon_path`, `created_at`, `updated_at`) VALUES ('2', '2', 'VIP 2', '100.00', '10', '1.20', '500.00', '4.00', 'Unlimited access to all features', NULL, '2025-06-27 10:23:08', '2025-06-27 10:23:08');
INSERT INTO `vip_levels` (`id`, `level`, `name`, `min_balance`, `max_daily_tasks`, `commission_multiplier`, `withdrawal_limit_daily`, `withdrawal_fee_percentage`, `benefits`, `icon_path`, `created_at`, `updated_at`) VALUES ('3', '3', 'VIP 3', '500.00', '15', '1.50', '1000.00', '3.00', 'Premium features and higher commissions', NULL, '2025-06-27 10:23:08', '2025-06-27 10:23:08');
INSERT INTO `vip_levels` (`id`, `level`, `name`, `min_balance`, `max_daily_tasks`, `commission_multiplier`, `withdrawal_limit_daily`, `withdrawal_fee_percentage`, `benefits`, `icon_path`, `created_at`, `updated_at`) VALUES ('4', '4', 'VIP 4', '1000.00', '20', '1.80', '2000.00', '2.50', 'Elite status with maximum benefits', NULL, '2025-06-27 10:23:08', '2025-06-27 10:23:08');
INSERT INTO `vip_levels` (`id`, `level`, `name`, `min_balance`, `max_daily_tasks`, `commission_multiplier`, `withdrawal_limit_daily`, `withdrawal_fee_percentage`, `benefits`, `icon_path`, `created_at`, `updated_at`) VALUES ('5', '5', 'VIP 5', '2500.00', '30', '2.00', '5000.00', '2.00', 'Ultimate VIP with highest rewards', NULL, '2025-06-27 10:23:08', '2025-06-27 10:23:08');

-- Data for table: settings
INSERT INTO `settings` (`id`, `key`, `value`, `type`, `description`, `category`, `is_public`, `updated_by`, `created_at`, `updated_at`) VALUES ('1', 'app_name', 'Kompyte', 'string', 'Application name', 'general', '1', NULL, '2025-06-27 10:23:09', '2025-06-28 10:02:47');
INSERT INTO `settings` (`id`, `key`, `value`, `type`, `description`, `category`, `is_public`, `updated_by`, `created_at`, `updated_at`) VALUES ('2', 'app_logo', '6861392b1bf47.png', 'string', 'Application logo URL', 'general', '1', NULL, '2025-06-27 10:23:09', '2025-06-29 15:01:31');
INSERT INTO `settings` (`id`, `key`, `value`, `type`, `description`, `category`, `is_public`, `updated_by`, `created_at`, `updated_at`) VALUES ('3', 'welcome_bonus', '10.00', 'float', 'Welcome bonus for new users', 'financial', '0', NULL, '2025-06-27 10:23:09', '2025-06-27 10:23:09');
INSERT INTO `settings` (`id`, `key`, `value`, `type`, `description`, `category`, `is_public`, `updated_by`, `created_at`, `updated_at`) VALUES ('4', 'min_withdrawal', '20.00', 'float', 'Minimum withdrawal amount', 'financial', '1', NULL, '2025-06-27 10:23:09', '2025-06-27 10:23:09');
INSERT INTO `settings` (`id`, `key`, `value`, `type`, `description`, `category`, `is_public`, `updated_by`, `created_at`, `updated_at`) VALUES ('5', 'max_withdrawal_daily', '1000.00', 'float', 'Maximum daily withdrawal', 'financial', '1', NULL, '2025-06-27 10:23:09', '2025-06-27 10:23:09');
INSERT INTO `settings` (`id`, `key`, `value`, `type`, `description`, `category`, `is_public`, `updated_by`, `created_at`, `updated_at`) VALUES ('6', 'negative_balance_trigger', '-50.00', 'float', 'Balance threshold to trigger negative balance', 'financial', '0', NULL, '2025-06-27 10:23:09', '2025-06-27 10:23:09');
INSERT INTO `settings` (`id`, `key`, `value`, `type`, `description`, `category`, `is_public`, `updated_by`, `created_at`, `updated_at`) VALUES ('7', 'task_expiry_hours', '24', 'integer', 'Hours before task expires', 'tasks', '0', NULL, '2025-06-27 10:23:09', '2025-06-27 10:23:09');
INSERT INTO `settings` (`id`, `key`, `value`, `type`, `description`, `category`, `is_public`, `updated_by`, `created_at`, `updated_at`) VALUES ('8', 'max_tasks_per_day', '10', 'integer', 'Maximum tasks per day for VIP 1', 'tasks', '0', NULL, '2025-06-27 10:23:09', '2025-06-27 10:23:09');
INSERT INTO `settings` (`id`, `key`, `value`, `type`, `description`, `category`, `is_public`, `updated_by`, `created_at`, `updated_at`) VALUES ('9', 'referral_commission_rate', '6', 'float', 'Referral commission percentage', 'referral', '0', NULL, '2025-06-27 10:23:09', '2025-06-28 01:31:56');
INSERT INTO `settings` (`id`, `key`, `value`, `type`, `description`, `category`, `is_public`, `updated_by`, `created_at`, `updated_at`) VALUES ('10', 'maintenance_mode', 'false', 'boolean', 'Enable maintenance mode', 'system', '0', NULL, '2025-06-27 10:23:09', '2025-06-27 10:23:09');
INSERT INTO `settings` (`id`, `key`, `value`, `type`, `description`, `category`, `is_public`, `updated_by`, `created_at`, `updated_at`) VALUES ('11', 'registration_enabled', 'true', 'boolean', 'Enable user registration', 'system', '1', NULL, '2025-06-27 10:23:09', '2025-06-27 10:23:09');
INSERT INTO `settings` (`id`, `key`, `value`, `type`, `description`, `category`, `is_public`, `updated_by`, `created_at`, `updated_at`) VALUES ('12', 'welcome_popup_text', 'Welcome to Bamboo! During our anniversary celebration, new users can get a bonus for the first time to complete group tasks.', 'string', 'Welcome popup message', 'general', '1', NULL, '2025-06-27 10:23:09', '2025-06-27 10:23:09');
INSERT INTO `settings` (`id`, `key`, `value`, `type`, `description`, `category`, `is_public`, `updated_by`, `created_at`, `updated_at`) VALUES ('13', 'notification_banner', 'Welcome to Bamboo, if any assistance, please seek help from our customer service. Have a nice day!', 'string', 'Main notification banner', 'general', '1', NULL, '2025-06-27 10:23:09', '2025-06-27 10:23:09');
INSERT INTO `settings` (`id`, `key`, `value`, `type`, `description`, `category`, `is_public`, `updated_by`, `created_at`, `updated_at`) VALUES ('14', 'appearance_primary_color', '#f07041', 'string', NULL, 'general', '0', NULL, '2025-06-27 19:00:17', '2025-06-29 17:44:03');
INSERT INTO `settings` (`id`, `key`, `value`, `type`, `description`, `category`, `is_public`, `updated_by`, `created_at`, `updated_at`) VALUES ('15', 'appearance_secondary_color', '#fea4ee', 'string', NULL, 'general', '0', NULL, '2025-06-27 19:00:17', '2025-06-28 21:12:52');
INSERT INTO `settings` (`id`, `key`, `value`, `type`, `description`, `category`, `is_public`, `updated_by`, `created_at`, `updated_at`) VALUES ('16', 'appearance_accent_color', '#ffffff', 'string', NULL, 'general', '0', NULL, '2025-06-27 19:00:17', '2025-06-28 21:16:55');
INSERT INTO `settings` (`id`, `key`, `value`, `type`, `description`, `category`, `is_public`, `updated_by`, `created_at`, `updated_at`) VALUES ('17', 'appearance_gradient_start', '#000000', 'string', NULL, 'general', '0', NULL, '2025-06-27 19:00:17', '2025-07-07 16:30:48');
INSERT INTO `settings` (`id`, `key`, `value`, `type`, `description`, `category`, `is_public`, `updated_by`, `created_at`, `updated_at`) VALUES ('18', 'appearance_gradient_end', '#c43a08', 'string', NULL, 'general', '0', NULL, '2025-06-27 19:00:17', '2025-07-07 16:30:48');
INSERT INTO `settings` (`id`, `key`, `value`, `type`, `description`, `category`, `is_public`, `updated_by`, `created_at`, `updated_at`) VALUES ('19', 'appearance_card_background', '#ffffff', 'string', NULL, 'general', '0', NULL, '2025-06-27 19:00:17', '2025-06-28 18:34:36');
INSERT INTO `settings` (`id`, `key`, `value`, `type`, `description`, `category`, `is_public`, `updated_by`, `created_at`, `updated_at`) VALUES ('20', 'appearance_sidebar_style', 'gradient', 'string', NULL, 'general', '0', NULL, '2025-06-27 19:00:18', '2025-06-27 19:00:18');
INSERT INTO `settings` (`id`, `key`, `value`, `type`, `description`, `category`, `is_public`, `updated_by`, `created_at`, `updated_at`) VALUES ('21', 'appearance_card_shadow', 'medium', 'string', NULL, 'general', '0', NULL, '2025-06-27 19:00:18', '2025-06-28 20:33:26');
INSERT INTO `settings` (`id`, `key`, `value`, `type`, `description`, `category`, `is_public`, `updated_by`, `created_at`, `updated_at`) VALUES ('22', 'appearance_border_radius', '0.5rem', 'string', NULL, 'general', '0', NULL, '2025-06-27 19:00:18', '2025-06-28 17:14:57');
INSERT INTO `settings` (`id`, `key`, `value`, `type`, `description`, `category`, `is_public`, `updated_by`, `created_at`, `updated_at`) VALUES ('23', 'appearance_theme_mode', 'light', 'string', NULL, 'general', '0', NULL, '2025-06-27 19:00:18', '2025-06-28 13:40:46');
INSERT INTO `settings` (`id`, `key`, `value`, `type`, `description`, `category`, `is_public`, `updated_by`, `created_at`, `updated_at`) VALUES ('54', 'withdrawal_policy_text', 'dd', 'string', NULL, 'general', '0', NULL, '2025-06-27 19:19:49', '2025-06-27 19:19:49');
INSERT INTO `settings` (`id`, `key`, `value`, `type`, `description`, `category`, `is_public`, `updated_by`, `created_at`, `updated_at`) VALUES ('56', 'contact_email', '<EMAIL>', 'string', NULL, 'general', '0', NULL, '2025-06-27 22:53:09', '2025-06-29 16:49:55');
INSERT INTO `settings` (`id`, `key`, `value`, `type`, `description`, `category`, `is_public`, `updated_by`, `created_at`, `updated_at`) VALUES ('57', 'contact_phone', '1010010101111', 'string', NULL, 'general', '0', NULL, '2025-06-27 22:53:09', '2025-06-29 16:57:37');
INSERT INTO `settings` (`id`, `key`, `value`, `type`, `description`, `category`, `is_public`, `updated_by`, `created_at`, `updated_at`) VALUES ('58', 'address', '11 Test klento roadx', 'string', NULL, 'general', '0', NULL, '2025-06-27 22:53:09', '2025-06-29 16:57:37');
INSERT INTO `settings` (`id`, `key`, `value`, `type`, `description`, `category`, `is_public`, `updated_by`, `created_at`, `updated_at`) VALUES ('59', 'default_currency', '$', 'string', NULL, 'general', '0', NULL, '2025-06-27 22:53:09', '2025-06-27 22:53:09');
INSERT INTO `settings` (`id`, `key`, `value`, `type`, `description`, `category`, `is_public`, `updated_by`, `created_at`, `updated_at`) VALUES ('60', 'decimal_places', '2', 'string', NULL, 'general', '0', NULL, '2025-06-27 22:53:09', '2025-06-27 22:53:09');
INSERT INTO `settings` (`id`, `key`, `value`, `type`, `description`, `category`, `is_public`, `updated_by`, `created_at`, `updated_at`) VALUES ('61', 'email_verification_required', '1', 'string', NULL, 'general', '0', NULL, '2025-06-27 22:53:09', '2025-06-27 22:53:09');
INSERT INTO `settings` (`id`, `key`, `value`, `type`, `description`, `category`, `is_public`, `updated_by`, `created_at`, `updated_at`) VALUES ('62', 'opening_hours', '9', 'string', NULL, 'general', '0', NULL, '2025-06-27 22:53:09', '2025-06-27 22:53:09');
INSERT INTO `settings` (`id`, `key`, `value`, `type`, `description`, `category`, `is_public`, `updated_by`, `created_at`, `updated_at`) VALUES ('63', 'closing_hours', '21', 'string', NULL, 'general', '0', NULL, '2025-06-27 22:53:09', '2025-06-27 22:53:09');
INSERT INTO `settings` (`id`, `key`, `value`, `type`, `description`, `category`, `is_public`, `updated_by`, `created_at`, `updated_at`) VALUES ('64', 'signup_bonus', '100', 'string', NULL, 'general', '0', NULL, '2025-06-27 22:53:10', '2025-06-29 16:56:32');
INSERT INTO `settings` (`id`, `key`, `value`, `type`, `description`, `category`, `is_public`, `updated_by`, `created_at`, `updated_at`) VALUES ('65', 'min_wallet_balance', '50', 'string', NULL, 'general', '0', NULL, '2025-06-27 22:53:10', '2025-06-29 16:56:32');
INSERT INTO `settings` (`id`, `key`, `value`, `type`, `description`, `category`, `is_public`, `updated_by`, `created_at`, `updated_at`) VALUES ('66', 'contract_terms', 'Contract Rules

1) To optimize and reset your account, you must first complete all ratings with a minimum amount of 100 USDT and a minimum account reset amount of  100 USDT. 

1.1) If you need to reset your account, you must contact our online service to reset your account after you have completed all your optimization and withdrawn your funds.

2) User withdrawals and system withdrawal requirements / security of user funds

2.1) Each user needs to complete all the optimization rating before they can meet the system withdrawal requirements

2.2) In order to avoid any loss of funds, all withdrawals are processed automatically by the system and not manually.

2.3) All users are not allowed to apply for withdrawal in the middle of task to avoid affecting the merchant\'s operation

2.4) Users\' funds are completely safe on the Platform and the Platform will be liable for any accidental loss

(3) Please do not disclose your account password and withdrawal password to others. The platform will not be held responsible for any loss or damage caused.

3.1) All users are advised to keep their accounts secure to avoid disclosure 

(3.2) The Platform is not responsible for any accidental disclosure of accounts

3.3) Because of the financial implications of the accounts, it is important not to disclose them to avoid unnecessary problems.

3.4) Withdrawal password It is recommended that you do not set a birthday password, ID card number or mobile phone number, etc. It is recommended that you set a more difficult password to protect your funds.

3.5) If you forget your password, you can reset it by contacting the online service and be sure to change it yourself afterwards.

(4) Optimization rating are randomly assigned by the system and therefore cannot be changed, canceled, controlled or skipped in any way

4.1) Due to a large number of users on the platform, it is not possible to distribute group purchase items manually, so all group purchase items are distributed randomly by the system.

(4.2) Group purchase/combination items are randomly released by the system and cannot be changed/cancelled/skipped by any user/staff.

5. Legal action will be taken in the event of misuse of the account

6. Each item comes from a different merchant, no deposit for more than 10 minutes, and each deposit must be made with the online service to confirm the merchant\'s cryptocurrency wallet address

7. The platform will not be held responsible for any deposits made to the wrong wallet address

8. Each time optimization rating, the user is required to complete it within 2   hours, if it is not completed without notifying the merchant to apply for a time extension, resulting in complaints from the merchant, the user is liable for breach of contract

', 'string', NULL, 'general', '0', NULL, '2025-06-27 22:53:10', '2025-06-28 13:10:08');
INSERT INTO `settings` (`id`, `key`, `value`, `type`, `description`, `category`, `is_public`, `updated_by`, `created_at`, `updated_at`) VALUES ('67', 'about_us', 'ABOUT US

Bamboo has been a trusted partner in the truest sense of the word.
We approach growth from multiple angles.
Most agencies only focus on channel management to determine success.
This is no longer enough. Bamboo looks beyond the channels to understand, forecast, and make smarter investments over time', 'string', NULL, 'general', '0', NULL, '2025-06-27 22:53:10', '2025-06-28 13:10:08');
INSERT INTO `settings` (`id`, `key`, `value`, `type`, `description`, `category`, `is_public`, `updated_by`, `created_at`, `updated_at`) VALUES ('68', 'faq_content', 'I. Start Product Optimization Task 
1.1) Minimum account balance of 100 USDT for the first 40 tasks/set 
1.2) A minimum renewal of 100 USDT is required to start the tasks
1.3) Once one set of tasks has been completed, the user must request a full withdrawal and receive the withdrawal amount before requesting to reset the account.

II. Withdrawal
2.1) Full withdrawal amount can be requested after completing 1 group of task
2.2) Users need to complete 1 group of tasks before they can request a withdrawal.
2.3) You cannot request a withdrawal or refund if you choose to give up or withdraw in the middle of a task optimization. 
2.4) No withdrawals can be processed if the user\'s withdrawal request has not been received.
2.5) If the withdrawal amount is 30000 USDT or above, please contact our online customer service to make a withdrawal. 

III. Funds
3.1) All funds will be held securely in the user\'s account and can be requested in full once all data has been completed
3.2) To avoid any loss of funds, all data will be processed by the system and not manually
3.3) The platform will take full responsibility for any accidental loss of funds.
3.4) If the user\'s funds exceed the taxable amount of the UK government, the user will need to pay tax

IV. Account Security
(4.1) Please do not disclose your login password and withdrawal password to others, as the platform will not be held responsible for any loss.
(4.2) Users are not recommended to set their birthday password, ID card number, or mobile phone number as their withdrawal password or login password.
4.3) If you forget your login password or withdrawal password, please contact our online service to reset it.

V. General Product Data
5.1) Platform earnings are divided into normal earnings and  quintuple earnings
5.2) Normal users will earn 0.5% of the profit for each normal task of optimizing
5.3) VIP 2 users will earn 1% of the profit for each normal task of optimizing
5.4) VIP 3 users will earn 1.5% of the profit for each normal task of optimizing
5.5) VIP 4 users will earn 2% of the profit for each normal task of optimizing
5.6) Funds and earnings will be refunded to the user\'s account for each completed task of optimizing
5.7) The system will randomly allocate tasks to the user\'s account based on the total amount in the user\'s account
5.8) Once the tasks of optimise product have been allocated to the user\'s account they cannot be cancelled or skipped

VI. Combined Product Data
6.1) Combined Product Data is composed of 0 to 3 data, the user may not necessarily get 3 products, the system will randomly allocate the data in the combined product the user has a higher chance of getting 1 or 2 product
6.2) Users will receive five times the profit for each product data combined  than for the general product data
6.3) Once you have received the combined product data, all funds will stop rolling and will be returned to your account after you have completed each product data in the combined.
6.4) The system will randomly allocate the combined product data to the user\'s account according to the total balance on the user\'s account
6.5) Once the combined product data has been allocated to the user\'s account, it cannot be cancelled or skipped.

VII. Deposit
(7.1) The amount of the deposit is the choice of the user, we cannot decide the amount of the deposit for the user, we suggest that the user can make the advance according to his ability or after he is familiar with the platform.
(7.2) If a user needs to make a deposit when receiving combined product data, it is recommended that the user be able to make an advance based on the insufficient amount shown on the account.
7.3) Before making an advance, you must request an advance from the online service and confirm the deposit detail.
7.4) The platform will not be held responsible if the user deposits the wrong deposit detail.

VIII. Merchant Cooperation
(8.1) There are different products on the platform at each time, if the product is not optimized for a long period, the merchant will not be able to offload the task, which will affect the merchant product optimization progress, it is recommended that the user completes all product optimization and applies for a withdrawal as soon as possible to avoid affecting the merchant progress
(8.2) The merchant will provide a deposit detail for the user to make a deposit 
(8.3) Any delay in completing the product optimization will be detrimental to the merchant and the process
(8.4) User does not deal with it for a long time, the merchant has the right to complain about the user\'s account, resulting in a bad reputation for the user\'s account

IX. Invitation
9.1) Users will be able to invite other users using the invitation code on their account
9.2) If the account has not yet completed all the data, it is not possible to invite other users
9.3) The referrer will receive 20% of the referee\'s total earnings for the day

X. Operating hours
(10.1) Platform operating hours 9:00 to 21:59
(10.2) Online service operating hours 9:00 to 21:59
(10.3) Platform withdrawal hour 9:00 to 21:59', 'string', NULL, 'general', '0', NULL, '2025-06-27 22:53:10', '2025-06-28 13:10:08');
INSERT INTO `settings` (`id`, `key`, `value`, `type`, `description`, `category`, `is_public`, `updated_by`, `created_at`, `updated_at`) VALUES ('69', 'latest_events', 'I. Start Product Optimization Task 
1.1) Minimum account balance of 100 USDT for the first 40 tasks/set 
1.2) A minimum renewal of 100USDT is required to start the tasks
1.3) Once one set of tasks has been completed, the user must request a full withdrawal and receive the withdrawal amount before requesting to reset the account.

II. Withdrawal
2.1) Full withdrawal amount can be requested after completing 1 group of task
2.2) Users need to complete 1 group of tasks before they can request a withdrawal.
2.3) You cannot request a withdrawal or refund if you choose to give up or withdraw in the middle of a task optimization. 
2.4) No withdrawals can be processed if the user\'s withdrawal request has not been received.
2.5) If the withdrawal amount is 30000 USDT or above, please contact our online customer service to make a withdrawal. 

III. Funds
3.1) All funds will be held securely in the user\'s account and can be requested in full once all data has been completed
3.2) To avoid any loss of funds, all data will be processed by the system and not manually
3.3) The platform will take full responsibility for any accidental loss of funds.
3.4) If the user\'s funds exceed the taxable amount of the UK government, the user will need to pay tax

IV. Account Security
(4.1) Please do not disclose your login password and withdrawal password to others, as the platform will not be held responsible for any loss.
(4.2) Users are not recommended to set their birthday password, ID card number, or mobile phone number as their withdrawal password or login password.
4.3) If you forget your login password or withdrawal password, please contact our online service to reset it.

V. General Product Data
5.1) Platform earnings are divided into normal earnings and  quintuple earnings
5.2) Normal users will earn 0.5% of the profit for each normal task of optimizing
5.3) VIP 2 users will earn 1% of the profit for each normal task of optimizing
5.4) VIP 3 users will earn 1.5% of the profit for each normal task of optimizing
5.5) VIP 4 users will earn 2% of the profit for each normal task of optimizing
5.6) Funds and earnings will be refunded to the user\'s account for each completed task of optimizing
5.7) The system will randomly allocate tasks to the user\'s account based on the total amount in the user\'s account
5.8) Once the tasks of optimise product have been allocated to the user\'s account they cannot be cancelled or skipped

VI. Combined Product Data
6.1) Combined Product Data is composed of 0 to 3 data, the user may not necessarily get 3 products, the system will randomly allocate the data in the combined product the user has a higher chance of getting 1 or 2 product
6.2) Users will receive five times the profit for each product data combined  than for the general product data
6.3) Once you have received the combined product data, all funds will stop rolling and will be returned to your account after you have completed each product data in the combined.
6.4) The system will randomly allocate the combined product data to the user\'s account according to the total balance on the user\'s account
6.5) Once the combined product data has been allocated to the user\'s account, it cannot be cancelled or skipped.

VII. Deposit
(7.1) The amount of the deposit is the choice of the user, we cannot decide the amount of the deposit for the user, we suggest that the user can make the advance according to his ability or after he is familiar with the platform.
(7.2) If a user needs to make a deposit when receiving combined product data, it is recommended that the user be able to make an advance based on the insufficient amount shown on the account.
7.3) Before making an advance, you must request an advance from the online service and confirm the deposit detail.
7.4) The platform will not be held responsible if the user deposits the wrong deposit detail.

VIII. Merchant Cooperation
(8.1) There are different products on the platform at each time, if the product is not optimized for a long period, the merchant will not be able to offload the task, which will affect the merchant product optimization progress, it is recommended that the user completes all product optimization and applies for a withdrawal as soon as possible to avoid affecting the merchant progress
(8.2) The merchant will provide a deposit detail for the user to make a deposit 
(8.3) Any delay in completing the product optimization will be detrimental to the merchant and the process
(8.4) User does not deal with it for a long time, the merchant has the right to complain about the user\'s account, resulting in a bad reputation for the user\'s account

IX. Invitation
9.1) Users will be able to invite other users using the invitation code on their account
9.2) If the account has not yet completed all the data, it is not possible to invite other users
9.3) The referrer will receive 20% of the referee\'s total earnings for the day

X. Operating hours
(10.1) Platform operating hours 9:00 to 21:59
(10.2) Online service operating hours 9:00 to 21:59
(10.3) Platform withdrawal hour 9:00 to 21:59', 'string', NULL, 'general', '0', NULL, '2025-06-27 22:53:10', '2025-06-28 13:10:08');
INSERT INTO `settings` (`id`, `key`, `value`, `type`, `description`, `category`, `is_public`, `updated_by`, `created_at`, `updated_at`) VALUES ('70', 'user_registration_agreement', 'Contract Rules

1) To optimize and reset your account, you must first complete all ratings with a minimum amount of 100 USDT and a minimum account reset amount of  100 USDT. 

1.1) If you need to reset your account, you must contact our online service to reset your account after you have completed all your optimization and withdrawn your funds.

2) User withdrawals and system withdrawal requirements / security of user funds

2.1) Each user needs to complete all the optimization rating before they can meet the system withdrawal requirements

2.2) In order to avoid any loss of funds, all withdrawals are processed automatically by the system and not manually.

2.3) All users are not allowed to apply for withdrawal in the middle of task to avoid affecting the merchant\'s operation

2.4) Users\' funds are completely safe on the Platform and the Platform will be liable for any accidental loss

(3) Please do not disclose your account password and withdrawal password to others. The platform will not be held responsible for any loss or damage caused.

3.1) All users are advised to keep their accounts secure to avoid disclosure 

(3.2) The Platform is not responsible for any accidental disclosure of accounts

3.3) Because of the financial implications of the accounts, it is important not to disclose them to avoid unnecessary problems.

3.4) Withdrawal password It is recommended that you do not set a birthday password, ID card number or mobile phone number, etc. It is recommended that you set a more difficult password to protect your funds.

3.5) If you forget your password, you can reset it by contacting the online service and be sure to change it yourself afterwards.

(4) Optimization rating are randomly assigned by the system and therefore cannot be changed, canceled, controlled or skipped in any way

4.1) Due to a large number of users on the platform, it is not possible to distribute group purchase items manually, so all group purchase items are distributed randomly by the system.

(4.2) Group purchase/combination items are randomly released by the system and cannot be changed/cancelled/skipped by any user/staff.

5. Legal action will be taken in the event of misuse of the account

6. Each item comes from a different merchant, no deposit for more than 10 minutes, and each deposit must be made with the online service to confirm the merchant\'s cryptocurrency wallet address

7. The platform will not be held responsible for any deposits made to the wrong wallet address

8. Each time optimization rating, the user is required to complete it within 2   hours, if it is not completed without notifying the merchant to apply for a time extension, resulting in complaints from the merchant, the user is liable for breach of contract

', 'string', NULL, 'general', '0', NULL, '2025-06-27 22:53:10', '2025-06-28 13:10:08');
INSERT INTO `settings` (`id`, `key`, `value`, `type`, `description`, `category`, `is_public`, `updated_by`, `created_at`, `updated_at`) VALUES ('71', 'app_certificate', '685f11ec6b5ee.pdf', 'string', NULL, 'general', '0', NULL, '2025-06-27 22:58:02', '2025-06-27 23:49:32');
INSERT INTO `settings` (`id`, `key`, `value`, `type`, `description`, `category`, `is_public`, `updated_by`, `created_at`, `updated_at`) VALUES ('331', 'appearance_button_style', 'primary', 'string', NULL, 'general', '0', NULL, '2025-06-28 13:40:46', '2025-06-28 13:41:02');
INSERT INTO `settings` (`id`, `key`, `value`, `type`, `description`, `category`, `is_public`, `updated_by`, `created_at`, `updated_at`) VALUES ('384', 'appearance_sidebar_text_color', '#ffffff', 'string', NULL, 'general', '0', NULL, '2025-06-28 15:49:22', '2025-07-07 16:31:00');
INSERT INTO `settings` (`id`, `key`, `value`, `type`, `description`, `category`, `is_public`, `updated_by`, `created_at`, `updated_at`) VALUES ('638', 'footer_text', '© 2025 Company Name. All rights reserved.', 'string', NULL, 'general', '0', NULL, '2025-06-28 17:38:06', '2025-06-28 17:38:06');
INSERT INTO `settings` (`id`, `key`, `value`, `type`, `description`, `category`, `is_public`, `updated_by`, `created_at`, `updated_at`) VALUES ('639', 'footer_company_name', 'Kompyte', 'string', NULL, 'general', '0', NULL, '2025-06-28 17:38:06', '2025-06-28 17:38:06');
INSERT INTO `settings` (`id`, `key`, `value`, `type`, `description`, `category`, `is_public`, `updated_by`, `created_at`, `updated_at`) VALUES ('640', 'footer_version', 'v1.0', 'string', NULL, 'general', '0', NULL, '2025-06-28 17:38:06', '2025-06-28 17:38:06');
INSERT INTO `settings` (`id`, `key`, `value`, `type`, `description`, `category`, `is_public`, `updated_by`, `created_at`, `updated_at`) VALUES ('652', 'appearance_logo_size', 'extra-large', 'string', NULL, 'general', '0', NULL, '2025-06-28 18:11:26', '2025-06-28 18:11:26');
INSERT INTO `settings` (`id`, `key`, `value`, `type`, `description`, `category`, `is_public`, `updated_by`, `created_at`, `updated_at`) VALUES ('653', 'appearance_table_header_color', 'accent', 'string', NULL, 'general', '0', NULL, '2025-06-28 18:11:26', '2025-06-28 18:35:35');
INSERT INTO `settings` (`id`, `key`, `value`, `type`, `description`, `category`, `is_public`, `updated_by`, `created_at`, `updated_at`) VALUES ('654', 'appearance_level_badge_color', 'primary', 'string', NULL, 'general', '0', NULL, '2025-06-28 18:11:26', '2025-06-28 21:17:24');
INSERT INTO `settings` (`id`, `key`, `value`, `type`, `description`, `category`, `is_public`, `updated_by`, `created_at`, `updated_at`) VALUES ('700', 'appearance_footer_bg_color', '#f8f9fa', 'string', NULL, 'general', '0', NULL, '2025-06-28 18:30:03', '2025-06-28 18:30:03');
INSERT INTO `settings` (`id`, `key`, `value`, `type`, `description`, `category`, `is_public`, `updated_by`, `created_at`, `updated_at`) VALUES ('701', 'appearance_footer_text_color', '#6c757d', 'string', NULL, 'general', '0', NULL, '2025-06-28 18:30:03', '2025-06-28 18:30:03');
INSERT INTO `settings` (`id`, `key`, `value`, `type`, `description`, `category`, `is_public`, `updated_by`, `created_at`, `updated_at`) VALUES ('801', 'appearance_table_header_text_color', '#fe8b4d', 'string', NULL, 'general', '0', NULL, '2025-06-28 21:12:53', '2025-06-28 21:12:53');

-- Data for table: product_categories
INSERT INTO `product_categories` (`id`, `name`, `description`, `status`, `sort_order`, `created_at`, `updated_at`) VALUES ('1', 'Electronics', 'Electronic devices and gadgets', 'active', '0', '2025-06-27 10:23:08', '2025-06-27 10:23:08');
INSERT INTO `product_categories` (`id`, `name`, `description`, `status`, `sort_order`, `created_at`, `updated_at`) VALUES ('2', 'Fashion', 'Clothing and accessories', 'active', '0', '2025-06-27 10:23:08', '2025-06-27 10:23:08');
INSERT INTO `product_categories` (`id`, `name`, `description`, `status`, `sort_order`, `created_at`, `updated_at`) VALUES ('3', 'Home & Garden', 'Home improvement and garden items', 'active', '0', '2025-06-27 10:23:08', '2025-06-27 10:23:08');
INSERT INTO `product_categories` (`id`, `name`, `description`, `status`, `sort_order`, `created_at`, `updated_at`) VALUES ('4', 'Sports', 'Sports and fitness equipment', 'active', '0', '2025-06-27 10:23:08', '2025-06-27 10:23:08');
INSERT INTO `product_categories` (`id`, `name`, `description`, `status`, `sort_order`, `created_at`, `updated_at`) VALUES ('5', 'Beauty', 'Beauty and personal care products', 'active', '0', '2025-06-27 10:23:08', '2025-06-27 10:23:08');

-- Finalize
COMMIT;
SET FOREIGN_KEY_CHECKS = 1;
SET AUTOCOMMIT = 1;

-- PERFECT IMPORT COMPLETE!
