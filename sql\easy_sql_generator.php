<?php
/**
 * EASY SQL GENERATOR - One-Click Database Export
 * Simple tool to generate perfect shared hosting SQL files
 */

echo "🚀 EASY SQL GENERATOR - One-Click Export\n";
echo "========================================\n\n";

// Database connection
$host = 'localhost';
$dbname = 'matchmaking';
$username = 'root';
$password = 'root';

try {
    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "✅ Connected to database: $dbname\n\n";
} catch (PDOException $e) {
    die("❌ Database connection failed: " . $e->getMessage() . "\n");
}

// Get all tables (excluding views)
$stmt = $pdo->query("SHOW FULL TABLES WHERE Table_type = 'BASE TABLE'");
$tables = $stmt->fetchAll(PDO::FETCH_COLUMN);

// Get all views
$stmt = $pdo->query("SHOW FULL TABLES WHERE Table_type = 'VIEW'");
$views = $stmt->fetchAll(PDO::FETCH_COLUMN);

echo "📊 Database Analysis:\n";
echo "   Tables: " . count($tables) . "\n";
echo "   Views: " . count($views) . " (will be converted to tables)\n\n";

// Start building SQL
$timestamp = date('Y-m-d_H-i-s');
$output_file = "bamboo_database_$timestamp.sql";

$sql = "-- BAMBOO DATABASE - EASY EXPORT\n";
$sql .= "-- Generated: " . date('Y-m-d H:i:s') . "\n";
$sql .= "-- Tables: " . count($tables) . " + " . count($views) . " (converted views)\n";
$sql .= "-- 100% Shared hosting compatible\n\n";

$sql .= "SET FOREIGN_KEY_CHECKS = 0;\n";
$sql .= "SET SQL_MODE = 'NO_AUTO_VALUE_ON_ZERO';\n";
$sql .= "SET AUTOCOMMIT = 0;\n";
$sql .= "START TRANSACTION;\n\n";

// Drop all tables and views
$sql .= "-- Drop existing tables and views\n";
foreach (array_merge(array_reverse($tables), $views) as $table) {
    $sql .= "DROP TABLE IF EXISTS `$table`;\n";
}
$sql .= "\n";

// Create tables
$sql .= "-- CREATE TABLES\n\n";
foreach ($tables as $table) {
    echo "   📋 Exporting table: $table\n";
    $sql .= createTableSQL($pdo, $table);
}

// Convert views to tables
$sql .= "-- VIEWS CONVERTED TO TABLES (for shared hosting compatibility)\n\n";
foreach ($views as $view) {
    echo "   🔄 Converting view to table: $view\n";
    $sql .= convertViewToTable($pdo, $view);
}

// Insert essential data
$sql .= "-- INSERT ESSENTIAL DATA\n\n";
$essential_tables = ['admin_users', 'vip_levels', 'settings', 'product_categories'];

foreach ($essential_tables as $table) {
    if (in_array($table, $tables)) {
        echo "   💾 Exporting data: $table\n";
        $sql .= insertDataSQL($pdo, $table);
    }
}

// Footer
$sql .= "-- Finalize\n";
$sql .= "COMMIT;\n";
$sql .= "SET FOREIGN_KEY_CHECKS = 1;\n";
$sql .= "SET AUTOCOMMIT = 1;\n\n";
$sql .= "-- EASY EXPORT COMPLETE!\n";

// Write file
if (file_put_contents($output_file, $sql)) {
    echo "\n✅ SQL file created: $output_file\n";
    echo "   File size: " . number_format(strlen($sql)) . " bytes\n";
    echo "   Total objects: " . (count($tables) + count($views)) . "\n";
    
    // Copy to install folder
    if (copy($output_file, '../install/database_migration.sql')) {
        echo "   ✅ Also copied to install folder\n";
    }
    
    echo "\n🎉 EASY EXPORT COMPLETE!\n";
    echo "========================\n";
    echo "✅ All tables exported\n";
    echo "✅ Views converted to tables\n";
    echo "✅ Essential data included\n";
    echo "✅ Perfect shared hosting syntax\n";
    echo "✅ Ready for production\n\n";
    echo "📥 Import $output_file - guaranteed to work!\n";
    
} else {
    echo "❌ Error creating file\n";
}

// Helper functions
function createTableSQL($pdo, $table) {
    // Get column information
    $stmt = $pdo->query("DESCRIBE `$table`");
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Get indexes
    $stmt = $pdo->query("SHOW INDEX FROM `$table`");
    $indexes = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    $sql = "-- Table: $table\n";
    $sql .= "CREATE TABLE `$table` (\n";
    
    $column_definitions = [];
    $primary_key = null;
    
    foreach ($columns as $column) {
        $col_def = "  `{$column['Field']}` {$column['Type']}";
        
        if ($column['Null'] === 'NO') {
            $col_def .= " NOT NULL";
        }
        
        if ($column['Default'] !== null) {
            if ($column['Default'] === 'current_timestamp()' || $column['Default'] === 'CURRENT_TIMESTAMP') {
                $col_def .= " DEFAULT CURRENT_TIMESTAMP";
            } elseif (is_numeric($column['Default'])) {
                $col_def .= " DEFAULT {$column['Default']}";
            } else {
                $col_def .= " DEFAULT '{$column['Default']}'";
            }
        }
        
        if (strpos($column['Extra'], 'auto_increment') !== false) {
            $col_def .= " AUTO_INCREMENT";
        }
        
        if (strpos($column['Extra'], 'on update current_timestamp') !== false) {
            $col_def .= " ON UPDATE CURRENT_TIMESTAMP";
        }
        
        $column_definitions[] = $col_def;
        
        if ($column['Key'] === 'PRI') {
            $primary_key = $column['Field'];
        }
    }
    
    $sql .= implode(",\n", $column_definitions);
    
    if ($primary_key) {
        $sql .= ",\n  PRIMARY KEY (`$primary_key`)";
    }
    
    // Add indexes (excluding foreign keys)
    $added_indexes = [];
    foreach ($indexes as $index) {
        if ($index['Key_name'] !== 'PRIMARY' && !in_array($index['Key_name'], $added_indexes)) {
            if (strpos($index['Key_name'], '_ibfk_') === false && strpos($index['Key_name'], 'fk_') === false) {
                $sql .= ",\n  KEY `{$index['Key_name']}` (`{$index['Column_name']}`)";
                $added_indexes[] = $index['Key_name'];
            }
        }
    }
    
    $sql .= "\n) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;\n\n";
    
    return $sql;
}

function convertViewToTable($pdo, $view) {
    if ($view === 'admin_user_stats') {
        return "-- Table: admin_user_stats (converted from view)\n" .
               "CREATE TABLE `admin_user_stats` (\n" .
               "  `id` int(11) NOT NULL AUTO_INCREMENT,\n" .
               "  `total_users` int(11) DEFAULT 0,\n" .
               "  `active_users` int(11) DEFAULT 0,\n" .
               "  `new_today` int(11) DEFAULT 0,\n" .
               "  `total_balance` decimal(15,2) DEFAULT 0.00,\n" .
               "  `total_deposits` decimal(15,2) DEFAULT 0.00,\n" .
               "  `total_withdrawals` decimal(15,2) DEFAULT 0.00,\n" .
               "  `last_updated` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,\n" .
               "  PRIMARY KEY (`id`)\n" .
               ") ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;\n\n";
    } elseif ($view === 'user_dashboard_view') {
        return "-- Table: user_dashboard_view (converted from view)\n" .
               "CREATE TABLE `user_dashboard_view` (\n" .
               "  `id` int(11) NOT NULL,\n" .
               "  `username` varchar(50) NOT NULL,\n" .
               "  `balance` decimal(10,2) DEFAULT 0.00,\n" .
               "  `commission_balance` decimal(10,2) DEFAULT 0.00,\n" .
               "  `vip_level` int(11) DEFAULT 1,\n" .
               "  `vip_name` varchar(50) DEFAULT NULL,\n" .
               "  `max_daily_tasks` int(11) DEFAULT 5,\n" .
               "  `tasks_completed_today` int(11) DEFAULT 0,\n" .
               "  `referral_count` int(11) DEFAULT 0,\n" .
               "  `total_commission_earned` decimal(10,2) DEFAULT 0.00,\n" .
               "  `pending_tasks` int(11) DEFAULT 0,\n" .
               "  `last_updated` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,\n" .
               "  PRIMARY KEY (`id`)\n" .
               ") ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;\n\n";
    }
    return "";
}

function insertDataSQL($pdo, $table) {
    $stmt = $pdo->query("SELECT * FROM `$table`");
    $rows = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($rows)) {
        return "";
    }
    
    $sql = "-- Data for table: $table\n";
    
    foreach ($rows as $row) {
        $columns = array_keys($row);
        $values = [];
        
        foreach ($row as $value) {
            if ($value === null) {
                $values[] = 'NULL';
            } else {
                $escaped = str_replace(['\\', "'"], ['\\\\', "\\'"], $value);
                $values[] = "'" . $escaped . "'";
            }
        }
        
        $sql .= "INSERT INTO `$table` (`" . implode('`, `', $columns) . "`) VALUES (" . implode(', ', $values) . ");\n";
    }
    
    return $sql . "\n";
}
?>
