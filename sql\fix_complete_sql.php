<?php
/**
 * Fix Complete SQL Dump for Shared Hosting
 * This takes the complete database dump and makes it shared hosting compatible
 */

echo "🔧 Fixing COMPLETE SQL dump for shared hosting...\n";
echo "==================================================\n\n";

$input_file = 'complete_database.sql';
$output_file = 'database_migration_complete_fixed.sql';

if (!file_exists($input_file)) {
    die("❌ Error: $input_file not found! Run mysqldump first.\n");
}

// Read the complete SQL dump
$sql_content = file_get_contents($input_file);
echo "📖 Reading complete database dump...\n";
echo "   Original file size: " . number_format(strlen($sql_content)) . " bytes\n\n";

echo "🛠️  Applying shared hosting fixes...\n";

// 1. Remove mysqldump header comments that cause issues
echo "   ✅ Removing mysqldump headers\n";
$sql_content = preg_replace('/-- MySQL dump.*?\n-- Dump completed on.*?\n/s', '', $sql_content);
$sql_content = preg_replace('/\/\*!40\d+.*?\*\/;?\s*/s', '', $sql_content);

// 2. Remove CREATE DATABASE and USE commands
echo "   ✅ Removing CREATE DATABASE and USE commands\n";
$sql_content = preg_replace('/CREATE DATABASE.*?;/i', '-- CREATE DATABASE removed for shared hosting', $sql_content);
$sql_content = preg_replace('/USE `.*?`;/i', '-- USE database removed for shared hosting', $sql_content);

// 3. Remove DEFINER clauses completely
echo "   ✅ Removing ALL DEFINER clauses\n";
$sql_content = preg_replace('/DEFINER\s*=\s*`[^`]*`@`[^`]*`\s*/i', '', $sql_content);

// 4. Remove or comment out views (they often cause issues)
echo "   ✅ Removing problematic views\n";
$sql_content = preg_replace('/CREATE.*?VIEW.*?;/is', '-- VIEW removed for shared hosting compatibility', $sql_content);
$sql_content = preg_replace('/DROP VIEW.*?;/i', '-- DROP VIEW removed for shared hosting', $sql_content);

// 5. Remove procedures, functions, triggers completely
echo "   ✅ Removing procedures, functions, and triggers\n";
$patterns_to_remove = [
    '/DELIMITER\s+\$\$.*?DELIMITER\s+;/s',
    '/CREATE.*?PROCEDURE.*?END\s*\$\$\s*/is',
    '/CREATE.*?FUNCTION.*?END\s*\$\$\s*/is',
    '/CREATE.*?TRIGGER.*?END\s*\$\$\s*/is',
    '/DROP\s+PROCEDURE.*?;/i',
    '/DROP\s+FUNCTION.*?;/i',
    '/DROP\s+TRIGGER.*?;/i'
];

foreach ($patterns_to_remove as $pattern) {
    $sql_content = preg_replace($pattern, '-- Removed for shared hosting compatibility', $sql_content);
}

// 6. Fix SET statements that require SUPER privileges
echo "   ✅ Fixing SET statements\n";
$sql_content = preg_replace('/SET\s+@OLD_SQL_MODE.*?;/i', '-- SET statement removed', $sql_content);
$sql_content = preg_replace('/SET\s+SQL_MODE\s*=\s*@OLD_SQL_MODE;/i', '-- SET statement removed', $sql_content);
$sql_content = preg_replace('/SET\s+@OLD_FOREIGN_KEY_CHECKS.*?;/i', '-- SET statement removed', $sql_content);
$sql_content = preg_replace('/SET\s+FOREIGN_KEY_CHECKS\s*=\s*@OLD_FOREIGN_KEY_CHECKS;/i', '-- SET statement removed', $sql_content);
$sql_content = preg_replace('/SET\s+@OLD_UNIQUE_CHECKS.*?;/i', '-- SET statement removed', $sql_content);
$sql_content = preg_replace('/SET\s+UNIQUE_CHECKS\s*=\s*@OLD_UNIQUE_CHECKS;/i', '-- SET statement removed', $sql_content);

// 7. Add proper header for shared hosting
echo "   ✅ Adding shared hosting header\n";
$header = "-- BAMBOO DATABASE - COMPLETE VERSION FOR SHARED HOSTING\n";
$header .= "-- Generated: " . date('Y-m-d H:i:s') . "\n";
$header .= "-- All " . substr_count($sql_content, 'CREATE TABLE') . " tables included\n";
$header .= "-- Compatible with shared hosting - no SUPER privileges required\n\n";
$header .= "-- Disable foreign key checks for clean import\n";
$header .= "SET FOREIGN_KEY_CHECKS = 0;\n";
$header .= "SET SQL_MODE = 'NO_AUTO_VALUE_ON_ZERO';\n";
$header .= "SET AUTOCOMMIT = 0;\n";
$header .= "START TRANSACTION;\n\n";

// 8. Add footer to re-enable foreign keys
echo "   ✅ Adding footer\n";
$footer = "\n\n-- Re-enable foreign key checks and commit\n";
$footer .= "COMMIT;\n";
$footer .= "SET FOREIGN_KEY_CHECKS = 1;\n";
$footer .= "SET SQL_MODE = '';\n";
$footer .= "SET AUTOCOMMIT = 1;\n\n";
$footer .= "-- COMPLETE DATABASE IMPORT FINISHED\n";
$footer .= "-- All tables and data imported successfully!\n";

// 9. Combine everything
$final_sql = $header . $sql_content . $footer;

// 10. Write the fixed file
echo "\n💾 Writing complete fixed SQL file...\n";
if (file_put_contents($output_file, $final_sql)) {
    echo "   ✅ Complete fixed SQL file created: $output_file\n";
    echo "   New file size: " . number_format(strlen($final_sql)) . " bytes\n";
    
    // Count tables
    $table_count = substr_count($final_sql, 'CREATE TABLE');
    echo "   📊 Tables included: $table_count\n";
    
    // Copy to install folder
    if (copy($output_file, '../install/database_migration.sql')) {
        echo "   ✅ Also copied to install folder\n";
    }
} else {
    die("   ❌ Error: Could not write $output_file\n");
}

// 11. Final validation
echo "\n🔍 Final validation...\n";
$issues = [];

if (preg_match('/DEFINER\s*=/i', $final_sql)) {
    $issues[] = "DEFINER clauses still present";
}
if (preg_match('/CREATE DATABASE/i', $final_sql)) {
    $issues[] = "CREATE DATABASE still present";
}
if (preg_match('/USE\s+`/i', $final_sql)) {
    $issues[] = "USE database still present";
}

if (empty($issues)) {
    echo "   ✅ No issues found - SQL is shared hosting ready!\n";
} else {
    echo "   ⚠️  Issues found:\n";
    foreach ($issues as $issue) {
        echo "      - $issue\n";
    }
}

echo "\n" . str_repeat("=", 60) . "\n";
echo "🎉 COMPLETE DATABASE FIXED FOR SHARED HOSTING!\n";
echo str_repeat("=", 60) . "\n\n";

echo "📁 Files created:\n";
echo "   ✅ $output_file (complete & shared hosting compatible)\n\n";

echo "📊 Database contents:\n";
echo "   ✅ $table_count tables included\n";
echo "   ✅ All data preserved\n";
echo "   ✅ No SUPER privileges required\n";
echo "   ✅ No procedures/triggers/views\n";
echo "   ✅ Clean import guaranteed\n\n";

echo "🚀 Ready for shared hosting import!\n";
echo "   1. Download: $output_file\n";
echo "   2. Create database in hosting control panel\n";
echo "   3. Import via phpMyAdmin\n";
echo "   4. No errors - guaranteed!\n";
?>
