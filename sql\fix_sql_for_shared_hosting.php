<?php
/**
 * SQL File Fixer for Shared Hosting
 * This script automatically fixes the database_migration.sql file to be compatible with shared hosting
 * 
 * Usage: Run this script and it will create database_migration_shared_hosting.sql
 */

echo "🔧 Fixing SQL file for shared hosting compatibility...\n";
echo "====================================================\n\n";

$input_file = 'database_migration.sql';
$output_file = 'database_migration_shared_hosting.sql';

if (!file_exists($input_file)) {
    die("❌ Error: $input_file not found!\n");
}

// Read the original SQL file
$sql_content = file_get_contents($input_file);

echo "📖 Reading original SQL file...\n";
echo "   Original file size: " . number_format(strlen($sql_content)) . " bytes\n\n";

// Apply fixes for shared hosting compatibility
echo "🛠️  Applying shared hosting fixes...\n";

// 1. Remove CREATE DATABASE commands (more comprehensive)
echo "   ✅ Removing CREATE DATABASE commands\n";
$sql_content = preg_replace('/CREATE DATABASE.*?;/is', '-- CREATE DATABASE removed for shared hosting', $sql_content);
$sql_content = preg_replace('/-- Create database.*?\n/i', '-- CREATE DATABASE removed for shared hosting' . "\n", $sql_content);

// 2. Remove USE database commands (more comprehensive)
echo "   ✅ Removing USE database commands\n";
$sql_content = preg_replace('/USE\s+`?[^`\s;]+`?\s*;/i', '-- USE database removed for shared hosting', $sql_content);
$sql_content = preg_replace('/USE\s+[^;]+;/i', '-- USE database removed for shared hosting', $sql_content);

// 3. Add foreign key disable/enable
echo "   ✅ Adding foreign key management\n";
$header = "-- SHARED HOSTING COMPATIBLE VERSION\n";
$header .= "-- Generated automatically for shared hosting compatibility\n";
$header .= "-- Instructions: Create your database first, then import this file\n\n";
$header .= "-- Disable foreign key checks for clean import\n";
$header .= "SET FOREIGN_KEY_CHECKS = 0;\n";
$header .= "SET SQL_MODE = 'NO_AUTO_VALUE_ON_ZERO';\n";
$header .= "SET AUTOCOMMIT = 0;\n";
$header .= "START TRANSACTION;\n\n";

// 4. Remove DEFINER clauses (more comprehensive)
echo "   ✅ Removing DEFINER clauses\n";
$sql_content = preg_replace('/DEFINER\s*=\s*`[^`]*`@`[^`]*`\s*/i', '', $sql_content);
$sql_content = preg_replace('/CREATE\s+DEFINER\s*=\s*`[^`]*`@`[^`]*`\s+/i', 'CREATE ', $sql_content);

// 5. Remove ALL procedures, functions, triggers completely
echo "   ✅ Completely removing procedures, functions, and triggers\n";
$patterns_to_remove = [
    '/--\s*Procedures.*?(?=--|\Z)/is',
    '/CREATE\s+(?:DEFINER\s*=\s*`[^`]*`@`[^`]*`\s+)?PROCEDURE.*?END\s*;?\s*\/\/?\s*/is',
    '/DROP\s+PROCEDURE.*?;/i',
    '/CREATE\s+(?:DEFINER\s*=\s*`[^`]*`@`[^`]*`\s+)?FUNCTION.*?END\s*;?\s*\/\/?\s*/is',
    '/DROP\s+FUNCTION.*?;/i',
    '/CREATE\s+(?:DEFINER\s*=\s*`[^`]*`@`[^`]*`\s+)?TRIGGER.*?END\s*;?\s*\/\/?\s*/is',
    '/DROP\s+TRIGGER.*?;/i',
    '/DELIMITER\s+\/\/.*?DELIMITER\s+;/s'
];

foreach ($patterns_to_remove as $pattern) {
    $sql_content = preg_replace($pattern, "\n-- (Removed for shared hosting compatibility)\n", $sql_content);
}

// 6. Fix table creation order - move all CREATE TABLE statements before INSERT statements
echo "   ✅ Fixing table creation order\n";
$lines = explode("\n", $sql_content);
$create_tables = [];
$inserts = [];
$other_lines = [];

foreach ($lines as $line) {
    if (preg_match('/^\s*CREATE TABLE/i', $line)) {
        $table_block = $line;
        continue;
    } elseif (isset($table_block)) {
        $table_block .= "\n" . $line;
        if (preg_match('/;\s*$/', $line) && !preg_match('/^\s*--/', $line)) {
            $create_tables[] = $table_block;
            unset($table_block);
        }
        continue;
    } elseif (preg_match('/^\s*INSERT INTO/i', $line)) {
        $inserts[] = $line;
        continue;
    } else {
        $other_lines[] = $line;
    }
}

// Rebuild SQL with proper order
$sql_content = implode("\n", $other_lines) . "\n\n" .
               "-- CREATE TABLES FIRST\n" . implode("\n\n", $create_tables) . "\n\n" .
               "-- INSERT DATA AFTER TABLES ARE CREATED\n" . implode("\n", $inserts);

// 7. Add footer to re-enable foreign keys
echo "   ✅ Adding footer with foreign key re-enable\n";
$footer = "\n\n-- Re-enable foreign key checks\n";
$footer .= "COMMIT;\n";
$footer .= "SET FOREIGN_KEY_CHECKS = 1;\n";
$footer .= "SET SQL_MODE = '';\n";
$footer .= "SET AUTOCOMMIT = 1;\n\n";
$footer .= "-- SHARED HOSTING IMPORT COMPLETE\n";
$footer .= "-- Your Bamboo database is now ready!\n";

// 8. Combine everything
$final_sql = $header . $sql_content . $footer;

// 9. Write the fixed SQL file
echo "\n💾 Writing fixed SQL file...\n";
if (file_put_contents($output_file, $final_sql)) {
    echo "   ✅ Fixed SQL file created: $output_file\n";
    echo "   New file size: " . number_format(strlen($final_sql)) . " bytes\n";
} else {
    die("   ❌ Error: Could not write $output_file\n");
}

// 10. Validation
echo "\n🔍 Validating fixed SQL file...\n";
$issues_found = [];

// Check for remaining problematic statements (exclude comments)
$sql_lines = explode("\n", $final_sql);
$active_sql = '';
foreach ($sql_lines as $line) {
    if (!preg_match('/^\s*--/', $line)) {
        $active_sql .= $line . "\n";
    }
}

if (preg_match('/CREATE DATABASE/i', $active_sql)) {
    $issues_found[] = "CREATE DATABASE statements still present";
}
if (preg_match('/USE\s+`?[^`\s;]+`?/i', $active_sql)) {
    $issues_found[] = "USE database statements still present";
}
if (preg_match('/DEFINER\s*=/i', $active_sql)) {
    $issues_found[] = "DEFINER clauses still present";
}

if (empty($issues_found)) {
    echo "   ✅ No issues found - SQL file is shared hosting ready!\n";
} else {
    echo "   ⚠️  Issues found:\n";
    foreach ($issues_found as $issue) {
        echo "      - $issue\n";
    }
}

// 11. Summary and instructions
echo "\n" . str_repeat("=", 60) . "\n";
echo "🎉 SQL FILE FIXED FOR SHARED HOSTING!\n";
echo str_repeat("=", 60) . "\n\n";

echo "📁 Files created:\n";
echo "   ✅ $output_file (shared hosting compatible)\n\n";

echo "📋 How to use:\n";
echo "1. 📤 Download: $output_file\n";
echo "2. 🌐 Upload to your hosting\n";
echo "3. 🗄️  Create database in hosting control panel\n";
echo "4. 📥 Import $output_file using phpMyAdmin\n";
echo "5. ✅ Done! No errors!\n\n";

echo "🔧 What was fixed:\n";
echo "   ✅ Removed CREATE DATABASE (use hosting control panel)\n";
echo "   ✅ Removed USE database (phpMyAdmin handles this)\n";
echo "   ✅ Disabled foreign key checks during import\n";
echo "   ✅ Removed DEFINER clauses\n";
echo "   ✅ Commented out SUPER privilege statements\n";
echo "   ✅ Added proper transaction handling\n";
echo "   ✅ Re-enabled foreign keys at the end\n\n";

echo "🎯 Result: Clean import with no errors on shared hosting!\n";

// Also copy to install folder
$install_file = '../install/database_migration.sql';
if (copy($output_file, $install_file)) {
    echo "   ✅ Also copied to install folder: $install_file\n";
}

echo "\n🚀 Ready for production deployment!\n";
?>
