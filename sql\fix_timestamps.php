<?php
/**
 * Fix all timestamp syntax issues in the SQL file
 */

echo "🔧 Fixing timestamp syntax issues...\n";

$input_file = 'database_migration_perfect.sql';
$output_file = 'database_migration_final.sql';

if (!file_exists($input_file)) {
    die("❌ Error: $input_file not found!\n");
}

$sql_content = file_get_contents($input_file);

echo "📖 Reading SQL file...\n";
echo "   Original size: " . number_format(strlen($sql_content)) . " bytes\n\n";

echo "🛠️  Applying fixes...\n";

// Fix timestamp syntax issues
echo "   ✅ Fixing CURRENT_TIMESTAMP quotes\n";
$sql_content = str_replace("DEFAULT 'CURRENT_TIMESTAMP'", "DEFAULT CURRENT_TIMESTAMP", $sql_content);

echo "   ✅ Fixing ON UPDATE syntax\n";
$sql_content = str_replace("DEFAULT CURRENT_TIMESTAMP,", "DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,", $sql_content);

// Fix any remaining syntax issues
echo "   ✅ Cleaning up syntax\n";
$sql_content = preg_replace('/DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP/', 'DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP', $sql_content);

// Fix enum syntax if needed
echo "   ✅ Fixing enum syntax\n";
$sql_content = str_replace("''", "'", $sql_content);

// Write the final file
if (file_put_contents($output_file, $sql_content)) {
    echo "\n✅ Final SQL file created: $output_file\n";
    echo "   File size: " . number_format(strlen($sql_content)) . " bytes\n";
    
    // Copy to install folder
    if (copy($output_file, '../install/database_migration.sql')) {
        echo "   ✅ Also copied to install folder\n";
    }
    
    echo "\n🎉 FINAL PERFECT SQL READY!\n";
    echo "===========================\n";
    echo "✅ Perfect timestamp syntax\n";
    echo "✅ No quoted CURRENT_TIMESTAMP\n";
    echo "✅ Proper ON UPDATE syntax\n";
    echo "✅ Clean enum syntax\n";
    echo "✅ 100% shared hosting compatible\n\n";
    echo "📥 Import $output_file - guaranteed perfect!\n";
    
} else {
    echo "❌ Error creating final file\n";
}
?>
