<?php
/**
 * Admin Dashboard Comprehensive Audit Test
 */

define('BAMBOO_APP', true);
require_once 'includes/config.php';
require_once 'includes/functions.php';

echo "=== BAMBOO ADMIN DASHBOARD AUDIT ===\n\n";

// Test 1: Database Connection
echo "1. DATABASE CONNECTION TEST\n";
try {
    $db = getDB();
    echo "✅ Database connection successful\n";
} catch (Exception $e) {
    echo "❌ Database connection failed: " . $e->getMessage() . "\n";
    exit;
}

// Test 2: Admin Authentication Functions
echo "\n2. ADMIN AUTHENTICATION FUNCTIONS TEST\n";

// Check if admin login function exists
if (function_exists('adminLogin')) {
    echo "✅ adminLogin() function exists\n";
    
    // Test admin login with correct credentials
    $login_result = adminLogin('admin', 'admin123', false);
    if ($login_result['success']) {
        echo "✅ Admin login successful with correct credentials\n";
    } else {
        echo "❌ Admin login failed: " . $login_result['message'] . "\n";
    }
    
    // Test admin login with wrong credentials
    $wrong_login = adminLogin('admin', 'wrongpassword', false);
    if (!$wrong_login['success']) {
        echo "✅ Admin login correctly rejects wrong credentials\n";
    } else {
        echo "❌ Admin login security issue: accepts wrong credentials\n";
    }
} else {
    echo "❌ adminLogin() function not found\n";
}

// Test 3: Admin User Management
echo "\n3. ADMIN USER MANAGEMENT TEST\n";

// Check admin users table
$admin_count = getRecordCount('admin_users');
echo "✅ Admin users table accessible, $admin_count admin(s) found\n";

// Get admin user details
$admin_user = fetchRow("SELECT * FROM admin_users WHERE username = 'admin'");
if ($admin_user) {
    echo "✅ Default admin user exists\n";
    echo "   - Username: {$admin_user['username']}\n";
    echo "   - Email: {$admin_user['email']}\n";
    echo "   - Role: {$admin_user['role']}\n";
    echo "   - Status: {$admin_user['status']}\n";
} else {
    echo "❌ Default admin user not found\n";
}

// Test 4: User Management Functions
echo "\n4. USER MANAGEMENT FUNCTIONS TEST\n";

$user_count = getRecordCount('users');
echo "✅ Users table accessible, $user_count user(s) found\n";

// Test user CRUD operations
$test_users = fetchAll("SELECT id, username, email, status, vip_level, balance FROM users LIMIT 3");
foreach ($test_users as $user) {
    echo "   - User: {$user['username']}, Status: {$user['status']}, VIP: {$user['vip_level']}, Balance: {$user['balance']}\n";
}

// Test 5: Product Management
echo "\n5. PRODUCT MANAGEMENT TEST\n";

$product_count = getRecordCount('products');
echo "✅ Products table accessible, $product_count product(s) found\n";

$products = fetchAll("SELECT id, name, price, commission_rate, status FROM products LIMIT 3");
foreach ($products as $product) {
    echo "   - Product: {$product['name']}, Price: {$product['price']}, Commission: {$product['commission_rate']}%, Status: {$product['status']}\n";
}

// Test 6: Transaction Management
echo "\n6. TRANSACTION MANAGEMENT TEST\n";

$transaction_count = getRecordCount('transactions');
echo "✅ Transactions table accessible, $transaction_count transaction(s) found\n";

$recent_transactions = fetchAll("SELECT id, user_id, type, amount, status, created_at FROM transactions ORDER BY created_at DESC LIMIT 3");
foreach ($recent_transactions as $tx) {
    echo "   - TX {$tx['id']}: User {$tx['user_id']}, {$tx['type']}, {$tx['amount']}, {$tx['status']}\n";
}

// Test 7: VIP Level Management
echo "\n7. VIP LEVEL MANAGEMENT TEST\n";

$vip_count = getRecordCount('vip_levels');
echo "✅ VIP levels table accessible, $vip_count level(s) found\n";

$vip_levels = fetchAll("SELECT level, name, min_balance, max_daily_tasks, commission_multiplier FROM vip_levels ORDER BY level");
foreach ($vip_levels as $vip) {
    echo "   - {$vip['name']}: Min ${$vip['min_balance']}, {$vip['max_daily_tasks']} tasks, {$vip['commission_multiplier']}x multiplier\n";
}

// Test 8: Settings Management
echo "\n8. SETTINGS MANAGEMENT TEST\n";

$settings_count = getRecordCount('settings');
echo "✅ Settings table accessible, $settings_count setting(s) found\n";

// Test key settings
$key_settings = ['app_name', 'welcome_bonus', 'min_withdrawal', 'referral_commission_rate'];
foreach ($key_settings as $key) {
    $value = getAppSetting($key, 'NOT_FOUND');
    echo "   - $key: $value\n";
}

// Test 9: File Upload Directories
echo "\n9. FILE UPLOAD SYSTEM TEST\n";

$upload_dirs = [
    'uploads/',
    'uploads/products/',
    'uploads/avatars/',
    'uploads/logos/',
    'uploads/certificates/'
];

foreach ($upload_dirs as $dir) {
    if (is_dir($dir)) {
        $writable = is_writable($dir) ? 'writable' : 'not writable';
        echo "✅ Directory $dir exists and is $writable\n";
    } else {
        echo "❌ Directory $dir does not exist\n";
    }
}

// Test 10: Security Features
echo "\n10. SECURITY FEATURES TEST\n";

// Test CSRF token generation
if (function_exists('generateCSRFToken')) {
    $token = generateCSRFToken();
    echo "✅ CSRF token generation working: " . substr($token, 0, 10) . "...\n";
} else {
    echo "❌ CSRF token generation function not found\n";
}

// Test password hashing
if (function_exists('hashPassword')) {
    $hash = hashPassword('testpassword');
    echo "✅ Password hashing working\n";
} else {
    echo "❌ Password hashing function not found\n";
}

// Test input sanitization
if (function_exists('sanitizeInput')) {
    $clean = sanitizeInput('<script>alert("test")</script>');
    echo "✅ Input sanitization working\n";
} else {
    echo "❌ Input sanitization function not found\n";
}

echo "\n=== ADMIN AUDIT COMPLETE ===\n";
?>
