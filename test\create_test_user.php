<?php
/**
 * Create Test User for Login Testing
 */

define('BAMBOO_APP', true);
require_once __DIR__ . '/../includes/config.php';
require_once __DIR__ . '/../includes/functions.php';

// Create a simple test user
$username = 'testuser';
$password = 'password123';
$phone = '1234567890';
$email = '<EMAIL>';

// Check if user already exists
$existing_user = fetchRow("SELECT id FROM users WHERE username = ? OR phone = ? OR email = ?", [$username, $phone, $email]);

if ($existing_user) {
    echo "Test user already exists with ID: " . $existing_user['id'] . "\n";
    echo "Username: $username\n";
    echo "Password: $password\n";
    echo "Phone: $phone\n";
    echo "Email: $email\n";
} else {
    // Create new test user
    $password_hash = password_hash($password, PASSWORD_DEFAULT);
    $withdrawal_pin_hash = password_hash('1234', PASSWORD_DEFAULT);
    $invitation_code = 'TEST' . strtoupper(substr(uniqid(), -4));
    
    $user_data = [
        'username' => $username,
        'phone' => $phone,
        'email' => $email,
        'password_hash' => $password_hash,
        'withdrawal_pin_hash' => $withdrawal_pin_hash,
        'gender' => 'male',
        'invitation_code' => $invitation_code,
        'balance' => 100.00,
        'commission_balance' => 0.00,
        'frozen_balance' => 0.00,
        'vip_level' => 1,
        'status' => 'active',
        'email_verified' => 1,
        'phone_verified' => 1,
        'created_at' => date('Y-m-d H:i:s'),
        'updated_at' => date('Y-m-d H:i:s'),
        'credit_score' => 100
    ];
    
    $user_id = insertRecord('users', $user_data);
    
    if ($user_id) {
        echo "✅ Test user created successfully!\n";
        echo "User ID: $user_id\n";
        echo "Username: $username\n";
        echo "Password: $password\n";
        echo "Phone: $phone\n";
        echo "Email: $email\n";
        echo "Withdrawal PIN: 1234\n";
        echo "Invitation Code: $invitation_code\n";
        echo "\n🔗 You can now test login at: http://localhost/Bamboo/user/login/login.php\n";
    } else {
        echo "❌ Failed to create test user.\n";
    }
}
?>
