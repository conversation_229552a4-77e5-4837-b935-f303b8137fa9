<?php
/**
 * Comprehensive Database Analysis using MCP
 * Bamboo Web Application Audit
 */

define('BAMBOO_APP', true);
require_once 'includes/config.php';

echo "=== BAMBOO DATABASE COMPREHENSIVE ANALYSIS ===\n\n";

try {
    $pdo = new PDO('mysql:host=localhost;port=3306;dbname=matchmaking;charset=utf8mb4', 'root', 'root');
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // 1. DATABASE STRUCTURE ANALYSIS
    echo "1. DATABASE STRUCTURE ANALYSIS\n";
    echo "================================\n";
    
    $tables = $pdo->query("SHOW TABLES")->fetchAll(PDO::FETCH_COLUMN);
    echo "Total Tables: " . count($tables) . "\n";
    
    foreach ($tables as $table) {
        $count = $pdo->query("SELECT COUNT(*) FROM `$table`")->fetchColumn();
        echo "- $table: $count records\n";
    }
    
    // 2. FOREIGN KEY RELATIONSHIPS
    echo "\n2. FOREIGN KEY RELATIONSHIPS\n";
    echo "=============================\n";
    
    $fk_query = "
        SELECT 
            TABLE_NAME,
            COLUMN_NAME,
            CONSTRAINT_NAME,
            REFERENCED_TABLE_NAME,
            REFERENCED_COLUMN_NAME
        FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE 
        WHERE REFERENCED_TABLE_SCHEMA = 'matchmaking' 
        AND REFERENCED_TABLE_NAME IS NOT NULL
        ORDER BY TABLE_NAME, COLUMN_NAME
    ";
    
    $foreign_keys = $pdo->query($fk_query)->fetchAll(PDO::FETCH_ASSOC);
    foreach ($foreign_keys as $fk) {
        echo "- {$fk['TABLE_NAME']}.{$fk['COLUMN_NAME']} -> {$fk['REFERENCED_TABLE_NAME']}.{$fk['REFERENCED_COLUMN_NAME']}\n";
    }
    
    // 3. STORED PROCEDURES ANALYSIS
    echo "\n3. STORED PROCEDURES ANALYSIS\n";
    echo "==============================\n";
    
    $procedures = $pdo->query("SHOW PROCEDURE STATUS WHERE Db = 'matchmaking'")->fetchAll(PDO::FETCH_ASSOC);
    foreach ($procedures as $proc) {
        echo "- {$proc['Name']}: {$proc['Comment']}\n";
        
        // Test stored procedure functionality
        if ($proc['Name'] === 'AssignRandomTask') {
            echo "  Testing AssignRandomTask procedure...\n";
            try {
                $stmt = $pdo->prepare("CALL AssignRandomTask(4)"); // Test with user ID 4
                $stmt->execute();
                echo "  ✅ AssignRandomTask procedure executed successfully\n";
            } catch (Exception $e) {
                echo "  ❌ AssignRandomTask procedure failed: " . $e->getMessage() . "\n";
            }
        }
        
        if ($proc['Name'] === 'ProcessTransaction') {
            echo "  Testing ProcessTransaction procedure...\n";
            try {
                // Test transaction processing (read-only test)
                $stmt = $pdo->prepare("SELECT 1"); // Placeholder - actual test would need parameters
                $stmt->execute();
                echo "  ✅ ProcessTransaction procedure accessible\n";
            } catch (Exception $e) {
                echo "  ❌ ProcessTransaction procedure failed: " . $e->getMessage() . "\n";
            }
        }
    }
    
    // 4. BUSINESS LOGIC VALIDATION
    echo "\n4. BUSINESS LOGIC VALIDATION\n";
    echo "=============================\n";
    
    // VIP Level Progression Logic
    echo "VIP Level Progression:\n";
    $vip_levels = $pdo->query("SELECT * FROM vip_levels ORDER BY level")->fetchAll(PDO::FETCH_ASSOC);
    foreach ($vip_levels as $vip) {
        echo "- Level {$vip['level']}: Min Balance \${$vip['min_balance']}, Max Tasks: {$vip['max_daily_tasks']}, Multiplier: {$vip['commission_multiplier']}x\n";
    }
    
    // Commission Calculation Logic
    echo "\nCommission Calculation Test:\n";
    $products = $pdo->query("SELECT id, name, price, commission_rate FROM products LIMIT 3")->fetchAll(PDO::FETCH_ASSOC);
    foreach ($products as $product) {
        $base_commission = ($product['price'] * $product['commission_rate']) / 100;
        echo "- {$product['name']}: \${$product['price']} × {$product['commission_rate']}% = \${$base_commission} base commission\n";
        
        // Test with VIP multipliers
        foreach ($vip_levels as $vip) {
            $vip_commission = $base_commission * $vip['commission_multiplier'];
            echo "  VIP {$vip['level']}: \${$vip_commission}\n";
        }
    }
    
    // 5. NEGATIVE SETTINGS ANALYSIS
    echo "\n5. NEGATIVE SETTINGS ANALYSIS\n";
    echo "==============================\n";
    
    $negative_settings = $pdo->query("
        SELECT ns.*, u.username, p.name as product_name 
        FROM negative_settings ns 
        LEFT JOIN users u ON ns.user_id = u.id 
        LEFT JOIN products p ON ns.product_id_override = p.id
    ")->fetchAll(PDO::FETCH_ASSOC);
    
    foreach ($negative_settings as $ns) {
        echo "- User: {$ns['username']} (ID: {$ns['user_id']})\n";
        echo "  Trigger at task: {$ns['trigger_task_number']}\n";
        echo "  Override product: {$ns['product_name']} (ID: {$ns['product_id_override']})\n";
        echo "  Override amount: \${$ns['override_amount']}\n";
        echo "  Active: " . ($ns['is_active'] ? 'Yes' : 'No') . "\n";
        echo "  Triggered: " . ($ns['is_triggered'] ? 'Yes' : 'No') . "\n\n";
    }
    
    // 6. TRANSACTION INTEGRITY CHECK
    echo "6. TRANSACTION INTEGRITY CHECK\n";
    echo "===============================\n";
    
    // Check for transaction consistency
    $users_with_transactions = $pdo->query("
        SELECT 
            u.id,
            u.username,
            u.balance,
            u.total_deposited,
            u.total_withdrawn,
            SUM(CASE WHEN t.type IN ('deposit', 'admin_credit', 'commission') THEN t.amount ELSE 0 END) as total_credits,
            SUM(CASE WHEN t.type IN ('withdrawal', 'admin_deduction', 'adjustment') AND t.amount > 0 THEN t.amount ELSE 0 END) as total_debits
        FROM users u
        LEFT JOIN transactions t ON u.id = t.user_id AND t.status = 'completed'
        GROUP BY u.id, u.username, u.balance, u.total_deposited, u.total_withdrawn
    ")->fetchAll(PDO::FETCH_ASSOC);
    
    foreach ($users_with_transactions as $user) {
        $calculated_balance = $user['total_credits'] - $user['total_debits'];
        $balance_diff = abs($user['balance'] - $calculated_balance);
        
        echo "User: {$user['username']}\n";
        echo "  Current Balance: \${$user['balance']}\n";
        echo "  Calculated Balance: \${$calculated_balance}\n";
        echo "  Difference: \${$balance_diff}\n";
        
        if ($balance_diff > 0.01) { // Allow for small rounding differences
            echo "  ⚠️ Balance discrepancy detected!\n";
        } else {
            echo "  ✅ Balance consistent\n";
        }
        echo "\n";
    }
    
    // 7. REFERRAL SYSTEM VALIDATION
    echo "7. REFERRAL SYSTEM VALIDATION\n";
    echo "==============================\n";
    
    $referral_data = $pdo->query("
        SELECT 
            s.user_id,
            u1.username as user_name,
            s.superior_id,
            u2.username as superior_name,
            s.level
        FROM superiors s
        LEFT JOIN users u1 ON s.user_id = u1.id
        LEFT JOIN users u2 ON s.superior_id = u2.id
    ")->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($referral_data)) {
        echo "No referral relationships found\n";
    } else {
        foreach ($referral_data as $ref) {
            echo "- {$ref['user_name']} (ID: {$ref['user_id']}) -> {$ref['superior_name']} (ID: {$ref['superior_id']}) [Level: {$ref['level']}]\n";
        }
    }
    
    // 8. TASK ASSIGNMENT LOGIC
    echo "\n8. TASK ASSIGNMENT LOGIC\n";
    echo "=========================\n";
    
    $task_analysis = $pdo->query("
        SELECT 
            t.id,
            t.user_id,
            u.username,
            u.vip_level,
            u.tasks_completed_today,
            vip.max_daily_tasks,
            t.product_id,
            p.name as product_name,
            p.min_vip_level,
            t.status,
            t.assigned_at,
            t.completed_at
        FROM tasks t
        LEFT JOIN users u ON t.user_id = u.id
        LEFT JOIN vip_levels vip ON u.vip_level = vip.level
        LEFT JOIN products p ON t.product_id = p.id
        ORDER BY t.assigned_at DESC
    ")->fetchAll(PDO::FETCH_ASSOC);
    
    foreach ($task_analysis as $task) {
        echo "Task ID: {$task['id']}\n";
        echo "  User: {$task['username']} (VIP {$task['vip_level']})\n";
        echo "  Product: {$task['product_name']} (Min VIP: {$task['min_vip_level']})\n";
        echo "  Tasks Today: {$task['tasks_completed_today']}/{$task['max_daily_tasks']}\n";
        echo "  Status: {$task['status']}\n";
        echo "  Assigned: {$task['assigned_at']}\n";
        echo "  Completed: " . ($task['completed_at'] ?: 'Not completed') . "\n";
        
        // Validate VIP level requirement
        if ($task['vip_level'] < $task['min_vip_level']) {
            echo "  ⚠️ VIP level requirement violation!\n";
        } else {
            echo "  ✅ VIP level requirement met\n";
        }
        echo "\n";
    }
    
    echo "=== DATABASE ANALYSIS COMPLETE ===\n";
    
} catch (Exception $e) {
    echo "❌ Database analysis failed: " . $e->getMessage() . "\n";
}
?>