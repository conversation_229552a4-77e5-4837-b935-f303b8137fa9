<?php
/**
 * Fixed Database Analysis - Referral System Check
 */

define('BAMBOO_APP', true);
require_once 'includes/config.php';

echo "=== REFERRAL SYSTEM & TASK ANALYSIS ===\n\n";

try {
    $pdo = new PDO('mysql:host=localhost;port=3306;dbname=matchmaking;charset=utf8mb4', 'root', 'root');
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // Check superiors table structure
    echo "SUPERIORS TABLE STRUCTURE:\n";
    $columns = $pdo->query("DESCRIBE superiors")->fetchAll(PDO::FETCH_ASSOC);
    foreach ($columns as $col) {
        echo "- {$col['Field']}: {$col['Type']}\n";
    }
    
    echo "\nSUPERIORS TABLE DATA:\n";
    $superiors = $pdo->query("SELECT * FROM superiors")->fetchAll(PDO::FETCH_ASSOC);
    foreach ($superiors as $sup) {
        echo "- ID: {$sup['id']}, User: {$sup['user_id']}, Superior: {$sup['superior_id']}, Level: {$sup['level']}\n";
    }
    
    // Transaction integrity detailed analysis
    echo "\nDETAILED TRANSACTION ANALYSIS:\n";
    echo "===============================\n";
    
    $users = $pdo->query("SELECT id, username, balance FROM users")->fetchAll(PDO::FETCH_ASSOC);
    
    foreach ($users as $user) {
        echo "\nUser: {$user['username']} (ID: {$user['id']})\n";
        echo "Current Balance: \${$user['balance']}\n";
        
        // Get all transactions for this user
        $transactions = $pdo->query("
            SELECT type, amount, status, description, created_at 
            FROM transactions 
            WHERE user_id = {$user['id']} 
            ORDER BY created_at ASC
        ")->fetchAll(PDO::FETCH_ASSOC);
        
        $running_balance = 0;
        echo "Transaction History:\n";
        
        foreach ($transactions as $tx) {
            if ($tx['status'] === 'completed') {
                if (in_array($tx['type'], ['deposit', 'admin_credit', 'commission', 'adjustment'])) {
                    $running_balance += $tx['amount'];
                    echo "  + \${$tx['amount']} ({$tx['type']}) = \${$running_balance} - {$tx['created_at']}\n";
                } elseif (in_array($tx['type'], ['withdrawal', 'admin_deduction'])) {
                    $running_balance -= $tx['amount'];
                    echo "  - \${$tx['amount']} ({$tx['type']}) = \${$running_balance} - {$tx['created_at']}\n";
                }
            } else {
                echo "  PENDING: \${$tx['amount']} ({$tx['type']}) - {$tx['created_at']}\n";
            }
        }
        
        echo "Calculated Final Balance: \${$running_balance}\n";
        echo "Actual Balance: \${$user['balance']}\n";
        $diff = abs($user['balance'] - $running_balance);
        echo "Difference: \${$diff}\n";
        
        if ($diff > 0.01) {
            echo "⚠️ BALANCE DISCREPANCY DETECTED!\n";
        } else {
            echo "✅ Balance is consistent\n";
        }
    }
    
    // Task completion analysis
    echo "\n\nTASK COMPLETION ANALYSIS:\n";
    echo "==========================\n";
    
    $task_stats = $pdo->query("
        SELECT 
            status,
            COUNT(*) as count,
            AVG(commission_earned) as avg_commission
        FROM tasks 
        GROUP BY status
    ")->fetchAll(PDO::FETCH_ASSOC);
    
    foreach ($task_stats as $stat) {
        echo "- {$stat['status']}: {$stat['count']} tasks, Avg Commission: \${$stat['avg_commission']}\n";
    }
    
    // Negative settings trigger analysis
    echo "\nNEGATIVE SETTINGS TRIGGER ANALYSIS:\n";
    echo "====================================\n";
    
    $negative_analysis = $pdo->query("
        SELECT 
            ns.*,
            u.username,
            u.tasks_completed_today,
            p.name as product_name
        FROM negative_settings ns
        LEFT JOIN users u ON ns.user_id = u.id
        LEFT JOIN products p ON ns.product_id_override = p.id
        WHERE ns.is_active = 1
    ")->fetchAll(PDO::FETCH_ASSOC);
    
    foreach ($negative_analysis as $ns) {
        echo "User: {$ns['username']}\n";
        echo "  Current tasks completed today: {$ns['tasks_completed_today']}\n";
        echo "  Will trigger at task: {$ns['trigger_task_number']}\n";
        echo "  Override product: {$ns['product_name']}\n";
        echo "  Override amount: \${$ns['override_amount']}\n";
        echo "  Status: " . ($ns['is_triggered'] ? 'TRIGGERED' : 'WAITING') . "\n";
        
        if ($ns['tasks_completed_today'] >= $ns['trigger_task_number'] && !$ns['is_triggered']) {
            echo "  ⚠️ Should have triggered but hasn't!\n";
        } elseif ($ns['tasks_completed_today'] < $ns['trigger_task_number']) {
            $remaining = $ns['trigger_task_number'] - $ns['tasks_completed_today'];
            echo "  📊 $remaining tasks remaining until trigger\n";
        }
        echo "\n";
    }
    
    echo "=== ANALYSIS COMPLETE ===\n";
    
} catch (Exception $e) {
    echo "❌ Analysis failed: " . $e->getMessage() . "\n";
}
?>