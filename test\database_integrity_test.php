<?php
/**
 * Database Integrity and Business Logic Validation Test
 * Comprehensive testing of stored procedures, triggers, and business logic
 */

define('BAMBOO_APP', true);
require_once 'includes/config.php';
require_once 'includes/functions.php';

echo "🔍 DATABASE INTEGRITY AND BUSINESS LOGIC VALIDATION\n";
echo "====================================================\n\n";

try {
    $db = getDB();
    
    // 1. TEST STORED PROCEDURES
    echo "1. STORED PROCEDURES VALIDATION\n";
    echo "===============================\n";
    
    // Check if stored procedures exist
    $procedures = $db->query("SHOW PROCEDURE STATUS WHERE Db = DATABASE()")->fetchAll(PDO::FETCH_ASSOC);
    $procedure_names = array_column($procedures, 'Name');
    
    echo "Available stored procedures:\n";
    foreach ($procedure_names as $proc_name) {
        echo "  ✅ $proc_name\n";
    }
    
    // Test AssignRandomTask procedure
    echo "\nTesting AssignRandomTask procedure:\n";
    if (in_array('AssignRandomTask', $procedure_names)) {
        // Get a test user
        $test_user = fetchRow("SELECT id, username, vip_level, tasks_completed_today FROM users WHERE status = 'active' LIMIT 1");
        if ($test_user) {
            echo "  Test user: {$test_user['username']} (ID: {$test_user['id']}, VIP: {$test_user['vip_level']})\n";
            echo "  Current tasks today: {$test_user['tasks_completed_today']}\n";
            
            // Check VIP level limits
            $vip_level = fetchRow("SELECT * FROM vip_levels WHERE level = ?", [$test_user['vip_level']]);
            if ($vip_level) {
                echo "  VIP Level {$vip_level['level']}: Max daily tasks = {$vip_level['max_daily_tasks']}\n";
                
                if ($test_user['tasks_completed_today'] < $vip_level['max_daily_tasks']) {
                    echo "  ✅ User can receive more tasks\n";
                } else {
                    echo "  ⚠️  User has reached daily task limit\n";
                }
            }
        }
    } else {
        echo "  ❌ AssignRandomTask procedure not found\n";
    }
    
    // Test ProcessTransaction procedure
    echo "\nTesting ProcessTransaction procedure:\n";
    if (in_array('ProcessTransaction', $procedure_names)) {
        echo "  ✅ ProcessTransaction procedure exists\n";
        
        // Test transaction logic (read-only validation)
        $test_user = fetchRow("SELECT id, username, balance FROM users WHERE status = 'active' LIMIT 1");
        if ($test_user) {
            echo "  Test user: {$test_user['username']} (Balance: USDT {$test_user['balance']})\n";
            echo "  ✅ Transaction processing logic available\n";
        }
    } else {
        echo "  ❌ ProcessTransaction procedure not found\n";
    }
    
    // 2. TEST DATABASE TRIGGERS
    echo "\n2. DATABASE TRIGGERS VALIDATION\n";
    echo "===============================\n";
    
    $triggers = $db->query("SHOW TRIGGERS")->fetchAll(PDO::FETCH_ASSOC);
    if (!empty($triggers)) {
        foreach ($triggers as $trigger) {
            echo "  ✅ Trigger: {$trigger['Trigger']} on {$trigger['Table']} ({$trigger['Event']})\n";
        }
    } else {
        echo "  ⚠️  No triggers found\n";
    }
    
    // 3. VALIDATE VIP LEVEL PROGRESSION LOGIC
    echo "\n3. VIP LEVEL PROGRESSION VALIDATION\n";
    echo "===================================\n";
    
    $vip_levels = fetchAll("SELECT * FROM vip_levels ORDER BY level");
    echo "VIP Level Structure:\n";
    foreach ($vip_levels as $vip) {
        echo "  Level {$vip['level']}: {$vip['name']}\n";
        echo "    - Min Balance: USDT {$vip['min_balance']}\n";
        echo "    - Max Daily Tasks: {$vip['max_daily_tasks']}\n";
        echo "    - Commission Multiplier: {$vip['commission_multiplier']}x\n";
        echo "    - Daily Withdrawal Limit: USDT {$vip['withdrawal_limit_daily']}\n";
        echo "    - Withdrawal Fee: {$vip['withdrawal_fee_percentage']}%\n\n";
    }
    
    // Validate progression logic
    echo "VIP Progression Logic Validation:\n";
    $users_with_vip = fetchAll("
        SELECT u.id, u.username, u.balance, u.vip_level, vl.min_balance as required_balance
        FROM users u 
        LEFT JOIN vip_levels vl ON u.vip_level = vl.level 
        WHERE u.status = 'active'
        ORDER BY u.vip_level DESC
    ");
    
    foreach ($users_with_vip as $user) {
        $balance_check = $user['balance'] >= $user['required_balance'] ? "✅" : "❌";
        echo "  {$balance_check} {$user['username']}: VIP {$user['vip_level']} (Balance: USDT {$user['balance']}, Required: USDT {$user['required_balance']})\n";
    }
    
    // 4. VALIDATE NEGATIVE SETTINGS FUNCTIONALITY
    echo "\n4. NEGATIVE SETTINGS VALIDATION\n";
    echo "===============================\n";
    
    $negative_settings = fetchAll("
        SELECT ns.*, u.username, p.name as product_name 
        FROM negative_settings ns
        LEFT JOIN users u ON ns.user_id = u.id
        LEFT JOIN products p ON ns.product_id_override = p.id
        ORDER BY ns.created_at DESC
    ");
    
    if (!empty($negative_settings)) {
        echo "Active Negative Settings:\n";
        foreach ($negative_settings as $setting) {
            $status = $setting['is_triggered'] ? "TRIGGERED" : "ACTIVE";
            echo "  {$status}: {$setting['username']} - Task #{$setting['trigger_task_number']}\n";
            echo "    Product: {$setting['product_name']}\n";
            echo "    Override Amount: USDT {$setting['override_amount']}\n";
            echo "    Created: {$setting['created_at']}\n\n";
        }
    } else {
        echo "  ℹ️  No negative settings configured\n";
    }
    
    // 5. VALIDATE REFERRAL SYSTEM
    echo "\n5. REFERRAL SYSTEM VALIDATION\n";
    echo "=============================\n";
    
    $referral_data = fetchAll("
        SELECT 
            u1.username as referrer,
            u1.referral_count,
            COUNT(u2.id) as actual_referrals
        FROM users u1
        LEFT JOIN users u2 ON u1.id = u2.invited_by
        WHERE u1.referral_count > 0
        GROUP BY u1.id, u1.username, u1.referral_count
    ");
    
    if (!empty($referral_data)) {
        echo "Referral System Integrity:\n";
        foreach ($referral_data as $ref) {
            $integrity_check = $ref['referral_count'] == $ref['actual_referrals'] ? "✅" : "❌";
            echo "  {$integrity_check} {$ref['referrer']}: Recorded={$ref['referral_count']}, Actual={$ref['actual_referrals']}\n";
        }
    } else {
        echo "  ℹ️  No referral relationships found\n";
    }
    
    // 6. VALIDATE TRANSACTION LOGGING ACCURACY
    echo "\n6. TRANSACTION LOGGING VALIDATION\n";
    echo "=================================\n";
    
    $transaction_summary = fetchRow("
        SELECT 
            COUNT(*) as total_transactions,
            SUM(CASE WHEN type IN ('deposit', 'commission', 'bonus') THEN amount ELSE 0 END) as total_credits,
            SUM(CASE WHEN type IN ('withdrawal', 'task_deduction') THEN amount ELSE 0 END) as total_debits,
            COUNT(CASE WHEN status = 'completed' THEN 1 END) as completed_transactions,
            COUNT(CASE WHEN status = 'pending' THEN 1 END) as pending_transactions
        FROM transactions
    ");
    
    echo "Transaction Summary:\n";
    echo "  Total Transactions: {$transaction_summary['total_transactions']}\n";
    echo "  Total Credits: USDT {$transaction_summary['total_credits']}\n";
    echo "  Total Debits: USDT {$transaction_summary['total_debits']}\n";
    echo "  Completed: {$transaction_summary['completed_transactions']}\n";
    echo "  Pending: {$transaction_summary['pending_transactions']}\n";
    
    // Balance reconciliation check
    echo "\nBalance Reconciliation Check:\n";
    $users_balance_check = fetchAll("
        SELECT 
            u.id,
            u.username,
            u.balance as current_balance,
            COALESCE(
                SUM(CASE WHEN t.type IN ('deposit', 'commission', 'bonus', 'referral_bonus') THEN t.amount ELSE 0 END) -
                SUM(CASE WHEN t.type IN ('withdrawal', 'task_deduction') THEN t.amount ELSE 0 END), 0
            ) as calculated_balance
        FROM users u
        LEFT JOIN transactions t ON u.id = t.user_id AND t.status = 'completed'
        WHERE u.status = 'active'
        GROUP BY u.id, u.username, u.balance
        HAVING ABS(current_balance - calculated_balance) > 0.01
    ");
    
    if (!empty($users_balance_check)) {
        echo "  ⚠️  Balance discrepancies found:\n";
        foreach ($users_balance_check as $user) {
            echo "    {$user['username']}: Current=USDT {$user['current_balance']}, Calculated=USDT {$user['calculated_balance']}\n";
        }
    } else {
        echo "  ✅ All user balances are accurate\n";
    }
    
    echo "\n✅ DATABASE INTEGRITY VALIDATION COMPLETED\n";
    
} catch (Exception $e) {
    echo "❌ Error during validation: " . $e->getMessage() . "\n";
}
?>
