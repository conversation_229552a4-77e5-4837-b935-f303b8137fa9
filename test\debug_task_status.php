<?php
/**
 * Debug Task Status - Check current task status for user
 */

define('BAMBOO_APP', true);
require_once 'includes/config.php';
require_once 'includes/functions.php';

// Start session
session_start();

// Check if user is logged in
if (!isLoggedIn()) {
    die("User not logged in");
}

$user_id = $_SESSION['user_id'];

echo "<h2>Task Status Debug for User ID: $user_id</h2>";

// Get current active task
$active_task = fetchRow("SELECT * FROM tasks WHERE user_id = ? AND status IN ('assigned', 'in_progress') ORDER BY assigned_at DESC LIMIT 1", [$user_id]);

if ($active_task) {
    echo "<h3>Active Task Found:</h3>";
    echo "<pre>";
    print_r($active_task);
    echo "</pre>";
    
    // Get product info
    $product = fetchRow("SELECT * FROM products WHERE id = ?", [$active_task['product_id']]);
    if ($product) {
        echo "<h3>Product Info:</h3>";
        echo "<pre>";
        print_r($product);
        echo "</pre>";
    }
} else {
    echo "<h3>No Active Task Found</h3>";
    
    // Check recent tasks
    $recent_tasks = fetchAll("SELECT * FROM tasks WHERE user_id = ? ORDER BY assigned_at DESC LIMIT 5", [$user_id]);
    echo "<h3>Recent Tasks:</h3>";
    echo "<pre>";
    print_r($recent_tasks);
    echo "</pre>";
}

// Get user info
$user = fetchRow("SELECT * FROM users WHERE id = ?", [$user_id]);
echo "<h3>User Info:</h3>";
echo "<pre>";
print_r($user);
echo "</pre>";
?>
