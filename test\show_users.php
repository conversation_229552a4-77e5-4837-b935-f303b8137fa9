<?php
define('BAMBOO_APP', true);
require_once __DIR__ . '/../includes/config.php';
require_once __DIR__ . '/../includes/functions.php';

echo "👥 Available Test Users\n";
echo "======================\n\n";

$users = fetchAll('SELECT id, username, phone, email, status, vip_level, balance FROM users WHERE status = "active" ORDER BY id LIMIT 10');

if ($users) {
    foreach($users as $u) {
        echo "🔹 User ID: {$u['id']}\n";
        echo "   Username: {$u['username']}\n";
        echo "   Phone: {$u['phone']}\n";
        echo "   Email: {$u['email']}\n";
        echo "   VIP Level: {$u['vip_level']}\n";
        echo "   Balance: {$u['balance']}\n";
        echo "   Status: {$u['status']}\n";
        echo "   Password: password (for sample users)\n";
        echo "\n";
    }
    
    echo "💡 Login Instructions:\n";
    echo "======================\n";
    echo "1. Go to: http://localhost/Bamboo/user/login/login.php\n";
    echo "2. Use any username from above\n";
    echo "3. Password: 'password' (for sample users)\n";
    echo "4. Should redirect to dashboard after successful login\n";
} else {
    echo "❌ No active users found\n";
}
?>
