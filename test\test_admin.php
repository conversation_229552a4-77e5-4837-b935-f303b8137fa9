<?php
try {
    $pdo = new PDO('mysql:host=localhost;port=3306;dbname=matchmaking;charset=utf8mb4', 'root', 'root');
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "=== ADMIN_USERS TABLE STRUCTURE ===\n";
    $stmt = $pdo->query('DESCRIBE admin_users');
    while($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
        echo $row['Field'] . ' - ' . $row['Type'] . "\n";
    }
    
    echo "\n=== ADMIN_USERS DATA ===\n";
    $stmt = $pdo->query('SELECT * FROM admin_users LIMIT 1');
    $admin = $stmt->fetch(PDO::FETCH_ASSOC);
    if($admin) {
        foreach($admin as $key => $value) {
            if($key === 'password' || $key === 'password_hash') {
                echo $key . ': ' . substr($value, 0, 20) . "...\n";
            } else {
                echo $key . ': ' . $value . "\n";
            }
        }
    }
    
    // Test common passwords
    $test_passwords = ['admin', 'password', '123456', 'admin123'];
    echo "\n=== PASSWORD TESTING ===\n";
    foreach($test_passwords as $test_pass) {
        if(isset($admin['password']) && password_verify($test_pass, $admin['password'])) {
            echo "✅ Password '{$test_pass}' works with 'password' column\n";
        }
        if(isset($admin['password_hash']) && password_verify($test_pass, $admin['password_hash'])) {
            echo "✅ Password '{$test_pass}' works with 'password_hash' column\n";
        }
    }
    
} catch(Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}
?>
