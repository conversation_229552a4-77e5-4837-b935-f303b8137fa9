<?php
/**
 * Test User Login Functionality
 */

define('BAMBOO_APP', true);
require_once __DIR__ . '/../includes/config.php';
require_once __DIR__ . '/../includes/functions.php';

// Start session
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

echo "🧪 Testing User Login Functionality\n";
echo "=====================================\n\n";

// Test credentials - using existing user from sample data
$test_username = 'alice';
$test_password = 'password'; // The sample data hash corresponds to 'password'

echo "1. Testing user lookup...\n";

// Test user lookup
$user = fetchRow(
    "SELECT id, username, password_hash, status, vip_level, balance FROM users WHERE username = ? OR phone = ?",
    [$test_username, $test_username]
);

if ($user) {
    echo "✅ User found: ID {$user['id']}, Username: {$user['username']}, Status: {$user['status']}\n";
    
    echo "\n2. Testing password verification...\n";
    
    // Test password verification
    if (verifyPassword($test_password, $user['password_hash'])) {
        echo "✅ Password verification successful\n";
        
        echo "\n3. Testing account status...\n";
        
        if ($user['status'] === 'active') {
            echo "✅ Account is active\n";
            
            echo "\n4. Testing session creation...\n";
            
            // Simulate successful login
            $_SESSION['user_id'] = $user['id'];
            $_SESSION['username'] = $user['username'];
            $_SESSION['user_type'] = 'user';
            $_SESSION['vip_level'] = $user['vip_level'];
            $_SESSION['last_activity'] = time();
            
            echo "✅ Session variables set:\n";
            echo "   - user_id: {$_SESSION['user_id']}\n";
            echo "   - username: {$_SESSION['username']}\n";
            echo "   - user_type: {$_SESSION['user_type']}\n";
            echo "   - vip_level: {$_SESSION['vip_level']}\n";
            
            echo "\n5. Testing isLoggedIn() function...\n";
            
            if (isLoggedIn()) {
                echo "✅ isLoggedIn() returns true\n";
                
                echo "\n6. Testing dashboard access...\n";
                
                // Test if we can fetch user data for dashboard
                $dashboard_user = fetchRow("SELECT * FROM users WHERE id = ?", [$user['id']]);
                if ($dashboard_user) {
                    echo "✅ Dashboard user data accessible\n";
                    echo "   - Balance: {$dashboard_user['balance']}\n";
                    echo "   - VIP Level: {$dashboard_user['vip_level']}\n";
                    echo "   - Credit Score: " . ($dashboard_user['credit_score'] ?? 'Not set') . "\n";
                    
                    echo "\n🎉 LOGIN TEST SUCCESSFUL!\n";
                    echo "=====================================\n";
                    echo "✅ All login components working correctly\n";
                    echo "✅ User can successfully log in with:\n";
                    echo "   Username: $test_username\n";
                    echo "   Password: $test_password\n";
                    echo "\n🔗 Test the login at: http://localhost/Bamboo/user/login/login.php\n";
                    
                } else {
                    echo "❌ Failed to fetch dashboard user data\n";
                }
            } else {
                echo "❌ isLoggedIn() returns false\n";
            }
        } else {
            echo "❌ Account is not active (Status: {$user['status']})\n";
        }
    } else {
        echo "❌ Password verification failed\n";
        echo "   Expected password: $test_password\n";
        echo "   Stored hash: {$user['password_hash']}\n";
    }
} else {
    echo "❌ User not found with username: $test_username\n";
    
    // Let's check what users exist
    echo "\nChecking existing users...\n";
    $users = fetchAll("SELECT id, username, phone, email, status FROM users LIMIT 5");
    if ($users) {
        echo "Available users:\n";
        foreach ($users as $u) {
            echo "   - ID: {$u['id']}, Username: {$u['username']}, Phone: {$u['phone']}, Status: {$u['status']}\n";
        }
    } else {
        echo "No users found in database\n";
    }
}

// Clean up session
session_destroy();
echo "\n🧹 Session cleaned up\n";
?>
