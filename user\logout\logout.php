<?php
/**
 * Bamboo Web Application - User Logout
 * Company: Notepadsly
 * Version: 1.0
 */

// Define app constant
define('BAMBOO_APP', true);

// Include required files
require_once '../../includes/config.php';
require_once '../../includes/functions.php';

// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Check if user is logged in
if (!isLoggedIn()) {
    redirect('user/login/login.php');
}

// Get user ID before destroying session
$user_id = $_SESSION['user_id'] ?? null;

// Clear remember me cookie if it exists
if (isset($_COOKIE['remember_token'])) {
    setcookie('remember_token', '', time() - 3600, '/');
    
    // Clear remember token from database
    if ($user_id) {
        updateRecord('users', ['remember_token' => null], 'id = ?', [$user_id]);
    }
}

// Delete session record from database
if ($user_id) {
    executeQuery("DELETE FROM user_sessions WHERE user_id = ? AND session_id = ?", [$user_id, session_id()]);
}

// Destroy session
session_destroy();

// Clear session cookie
if (ini_get("session.use_cookies")) {
    $params = session_get_cookie_params();
    setcookie(session_name(), '', time() - 42000,
        $params["path"], $params["domain"],
        $params["secure"], $params["httponly"]
    );
}

// Set success message for login page
session_start();
showSuccess('You have been successfully logged out.');

// Redirect to login page
redirect('user/login/login.php');
?>
